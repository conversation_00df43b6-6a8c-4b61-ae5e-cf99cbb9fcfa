name: Deploy Wallet & Admin via ECR

on:
  push:
    branches: [production]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region us-east-2 | \
          docker login --username AWS --password-stdin 744600717187.dkr.ecr.us-east-2.amazonaws.com

      - name: Build and Push Wallet Image
        run: |
          WALLET_REPO="744600717187.dkr.ecr.us-east-2.amazonaws.com/wallet"
          docker build -t wallet ./wallet
          docker tag wallet:latest "$WALLET_REPO:latest"
          docker push "$WALLET_REPO:latest"

      - name: Build and Push Admin Image
        run: |
          ADMIN_REPO="744600717187.dkr.ecr.us-east-2.amazonaws.com/admin"
          docker build -t admin ./admin
          docker tag admin:latest "$ADMIN_REPO:latest"
          docker push "$ADMIN_REPO:latest"

      - name: Trigger Deploy Webhook
        run: |
          curl -X POST https://deployer.muda.tech/agg-deploy-masterS \
            -H "Authorization: Bearer ${{ secrets.DEPLOY_TOKENOLD}}"

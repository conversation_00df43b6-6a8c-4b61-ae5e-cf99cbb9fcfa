name: Monorepo Health Check - Block Push

on:
  push:
    branches: [main, master, production, stage]
  pull_request:
    branches: [main, master, production, stage]

jobs:
  health-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies for all services
        run: |
          echo "🔍 Installing dependencies for all services..."
          
          # Install for each service if package.json exists
          if [ -f "wallet/package.json" ]; then
            cd wallet && npm install && cd ..
          fi
          
          if [ -f "admin/package.json" ]; then
            cd admin && npm install && cd ..
          fi
          
          if [ -f "gateway/package.json" ]; then
            cd gateway && npm install && cd ..
          fi
          
          if [ -f "liquidityRailAdmin/package.json" ]; then
            cd liquidityRailAdmin && npm install && cd ..
          fi
          
          if [ -f "stellarService/package.json" ]; then
            cd stellarService && npm install && cd ..
          fi
          
          if [ -f "idp/package.json" ]; then
            cd idp && npm install && cd ..
          fi

      - name: Check TypeScript compilation for all services
        run: |
          echo "🔍 Checking TypeScript compilation for all services..."
          
          # Check wallet service
          if [ -f "wallet/tsconfig.json" ]; then
            echo "Checking wallet service..."
            cd wallet
            npx tsc --noEmit || (echo "❌ Wallet TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Check admin service
          if [ -f "admin/tsconfig.json" ]; then
            echo "Checking admin service..."
            cd admin
            npx tsc --noEmit || (echo "❌ Admin TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Check gateway service
          if [ -f "gateway/tsconfig.json" ]; then
            echo "Checking gateway service..."
            cd gateway
            npx tsc --noEmit || (echo "❌ Gateway TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Check liquidityRailAdmin service
          if [ -f "liquidityRailAdmin/tsconfig.json" ]; then
            echo "Checking liquidityRailAdmin service..."
            cd liquidityRailAdmin
            npx tsc --noEmit || (echo "❌ LiquidityRailAdmin TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Check stellarService
          if [ -f "stellarService/tsconfig.json" ]; then
            echo "Checking stellarService..."
            cd stellarService
            npx tsc --noEmit || (echo "❌ StellarService TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Check idp service
          if [ -f "idp/tsconfig.json" ]; then
            echo "Checking idp service..."
            cd idp
            npx tsc --noEmit || (echo "❌ IDP TypeScript compilation failed - push blocked!" && exit 1)
            cd ..
          fi
          
          echo "✅ All services TypeScript compilation passed!"

      - name: Run available tests
        run: |
          echo "🔍 Running available tests..."
          
          # Run wallet tests if they exist
          if [ -f "wallet/package.json" ] && grep -q '"test"' wallet/package.json; then
            echo "Running wallet tests..."
            cd wallet
            npm test || (echo "❌ Wallet tests failed - push blocked!" && exit 1)
            cd ..
          fi
          
          # Run admin tests if they exist
          if [ -f "admin/package.json" ] && grep -q '"test"' admin/package.json; then
            echo "Running admin tests..."
            cd admin
            npm test || (echo "❌ Admin tests failed - push blocked!" && exit 1)
            cd ..
          fi
          
          echo "✅ All available tests passed!" 
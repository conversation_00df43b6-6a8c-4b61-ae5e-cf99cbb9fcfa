#!/bin/bash
set -e

echo "🔍 Checking critical TypeScript errors across all services..."

SERVICES=("wallet" "admin" "gateway" "liquidityRailAdmin" "stellarService" "idp")
CRITICAL_ERRORS_FOUND=false

for service in "${SERVICES[@]}"; do
    if [ -f "$service/tsconfig.json" ]; then
        echo "Checking $service..."
        
        cd "$service"
        
        # Run TypeScript check and capture output
        if ! OUTPUT=$(NODE_OPTIONS="--max-old-space-size=4096" npx tsc --noEmit --skipLibCheck 2>&1); then
            # Filter for only critical errors (missing modules, wrong method calls)
            CRITICAL_ERRORS=$(echo "$OUTPUT" | grep -E "(Cannot find module|Property .* does not exist on type|Cannot invoke an expression)" || true)
            
            if [ -n "$CRITICAL_ERRORS" ]; then
                echo "❌ Critical errors found in $service:"
                echo "$CRITICAL_ERRORS"
                CRITICAL_ERRORS_FOUND=true
            else
                echo "⚠️ $service has minor type issues (ignoring)"
            fi
        else
            echo "✅ $service TypeScript check passed"
        fi
        
        cd ..
    else
        echo "⏭️ Skipping $service (no tsconfig.json)"
    fi
done

if [ "$CRITICAL_ERRORS_FOUND" = true ]; then
    echo ""
    echo "🚫 Push blocked due to critical TypeScript errors!"
    echo "💡 Fix missing modules and method call errors before pushing"
    exit 1
else
    echo ""
    echo "✅ All critical TypeScript checks passed!"
    echo "�� Push allowed"
fi 
{"info": {"_postman_id": "stellar-trading-api-collection", "name": "Stellar Trading API", "description": "Complete collection for Stellar trading endpoints - create offers, take offers, swaps, order book, and trading history", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3005", "type": "string"}, {"key": "client_id", "value": "client123", "type": "string"}, {"key": "password", "value": "user_password", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/trading/health", "host": ["{{base_url}}"], "path": ["trading", "health"]}, "description": "Check if the trading service is running and healthy"}, "response": []}, {"name": "Create Sell Order (UGX → USDC)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": \"{{client_id}}\",\n  \"sellingAsset\": \"UGX\",\n  \"sellingIssuer\": \"GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR\",\n  \"buyingAsset\": \"USDC\",\n  \"buyingIssuer\": \"GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN\",\n  \"amount\": \"1000\",\n  \"price\": \"0.0003\",\n  \"orderType\": \"sell\",\n  \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/trading/offers", "host": ["{{base_url}}"], "path": ["trading", "offers"]}, "description": "Create a sell order to sell 1000 UGX for USDC at 0.0003 price"}, "response": []}, {"name": "Create Buy Order (USDC → UGX)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": \"{{client_id}}\",\n  \"sellingAsset\": \"USDC\",\n  \"sellingIssuer\": \"GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN\",\n  \"buyingAsset\": \"UGX\",\n  \"buyingIssuer\": \"GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR\",\n  \"amount\": \"0.5\",\n  \"price\": \"3000\",\n  \"orderType\": \"buy\",\n  \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/trading/offers", "host": ["{{base_url}}"], "path": ["trading", "offers"]}, "description": "Create a buy order to buy UGX with 0.5 USDC at 3000 price"}, "response": []}, {"name": "Get Order Book (UGX/USDC)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/trading/orderbook?sellingAsset=UGX&buyingAsset=USDC&sellingIssuer=GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR&buyingIssuer=GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN", "host": ["{{base_url}}"], "path": ["trading", "orderbook"], "query": [{"key": "sellingAsset", "value": "UGX"}, {"key": "buyingAsset", "value": "USDC"}, {"key": "sellingIssuer", "value": "GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR"}, {"key": "buyingIssuer", "value": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN"}]}, "description": "Get the order book for UGX/USDC trading pair"}, "response": []}, {"name": "Take Offer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": \"client456\",\n  \"orderId\": \"ORD_12345\",\n  \"password\": \"{{password}}\",\n  \"takeAmount\": \"500\"\n}"}, "url": {"raw": "{{base_url}}/trading/offers/take", "host": ["{{base_url}}"], "path": ["trading", "offers", "take"]}, "description": "Take an existing offer. Replace ORD_12345 with actual order ID from previous requests."}, "response": []}, {"name": "Execute Swap (UGX → USDC)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": \"{{client_id}}\",\n  \"sourceAsset\": \"UGX\",\n  \"sourceIssuer\": \"GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR\",\n  \"destinationAsset\": \"USDC\",\n  \"destinationIssuer\": \"GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN\",\n  \"sourceAmount\": \"1000\",\n  \"minDestAmount\": \"0.25\",\n  \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/trading/swap", "host": ["{{base_url}}"], "path": ["trading", "swap"]}, "description": "Execute an atomic swap from 1000 UGX to minimum 0.25 USDC using Stellar path payments"}, "response": []}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"clientId\": \"{{client_id}}\",\n  \"orderId\": \"ORD_12345\",\n  \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{base_url}}/trading/offers/cancel", "host": ["{{base_url}}"], "path": ["trading", "offers", "cancel"]}, "description": "Cancel an active order. Replace ORD_12345 with actual order ID from previous requests."}, "response": []}, {"name": "Get Trading History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/trading/clients/{{client_id}}/history", "host": ["{{base_url}}"], "path": ["trading", "clients", "{{client_id}}", "history"]}, "description": "Get complete trading history (orders, trades, swaps) for the client"}, "response": []}, {"name": "Get Client Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/trading/clients/{{client_id}}/orders?status=active", "host": ["{{base_url}}"], "path": ["trading", "clients", "{{client_id}}", "orders"], "query": [{"key": "status", "value": "active", "description": "Filter by order status: active, completed, cancelled, partially_filled"}]}, "description": "Get client's orders filtered by status"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate timestamp for testing", "pm.globals.set('timestamp', Date.now());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is valid', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 404, 500]);", "});", "", "pm.test('Response has status field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "});", "", "pm.test('Response has message field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "});", "", "// Store order ID from create order responses", "if (pm.response.code === 200 && pm.request.url.path.includes('offers') && pm.request.method === 'POST') {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.orderId) {", "        pm.globals.set('last_order_id', jsonData.data.orderId);", "        console.log('Stored order ID:', jsonData.data.orderId);", "    }", "}"]}}]}
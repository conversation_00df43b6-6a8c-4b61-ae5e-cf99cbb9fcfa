# Tembo Integration Environment Configuration
#
# Essential environment variables for Tembo API integration.
# Copy this file to .env and fill in your actual credentials.
# Tests will FAIL if these are not properly configured.

# =============================================================================
# TEMBO API CREDENTIALS (REQUIRED)
# =============================================================================

# Tembo API Base URL
TEMBO_API_URL=https://api.temboplus.com

# Tembo Authentication Token (Get from Tembo dashboard)
TEMBO_AUTH_TOKEN=your_sandbox_auth_token_here

# Tembo Account ID (Your account identifier)
TEMBO_ACCOUNT_ID=your_account_id_here

# Tembo Secret Key (For API authentication and webhook verification)
TEMBO_SECRET_KEY=your_secret_key_here

# Tembo Main Account Number (For payouts and bank transfers)
TEMBO_MAIN_ACCOUNT_NO=your_main_account_number_here

# Base URL for webhook callbacks
WEBHOOK_BASE_URL=http://localhost:3000
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"  // Changed from postgresql to mysql based on your existing setup
  url      = env("DATABASE_URL")
}

// Client management
model Client {
  id              Int      @id @default(autoincrement())
  client_id       String   @unique
  name            String?
  contact_email   String?
  business_name   String?
  session_id      String?
  is_active       <PERSON><PERSON>an  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  wallets         ClientWallet[]
  transactions    Transaction[]
  apiLogs         ApiLog[]
  webhooks        Webhook[]

  @@map("clients")
}

// Client wallets/API keys
model ClientWallet {
  id              Int      @id @default(autoincrement())
  client_id       String
  public_key      String
  secret_key      String   // Encrypted
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  client          Client   @relation(fields: [client_id], references: [client_id])

  @@map("client_wallets")
}

// Products/Services
model Product {
  id              Int      @id @default(autoincrement())
  product_id      String   @unique
  product_name    String
  product_code    String
  currency        String
  transaction_type String  // PUSH, PULL
  has_c_account   String   @default("no")
  min_amount      Decimal  @default(0)
  max_amount      Decimal  @default(*********)
  fee_type        String   @default("FIXED") // FIXED, PERCENTAGE
  fee_amount      Decimal  @default(0)
  provider_fee    Decimal  @default(0)
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  transactions    Transaction[]

  @@map("products")
}

// Main transactions table
model Transaction {
  id                Int      @id @default(autoincrement())
  trans_id          String   @unique
  reference_id      String?
  validation_id     String?
  client_id         String
  product_id        String?
  trans_type        String   // PUSH, PULL
  amount            Decimal
  req_amount        Decimal?
  asset_code        String
  fee               Decimal  @default(0)
  provider_fees     Decimal  @default(0)
  currency          String
  service_name      String?
  sender_account    String?
  receiver_account  String?
  memo              String?
  status            String   @default("PENDING") // SUCCESS, PENDING, FAILED, etc.
  system_status     String?
  payment_method_id String?
  running_balance   Decimal?
  SessionId         String?
  ext_reference     String?
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  // Relations
  client            Client?  @relation(fields: [client_id], references: [client_id])
  product           Product? @relation(fields: [product_id], references: [product_id])
  logs              TransactionLog[]
  scTransactions    SCTransaction[]

  @@map("transactions")
}

// Transaction logging
model TransactionLog {
  id              Int      @id @default(autoincrement())
  trans_id        String
  status          String
  step            String   // Using Steps enum values
  response_code   Int
  description     String?
  data            Json?
  created_at      DateTime @default(now())

  // Relations
  transaction     Transaction @relation(fields: [trans_id], references: [trans_id])

  @@map("transactions_log")
}

// Stable coin transactions (Utilia)
model SCTransaction {
  id              Int      @id @default(autoincrement())
  clientId        String
  refId           String   // References transaction trans_id
  hash            String?
  direction       String   // INCOMING, OUTGOING
  state           String   // PENDING, CONFIRMED, FAILED
  network         String
  asset           String
  asset_id        String?
  amount          Decimal
  source          String?
  destination     String?
  createTime      DateTime?
  confirmTime     DateTime?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  transaction     Transaction? @relation(fields: [refId], references: [trans_id])

  @@map("sc_transactions")
}

// Third party integration logs
model ThirdPartyLog {
  id              Int      @id @default(autoincrement())
  trans_id        String
  service_name    String   // MOBILE_MONEY, BANK_TRANSFER, etc.
  log_type        String   // REQUEST, RESPONSE, ERROR
  request_type    String   // PUSH, PULL, VALIDATION, STATUS
  request_data    Json?
  error_message   String?
  created_at      DateTime @default(now())

  @@map("third_party_logs")
}

// Webhook management
model Webhook {
  id              Int      @id @default(autoincrement())
  client_id       String
  callback_url    String
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Relations
  client          Client   @relation(fields: [client_id], references: [client_id])

  @@map("webhooks")
}

// Webhook logs
model WebhookLog {
  id              Int      @id @default(autoincrement())
  cl_id           String?
  client_id       String
  trans_id        String
  webhook_url     String
  event           String
  webhook_data    Json?
  status_code     Int?
  response        Json?
  response_code   Int?
  timestamp       DateTime
  direction       String   @default("OUTGOING")
  provider        String   @default("MUDA")
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("webhook_logs")
}

// Utilia assets
model UtiliaAsset {
  id                 Int      @id @default(autoincrement())
  asset             String   @unique
  asset_id          String?
  asset_code        String
  symbol            String?
  network           String?
  is_muda_supported Boolean  @default(false)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  @@map("utilia_assets")
}

// Utilia networks
model UtiliaNetwork {
  id                 Int      @id @default(autoincrement())
  name              String   @unique
  testnet           Boolean  @default(false)
  is_muda_supported Boolean  @default(false)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt

  @@map("utilia_networks")
}

// API call logs
model ApiLog {
  id              Int      @id @default(autoincrement())
  client_id       String
  user_id         String?
  body            Json?
  ip              String?
  created_at      DateTime @default(now())

  // Relations
  client          Client   @relation(fields: [client_id], references: [client_id])

  @@map("api_logs")
}

// User OTP management
model UserOtp {
  id              Int      @id @default(autoincrement())
  user_id         String
  email           String
  otp             String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@unique([email, user_id])
  @@map("user_otp")
}

// 2FA management
model User2FA {
  id              Int      @id @default(autoincrement())
  user_id         String
  user_type       String   // admin, client
  secret          String
  deleted_at      DateTime?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("user_2fa")
}

// IP whitelist
model IpWhitelist {
  id              Int      @id @default(autoincrement())
  client_id       String
  ip_address      String
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("ip_whitelist")
}

// Crypto deposit addresses
model CryptoDeposit {
  id              Int      @id @default(autoincrement())
  client_id       String
  deposit_address String
  network         String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("dp_crypto_deposits")
}

// Bank accounts for fiat deposits
model Bank {
  id              Int      @id @default(autoincrement())
  currency        String
  account_name    String?
  account_number  String?
  bank_name       String?
  bank_code       String?
  reference_code  String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("banks")
}

// Payment methods
model PaymentMethod {
  id              Int      @id @default(autoincrement())
  client_id       String?
  method_type     String   // BANK_TRANSFER, MOBILE_MONEY, etc.
  account_name    String?
  account_number  String?
  bank_name       String?
  currency        String?
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("payment_methods")
}

// Third party accounts/configs
model ThirdPartyAccount {
  id              Int      @id @default(autoincrement())
  service_name    String
  account_name    String?
  primary_email   String?
  secondary_email String?
  api_key         String?
  api_secret      String?
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("third_party_accounts")
}

// Notification templates
model NotificationTemplate {
  id              Int      @id @default(autoincrement())
  operation       String   @unique
  channel         String   // EMAIL, SMS, PUSH
  title           String
  body            String   @db.Text
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  @@map("notification_templates")
}

// knexfile.ts
import dotenv from 'dotenv';
dotenv.config();

export default {
    development: {
      client: 'mysql2',
      connection: {
        host: process.env.HOST_NAME,
        user:  process.env.USER_NAME,
        password:  process.env.PASSWORD,
        database: process.env.DBNAME
      },
      migrations: {
        tableName: 'knex_migrations',
        directory: './migrations'
      },
      seeds: {
        directory: './seeds'
      }
    },
    production: {
      // Your production config
      client: 'mysql2',
      connection: {
        host: process.env.HOST_NAME,
        user:  process.env.USER_NAME,
        password:  process.env.PASSWORD,
        database: process.env.DBNAME
      },
      migrations: {
        tableName: 'knex_migrations',
        directory: './migrations'
      },
      seeds: {
        directory: './seeds'
      }
    }
};

import { SweeperService } from '../services/sweeper.service';

/**
 * Sweeper Cron Job
 * This file demonstrates how to schedule automated sweep operations
 */

const sweeper = new SweeperService();

/**
 * Daily sweep job - runs every day at 2 AM
 * This is a safe, automated sweep with conservative settings
 */
export async function dailySweepJob() {
  console.log('🌅 Starting daily sweep job...');
  
  try {
    const result = await sweeper.runAutomatedSweep({
      minAmount: '10', // Only sweep amounts above 10
      excludeAssets: [
        'native.ethereum/ETH', // Keep ETH for gas fees
        'native.bsc/BNB',      // Keep BNB for gas fees
        'native.polygon/MATIC' // Keep MATIC for gas fees
      ],
      includeAssets: [
        'assets/ethereum/USDC',
        'assets/ethereum/USDT',
        'assets/bsc/USDC',
        'assets/bsc/USDT',
        'assets/polygon/USDC',
        'assets/polygon/USDT'
      ],
      dryRun: false, // Set to true for testing
      memo: 'Daily automated sweep'
    });

    console.log('📊 Daily sweep completed:', {
      success: result.success,
      totalWallets: result.summary.totalWallets,
      successfulSweeps: result.summary.successfulSweeps,
      totalAmount: result.summary.totalAmount
    });

    return result;
  } catch (error) {
    console.error('❌ Daily sweep job failed:', error);
    throw error;
  }
}

/**
 * Weekly deep sweep job - runs every Sunday at 3 AM
 * This is a more aggressive sweep that includes smaller amounts
 */
export async function weeklyDeepSweepJob() {
  console.log('📅 Starting weekly deep sweep job...');
  
  try {
    const result = await sweeper.runAutomatedSweep({
      minAmount: '1', // Sweep amounts above 1 (more aggressive)
      excludeAssets: [
        'native.ethereum/ETH', // Keep ETH for gas fees
        'native.bsc/BNB',      // Keep BNB for gas fees
        'native.polygon/MATIC' // Keep MATIC for gas fees
      ],
      dryRun: false, // Set to true for testing
      memo: 'Weekly deep sweep'
    });

    console.log('📊 Weekly deep sweep completed:', {
      success: result.success,
      totalWallets: result.summary.totalWallets,
      successfulSweeps: result.summary.successfulSweeps,
      totalAmount: result.summary.totalAmount
    });

    return result;
  } catch (error) {
    console.error('❌ Weekly deep sweep job failed:', error);
    throw error;
  }
}

/**
 * Emergency sweep job - can be triggered manually
 * This sweeps everything except gas tokens
 */
export async function emergencySweepJob() {
  console.log('🚨 Starting emergency sweep job...');
  
  try {
    const result = await sweeper.runAutomatedSweep({
      minAmount: '0.1', // Very low threshold for emergency
      excludeAssets: [
        'native.ethereum/ETH',
        'native.bsc/BNB',
        'native.polygon/MATIC',
        'native.base/ETH',
        'native.arbitrum/ETH'
      ],
      dryRun: false, // Set to true for testing
      memo: 'Emergency sweep operation'
    });

    console.log('📊 Emergency sweep completed:', {
      success: result.success,
      totalWallets: result.summary.totalWallets,
      successfulSweeps: result.summary.successfulSweeps,
      totalAmount: result.summary.totalAmount
    });

    return result;
  } catch (error) {
    console.error('❌ Emergency sweep job failed:', error);
    throw error;
  }
}

/**
 * Test sweep job - for development and testing
 * Always runs in dry-run mode for safety
 */
export async function testSweepJob() {
  console.log('🧪 Starting test sweep job...');
  
  try {
    const result = await sweeper.runAutomatedSweep({
      minAmount: '5',
      excludeAssets: ['native.ethereum/ETH'],
      dryRun: true, // Always true for testing
      memo: 'Test sweep operation'
    });

    console.log('📊 Test sweep completed:', {
      success: result.success,
      totalWallets: result.summary.totalWallets,
      successfulSweeps: result.summary.successfulSweeps,
      totalAmount: result.summary.totalAmount
    });

    return result;
  } catch (error) {
    console.error('❌ Test sweep job failed:', error);
    throw error;
  }
}

// Example cron schedule (using node-cron or similar):
/*
import cron from 'node-cron';

// Daily sweep at 2 AM
cron.schedule('0 2 * * *', dailySweepJob);

// Weekly deep sweep every Sunday at 3 AM
cron.schedule('0 3 * * 0', weeklyDeepSweepJob);

// Test sweep every hour (for development)
cron.schedule('0 * * * *', testSweepJob);
*/ 
import speakeasy from "speakeasy";
import QRCode from "qrcode";
import * as dotenv from "dotenv";
dotenv.config();

class TwoFactorAuthHelper {
    private appName: any;
    private mainSecret: speakeasy.GeneratedSecret;

    constructor() {
        this.appName = process.env.APPNAME;
        this.mainSecret = speakeasy.generateSecret({ name: 'Payment.Muda:userAuth', length: 20 });
    }

    public generateSecret = async (userSecret: any = null ) => {
        try {

            let user2faObject: any ;
            if (!this.mainSecret.otpauth_url) {
                throw new Error("OTP Auth URL is undefined");
            }

            if(userSecret !== null){
              this.mainSecret = speakeasy.generateSecret({ name: `Muda.${userSecret?.user_type ?? "acc"}:${userSecret?.clientEmail ?? ""}`, length: 20 });
            }
            user2faObject = this.mainSecret;
            const qrCodeDataURL = await QRCode.toDataURL(user2faObject.otpauth_url);
            return {
                        status:  true,
                        secret:  this.mainSecret.base32,
                        data:    user2faObject,
                        qrCode:  qrCodeDataURL,
                        message: "2FA token generated successfully",
                    };


        } catch (error) {
            console.error("Error generating 2FA QR code:", error);
            return { status: false, message: "Error generating 2FA Code" };
        }
    };

    public verifySecret = (secret: string, token: string) => {
        try {

            const verified = speakeasy.totp.verify({
                secret,
                encoding: "base32",
                token,
                window: 1,
            });
            return {
                status: verified,
                message: verified ? "2FA Verified!" : "Invalid 2FA Code!",
            };

        } catch (error) {

            console.error("Error verifying 2FA token:", error);
            return { status: false, message: "Error verifying 2FA Code!" };
        }
    };
}

export default new TwoFactorAuthHelper();

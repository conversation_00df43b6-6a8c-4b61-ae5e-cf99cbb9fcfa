/**
 * Wallet Configuration Helper
 * Centralizes wallet ID management using environment variables
 */

export interface WalletConfig {
  gas: string;
  cold: string;
  payout: string;
  [key: string]: string;
}

export class WalletConfigHelper {
  private static instance: WalletConfigHelper;
  private config: WalletConfig;

  private constructor() {
    this.config = {
      gas: process.env.GAS_WALLET_ID || 'cea08904a71d',
      cold: process.env.COLD_WALLET_ID || '9a185f915491',
      payout: process.env.PAYOUT_WALLET_ID || '156acae8b285',
    };
  }

  public static getInstance(): WalletConfigHelper {
    if (!WalletConfigHelper.instance) {
      WalletConfigHelper.instance = new WalletConfigHelper();
    }
    return WalletConfigHelper.instance;
  }

  public getWalletId(type: keyof WalletConfig): string {
    return this.config[type];
  }

  public getAllWalletIds(): WalletConfig {
    return { ...this.config };
  }

  public getGasWalletId(): string {
    return this.config.gas;
  }

  public getColdWalletId(): string {
    return this.config.cold;
  }

  public getPayoutWalletId(): string {
    return this.config.payout;
  }

  /**
   * Get all wallet IDs as an array for easy iteration
   */
  public getWalletIdsArray(): Array<{ type: string; id: string }> {
    return Object.entries(this.config).map(([type, id]) => ({ type, id }));
  }

  /**
   * Validate that all required wallet IDs are configured
   */
  public validateConfig(): { valid: boolean; missing: string[] } {
    const missing: string[] = [];
    
    Object.entries(this.config).forEach(([type, id]) => {
      if (!id || id === '') {
        missing.push(type);
      }
    });

    return {
      valid: missing.length === 0,
      missing
    };
  }
}

// Convenience functions
export const getWalletId = (type: keyof WalletConfig): string => {
  return WalletConfigHelper.getInstance().getWalletId(type);
};

export const getGasWalletId = (): string => {
  return WalletConfigHelper.getInstance().getGasWalletId();
};

export const getColdWalletId = (): string => {
  return WalletConfigHelper.getInstance().getColdWalletId();
};

export const getPayoutWalletId = (): string => {
  return WalletConfigHelper.getInstance().getPayoutWalletId();
};

export const getAllWalletIds = (): WalletConfig => {
  return WalletConfigHelper.getInstance().getAllWalletIds();
}; 
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from 'dotenv';
config();

const JWT_SECRET: any = process.env.JWT_SECRET;

export class JWTMiddleware {
    static verifyTokenAccess(req: Request, res: Response, next: NextFunction) {
        const authHeader = req.headers.authorization;
        if (!authHeader) return res.status(401).json({ message: "No authorization header provided." });

        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).json({ message: "Token format is 'Bearer <token>'." });
        }

        const token = parts[1];
        jwt.verify(token, JWT_SECRET, (err: any, decoded: any) => {
            if (err) return res.status(401).json({ message: "Unauthorized Access Token." });

            // if (decoded.type !== "refresh") {
            //     return res.status(401).json({ message: "Invalid refresh token type." });
            // }
            console.log("decodedJWT", decoded);
            req.body.apiKey      = decoded?.apiKey || "";
            req.body.clientId    = decoded?.clientId || "";
            req.body.clientEmail = decoded?.email || "";
            req.body.role        = decoded?.role || "";
            req.body.userId      = decoded?.userId || "";
            next();
        });
    }

    static verifyAdminToken(req: Request, res: Response, next: NextFunction) {
        
        const authHeader = req.headers.authorization;
        if (!authHeader) return res.status(401).json({ message: "No authorization header provided." });

        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).json({ message: "Token format is 'Bearer <token>'." });
        }

        const token = parts[1];
        jwt.verify(token, JWT_SECRET, (err: any, decoded: any) => {

            console.log("JWT_SECRET -", JWT_SECRET);
            if (err) return res.status(401).json({ message: "Unauthorized Access Token." });

            req.body.clientId    = decoded?.adminId || "";
            req.body.clientEmail = decoded?.email || "";
            req.body.apiKey = decoded?.apiKey || "";
            req.body.role        = decoded?.role || "";
            req.body.userId      = decoded?.adminId || "";
            next();
        });
    }
}

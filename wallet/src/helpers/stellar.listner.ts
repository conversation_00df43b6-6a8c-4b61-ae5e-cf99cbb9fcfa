import StellarSdk from "stellar-sdk";
import dotenv from "dotenv";
import Transactions from "../models/transactions";

dotenv.config();

const HORIZON_URL = process.env.HORIZON_URL || "https://horizon.stellar.org";
const server = new StellarSdk.Server(HORIZON_URL);

class StellarListener {
    private issuerPublicKey: string;
    private transactions: Transactions;

    constructor() {
        this.issuerPublicKey = process.env.STELLAR_ISSUER_PUBLIC || "";
        this.transactions = new Transactions();
        console.log(`Listener:::`, this.issuerPublicKey )
    }

    // ✅ Start Listening to Issuer Account Transactions
    async startListening() {
        console.log(`🔹 Listening for burns on issuer account: ${this.issuerPublicKey}`);

        server.payments()
            .forAccount(this.issuerPublicKey)
            .cursor("now")
            .stream({
                onmessage: async (payment: any) => {
                    if (payment.type === "payment") {
                        await this.processBurnTransaction(payment);
                    }
                },
                onerror: (error: any) => {
                    console.error("Error streaming payments:", error);
                }
            });
    }

    // ✅ Process Burn Transactions
    async processBurnTransaction(payment: any) {
        try {
            // ✅ Check if the transaction is a burn (payment to issuer)
            if (payment.to !== this.issuerPublicKey) {
                return;
            }

            console.log(`🔥 Burn Detected: ${payment.id}`);

            const sender: string = payment.from;
            const amount: string = payment.amount;
            const asset_code: string = payment.asset_code || "UGX";
            const memo: string = payment.memo || "";

            // 🔎 Find the pending transaction using `memo`
      //      const transaction = await this.transactions.completeTransaction(payment.id, memo);
        //    if (transaction.status !== 200) {
        //        console.warn(`⚠️ No matching pending transaction for Memo: ${memo}`);
        //        return;
        //    }

            console.log(`✅ Transaction Completed: ${payment.id}`);

        } catch (error) {
            console.error("Error processing burn transaction:", error);
        }
    }
}

export default  StellarListener;

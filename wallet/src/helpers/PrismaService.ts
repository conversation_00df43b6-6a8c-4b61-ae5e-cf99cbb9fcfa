import { PrismaClient } from '@prisma/client';
import { Steps } from './model';

/**
 * PrismaService class - Modern ORM-based database service
 * This class provides Prisma ORM functionality alongside the existing Model class
 */
export class PrismaService {
    private prisma: PrismaClient;

    constructor() {
        this.prisma = new PrismaClient({
            log: ['query', 'info', 'warn', 'error'],
        });
    }

    /**
     * Get Prisma client instance for direct usage
     */
    getClient() {
        return this.prisma;
    }

    /**
     * Close Prisma connection
     */
    async disconnect() {
        await this.prisma.$disconnect();
    }

    // ===== TRANSACTION OPERATIONS =====

    /**
     * Create a new transaction using Prisma
     */
    async createTransactionPrisma(data: {
        trans_id: string;
        reference_id?: string;
        validation_id?: string;
        client_id: string;
        product_id?: string;
        trans_type: string;
        amount: number;
        req_amount?: number;
        asset_code: string;
        fee?: number;
        provider_fees?: number;
        currency: string;
        service_name?: string;
        sender_account?: string;
        receiver_account?: string;
        memo?: string;
        status?: string;
        payment_method_id?: string;
        SessionId?: string;
    }) {
        try {
            const transaction = await this.prisma.transaction.create({
                data: {
                    ...data,
                    amount: data.amount,
                    req_amount: data.req_amount || data.amount,
                    fee: data.fee || 0,
                    provider_fees: data.provider_fees || 0,
                    status: data.status || "PENDING"
                },
                include: {
                    client: true,
                    product: true
                }
            });
            return { success: true, data: transaction };
        } catch (error: any) {
            console.error('Error creating transaction:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get transaction by trans_id using Prisma
     */
    async getTransactionByIdPrisma(trans_id: string) {
        try {
            const transaction = await this.prisma.transaction.findUnique({
                where: { trans_id },
                include: {
                    client: true,
                    product: true,
                    logs: true,
                    scTransactions: true
                }
            });
            return transaction;
        } catch (error: any) {
            console.error('Error fetching transaction:', error);
            return null;
        }
    }

    /**
     * Get transaction by reference_id using Prisma
     */
    async getTransactionByRefIdPrisma(reference_id: string) {
        try {
            const transaction = await this.prisma.transaction.findFirst({
                where: { reference_id },
                include: {
                    client: true,
                    product: true,
                    logs: true,
                    scTransactions: true
                }
            });
            return transaction;
        } catch (error: any) {
            console.error('Error fetching transaction by ref ID:', error);
            return null;
        }
    }

    /**
     * Update transaction using Prisma
     */
    async updateTransactionPrisma(trans_id: string, updateData: {
        status?: string;
        system_status?: string;
        running_balance?: number;
        ext_reference?: string;
        [key: string]: any;
    }) {
        try {
            const transaction = await this.prisma.transaction.update({
                where: { trans_id },
                data: updateData
            });
            return { success: true, data: transaction };
        } catch (error: any) {
            console.error('Error updating transaction:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get transactions by client_id using Prisma
     */
    async getClientTransactionsPrisma(client_id: string, options?: {
        trans_type?: string;
        status?: string;
        limit?: number;
        offset?: number;
    }) {
        try {
            const where: any = { client_id };
            
            if (options?.trans_type) where.trans_type = options.trans_type;
            if (options?.status) where.status = options.status;

            const transactions = await this.prisma.transaction.findMany({
                where,
                include: {
                    client: true,
                    product: true,
                    scTransactions: true
                },
                orderBy: { created_at: 'desc' },
                take: options?.limit,
                skip: options?.offset
            });

            return transactions;
        } catch (error: any) {
            console.error('Error fetching client transactions:', error);
            return [];
        }
    }

    /**
     * Get pending transactions using Prisma
     */
    async getPendingTransactionsPrisma(trans_type?: string) {
        try {
            const where: any = { 
                status: { in: ['PENDING', 'INITIATED'] }
            };
            
            if (trans_type) where.trans_type = trans_type;

            const transactions = await this.prisma.transaction.findMany({
                where,
                include: {
                    client: true,
                    product: true
                },
                orderBy: { created_at: 'desc' }
            });

            return transactions;
        } catch (error: any) {
            console.error('Error fetching pending transactions:', error);
            return [];
        }
    }

    // ===== TRANSACTION LOGGING =====

    /**
     * Save transaction log using Prisma
     */
    async saveTransactionLogPrisma(
        trans_id: string,
        status: string,
        step: Steps,
        response_code: number,
        description: string,
        data: any
    ) {
        try {
            const log = await this.prisma.transactionLog.create({
                data: {
                    trans_id,
                    status,
                    step,
                    response_code,
                    description,
                    data: typeof data === 'string' ? data : JSON.stringify(data)
                }
            });
            return { success: true, data: log };
        } catch (error: any) {
            console.error('Error saving transaction log:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get transaction logs using Prisma
     */
    async getTransactionLogsPrisma(trans_id: string) {
        try {
            const logs = await this.prisma.transactionLog.findMany({
                where: { trans_id },
                orderBy: { created_at: 'desc' }
            });
            return logs;
        } catch (error: any) {
            console.error('Error fetching transaction logs:', error);
            return [];
        }
    }

    // ===== CLIENT OPERATIONS =====

    /**
     * Get client by client_id using Prisma
     */
    async getClientPrisma(client_id: string) {
        try {
            const client = await this.prisma.client.findUnique({
                where: { client_id },
                include: {
                    wallets: true,
                    transactions: {
                        orderBy: { created_at: 'desc' },
                        take: 10
                    },
                    webhooks: true
                }
            });
            return client;
        } catch (error: any) {
            console.error('Error fetching client:', error);
            return null;
        }
    }

    /**
     * Get client wallet using Prisma
     */
    async getClientWalletPrisma(client_id: string) {
        try {
            const wallet = await this.prisma.clientWallet.findFirst({
                where: { client_id },
                include: {
                    client: true
                }
            });
            return wallet;
        } catch (error: any) {
            console.error('Error fetching client wallet:', error);
            return null;
        }
    }

    // ===== THIRD PARTY LOGGING =====

    /**
     * Log third-party interaction using Prisma
     */
    async logThirdPartyInteractionPrisma(
        trans_id: string,
        service_name: string,
        log_type: 'REQUEST' | 'RESPONSE' | 'ERROR',
        request_type: 'PUSH' | 'PULL' | 'VALIDATION' | 'STATUS',
        request_data: any,
        error_message?: string
    ) {
        try {
            const log = await this.prisma.thirdPartyLog.create({
                data: {
                    trans_id,
                    service_name,
                    log_type,
                    request_type,
                    request_data,
                    error_message
                }
            });
            return { success: true, data: log };
        } catch (error: any) {
            console.error('Error logging third-party interaction:', error);
            return { success: false, error: error.message };
        }
    }

    // ===== WEBHOOK OPERATIONS =====

    /**
     * Save webhook log using Prisma
     */
    async saveWebhookLogPrisma(data: {
        cl_id?: string;
        client_id: string;
        trans_id: string;
        webhook_url: string;
        event: string;
        webhook_data: any;
        status_code: number;
        direction?: string;
        provider?: string;
    }) {
        try {
            const log = await this.prisma.webhookLog.create({
                data: {
                    ...data,
                    webhook_data: data.webhook_data,
                    timestamp: new Date(),
                    direction: data.direction || 'OUTGOING',
                    provider: data.provider || 'MUDA'
                }
            });
            return { success: true, data: log };
        } catch (error: any) {
            console.error('Error saving webhook log:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get webhooks for client using Prisma
     */
    async getClientWebhooksPrisma(client_id: string) {
        try {
            const webhooks = await this.prisma.webhook.findMany({
                where: { 
                    client_id,
                    is_active: true
                }
            });
            return webhooks;
        } catch (error: any) {
            console.error('Error fetching client webhooks:', error);
            return [];
        }
    }

    // ===== UTILITY METHODS =====

    /**
     * Check if transaction exists (prevent duplicates) using Prisma
     */
    async checkDuplicateTransactionPrisma(
        client_id: string,
        amount: number,
        receiver_account: string,
        currency: string,
        timeWindowMinutes: number = 2
    ) {
        try {
            const timeAgo = new Date(Date.now() - timeWindowMinutes * 60 * 1000);
            
            const existing = await this.prisma.transaction.findFirst({
                where: {
                    client_id,
                    amount,
                    receiver_account,
                    currency,
                    created_at: {
                        gt: timeAgo
                    }
                }
            });

            return existing !== null;
        } catch (error: any) {
            console.error('Error checking duplicate transaction:', error);
            return false;
        }
    }

    /**
     * Get assets using Prisma
     */
    async getUtiliaAssetsPrisma() {
        try {
            const assets = await this.prisma.utiliaAsset.findMany({
                where: { is_muda_supported: true }
            });
            return assets;
        } catch (error: any) {
            console.error('Error fetching assets:', error);
            return [];
        }
    }

    /**
     * Get products using Prisma
     */
    async getProductsPrisma() {
        try {
            const products = await this.prisma.product.findMany({
                where: { is_active: true }
            });
            return products;
        } catch (error: any) {
            console.error('Error fetching products:', error);
            return [];
        }
    }

    /**
     * Make standardized response
     */
    makeResponse(status: number, message: string, data: any = null) {
        const response: any = { status, message };
        if (data !== null) {
            response.data = data;
        }
        return response;
    }
}

export default PrismaService; 
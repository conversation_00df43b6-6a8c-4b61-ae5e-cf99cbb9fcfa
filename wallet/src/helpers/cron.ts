import * as cron from 'node-cron';
import Transactions from '../models/transactions';
import ThirdParty from '../models/thirdParty';
import PegaPay from '../intergrations/PegPay';
import ThirdPartyHandler from './ThirdPartyHandler';
import MyFX from '../intergrations/MyFX';
import Model, { Steps } from './model';
import { StatusCodes } from '../intergrations/interfaces';
import { get } from './httpRequest';
import { getItem, setItem } from './connectRedis';
import PollingService from '../services/polling.collections.service';
import PollingPayoutsService from '../services/polling.payouts.service';
const pollingService = new PollingService();
const pollingPayoutsService = new PollingPayoutsService();
class CronService {
    private thirdparty: ThirdParty;
    private transactions_: Transactions;
    constructor() {
        console.log("Cron Service initiated.");
        this.scheduleEveryThirtySeconds();
        this.scheduleEveryFourtySeconds();
        this.scheduleEveryOneMinutes();
        this.scheduleEverySixHours();
        this.scheduleEveryMinute();
        this.thirdparty = new ThirdParty();
        this.transactions_ = new Transactions();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours task...');
            try {
                MyFX.getJWT()
                // Add your logic here
                console.log('Every six hours task completed.');
            } catch (error) {
                console.error('Error running every six hours task:', error);
            }
        });
    }

    private scheduleEveryMinute() {
        cron.schedule('* * * * *', async () => {
            console.log('Running every minute task...');
            try {
                const transactions = new Transactions();
                transactions.ReverseTransaction({
                    token: "mdxQaUg",
                    userId: "123"
                })

                // Add your logic here
                console.log('Every minute task completed.');
            } catch (error) {
                console.error('Error running every minute task:', error);
            }
        });
    }

    private scheduleEveryFourtySeconds() {
        cron.schedule('*/40 * * * * *', this.everyFourtySecondsTask);
    }
 

    private scheduleEveryThirtySeconds() {
        cron.schedule('*/30 * * * * *', this.everyThirtySecondsTask);
    }

    private scheduleEveryOneMinutes() {
        cron.schedule('*/1 * * * *', this.everyOneMinutesTask);
    }

 
    private everyThirtySecondsTask = () => {
        pollingService.getCollectionTransactions();
        console.log('Task running every 30 seconds, checking pending transactions...');
    };

    private everyFourtySecondsTask() {
        pollingPayoutsService.checkPendingDirectPayouts();
        console.log('Task running every 40 seconds, checking pending transactions...');
    }

    private everyOneMinutesTask = () => {
    //    this.checkPendingHoneyCoinCollections();
        console.log('Task running every 5 minutess, checking pending transactions...');
    };

}

export default CronService;
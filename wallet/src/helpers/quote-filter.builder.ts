import { QuoteFilter } from '../services/liquidityrail.service';

export class QuoteFilterBuilder {
  private CONDITIONS: string[] = [];

  constructor(filter: QuoteFilter) {



    if (filter?.start_date && filter?.end_date) {
      this.CONDITIONS.push(`created_on BETWEEN '${filter.start_date.toISOString()}' AND '${filter.end_date.toISOString()}'` && `created_on BETWEEN '${filter.start_date.toISOString()}' AND '${filter.end_date.toISOString()}'`);
    } else if(filter?.start_date && filter?.end_date === undefined && filter?.end_date === null){
      this.CONDITIONS.push(`created_on >= '${filter.start_date.toISOString()}'`);
    }  else if(filter?.end_date && filter?.start_date === undefined && filter?.start_date === null) {
      this.CONDITIONS.push(`created_on <= '${filter.end_date.toISOString()}'`);
    }


    if (filter?.provider_id && filter?.provider_id !== undefined && filter?.provider_id !== null) this.CONDITIONS.push(`provider_id = '${filter.provider_id}'`);
    if (filter?.company_id && filter?.company_id !== undefined && filter?.company_id !== null) this.CONDITIONS.push(`company_id = ${filter.company_id}`);
    if (filter?.status && filter?.status !== undefined && filter?.status !== null) this.CONDITIONS.push(`status = '${filter.status}'`);
    if (filter?.pay_in_status && filter?.pay_in_status !== undefined && filter?.pay_in_status !== null) this.CONDITIONS.push(`pay_in_status = '${filter.pay_in_status}'`);
  }

  build(): string {
    return this.CONDITIONS.length > 0 ? this.CONDITIONS.join(' AND ') : '';
  }
} 
ALTER TABLE `transactions_log` ADD `step` VARCHAR(40) NULL AFTER `status`, ADD `response_code` VARCHAR(10) NULL AFTER `step`, ADD `description` TEXT NULL AFTER `response_code`
ALTER TABLE `webhook_logs` ADD `direction` VARCHAR(40) NULL AFTER `cl_id`, ADD `provider` VARCHAR(30) NULL DEFAULT 'MUDA' AFTER `direction`;
CREATE TABLE balance_updates (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trans_id VARCHAR(255) NOT NULL UNIQUE,
  client_id VARCHAR(255) NOT NULL,
  asset_code VARCHAR(50) NOT NULL,
  status VARCHAR(50) NOT NULL,
  balance DECIMAL(36, 18) NOT NULL,
  type VARCHAR(20) NOT NULL,
  balance_updated_at DATETIME DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

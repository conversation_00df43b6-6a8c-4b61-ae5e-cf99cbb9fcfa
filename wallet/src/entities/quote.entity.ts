import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm';

@Entity('quotes')
export class Quote {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'transId', length: 50 })
  transId: string;

  @Column({ name: 'provider_id', length: 40 })
  providerId: string;

  @Column({ name: 'company_id' })
  companyId: number;

  @Column({ name: 'send_asset', length: 40 })
  sendAsset: string;

  @Column({ name: 'send_amount', length: 40 })
  sendAmount: string;

  @Column({ name: 'receive_currency', length: 40 })
  receiveCurrency: string;

  @Column({ name: 'receive_amount' })
  receiveAmount: number;

  @Column({ name: 'ex_rate', length: 40 })
  exRate: string;

  @Column({ name: 'account_number', length: 20 })
  accountNumber: string;

  @Column({ name: 'service_id', length: 40 })
  serviceId: string;

  @Column({ name: 'receiver_address', length: 70 })
  receiverAddress: string;

  @Column({ name: 'pay_in_status', length: 40, default: 'PENDING' })
  payInStatus: string;

  @Column({ length: 40 })
  status: string;

  @Column({ name: 'sending_address', length: 100 })
  sendingAddress: string;

  @Column({ name: 'response_body', type: 'text', nullable: true })
  responseBody: string;

  @Column({ length: 50, nullable: true })
  reason: string;

  @CreateDateColumn({ name: 'created_on' })
  createdOn: Date;

  @Column({ name: 'bank_name', length: 50, nullable: true })
  bankName: string;

  @Column({ name: 'bank_code', length: 50, nullable: true })
  bankCode: string;

  @Column({ name: 'provider_ref_id', length: 50, nullable: true })
  providerRefId: string;

  @Column({ name: 'provider_address', length: 100, nullable: true })
  providerAddress: string;

  @Column({ name: 'provider_memo', length: 90, nullable: true })
  providerMemo: string;

  @Column({ type: 'decimal', precision: 10, scale: 3, nullable: true })
  fee: number;

  @Column({ name: 'payment_method_id', length: 70, nullable: true })
  paymentMethodId: string;

  @Column({ length: 100, nullable: true })
  narration: string;

  @Column({ length: 250, nullable: true })
  hash: string;
} 
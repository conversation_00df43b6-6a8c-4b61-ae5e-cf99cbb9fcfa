import axios, { AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

interface TemboBaseResponse {
  success?: boolean;
  message?: string;
  statusCode?: string | number;
  reason?: string;
}

interface TemboVirtualAccountResponse extends TemboBaseResponse {
  id?: string;
  accountName?: string;
  accountNo?: string;
  reference?: string;
  tag?: string;
}

interface TemboBalanceResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    accountName: string;
    branchCode: string;
    availableBalance: number;
    bookedBalance: number;
  };
}

interface TemboStatementResponse extends TemboBaseResponse {
  result?: {
    accountNo: string;
    statement: Array<{
      id: string;
      transactionId: string;
      reference: string;
      transactionType: string;
      channel: string;
      transactionDate: string;
      postingDate: string;
      valueDate: string;
      narration: string;
      currency: string;
      amountCredit: number;
      amountDebit: number;
      clearedBalance: number;
      bookedBalance: number;
    }>;
  };
}

interface TemboCollectionResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

interface TemboPaymentResponse extends TemboBaseResponse {
  transactionRef?: string;
  transactionId?: string;
}

interface TemboWebhookPayload {
  accountNo: string;
  payerName?: string;
  id: string;
  transactionId: string;
  reference: string;
  transactionType: string;
  channel: string;
  transactionDate: string;
  postingDate: string;
  valueDate: string;
  narration: string;
  currency: string;
  amountCredit: number;
  amountDebit: number;
  clearedBalance: number;
  bookedBalance: number;
}

interface StandardResponse {
  status: number;
  message?: string;
  trans_id?: string;
  amount_transfered?: number;
  transaction_fee?: number;
  data?: any;
}

interface Logger {
  info(message: string, data?: any): void;
  error(message: string, data?: any): void;
  warn(message: string, data?: any): void;
}

const defaultLogger: Logger = {
  info: (message: string, data?: any) => console.log(`INFO: ${message}`, data || ''),
  error: (message: string, data?: any) => console.error(`ERROR: ${message}`, data || ''),
  warn: (message: string, data?: any) => console.warn(`WARN: ${message}`, data || '')
};

export default class TemboService {
  private baseURL: string;
  private authToken: string;
  private accountId: string;
  private secretKey: string;
  private webhookSecret: string;
  private mainAccountNo: string;
  private logger: Logger;

  constructor(logger?: Logger) {
    this.logger = logger || defaultLogger;

    const requiredEnvVars = [
      { key: 'TEMBO_AUTH_TOKEN', description: 'Tembo API authentication token' },
      { key: 'TEMBO_ACCOUNT_ID', description: 'Tembo account identifier' },
      { key: 'TEMBO_SECRET_KEY', description: 'Tembo API secret key (also used for webhook verification)' },
      { key: 'TEMBO_MAIN_ACCOUNT_NO', description: 'Tembo main account number for payouts and transfers' },
      { key: 'WEBHOOK_BASE_URL', description: 'Base URL for webhook callbacks' }
    ];

    this.validateRequiredEnvVars(requiredEnvVars);

    this.baseURL = process.env.TEMBO_API_URL || 'https://sandbox.temboplus.com';
    this.authToken = process.env.TEMBO_AUTH_TOKEN!;
    this.accountId = process.env.TEMBO_ACCOUNT_ID!;
    this.secretKey = process.env.TEMBO_SECRET_KEY!;
    this.mainAccountNo = process.env.TEMBO_MAIN_ACCOUNT_NO!;
    this.webhookSecret = process.env.TEMBO_SECRET_KEY!;
  }

  private validateRequiredEnvVars(requiredVars: Array<{ key: string; description: string }>): void {
    const missingVars: string[] = [];

    for (const envVar of requiredVars) {
      if (!process.env[envVar.key]) {
        missingVars.push(`${envVar.key} (${envVar.description})`);
      }
    }

    if (missingVars.length > 0) {
      console.log("missing vars", missingVars);
    }
  }

  /**
   * Generate request headers following Tembo API requirements
   */
  private getHeaders(includeAuth: boolean = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'x-request-id': uuidv4()
    };

    if (includeAuth) {
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      if (this.accountId) {
        headers['x-account-id'] = this.accountId;
      }
      if (this.secretKey) {
        headers['x-secret-key'] = this.secretKey;
      }
    }

    return headers;
  }

  /**
   * Sanitize headers for logging (remove sensitive information)
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };
    if (sanitized['Authorization']) {
      sanitized['Authorization'] = 'Bearer ***MASKED***';
    }
    if (sanitized['x-secret-key']) {
      sanitized['x-secret-key'] = '***MASKED***';
    }
    
    return sanitized;
  }

  private async makeRequest(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    useCollectionAuth: boolean = false,
    useBearerAuth: boolean = false
  ): Promise<any> {
    const requestId = `TEMBO_${method}_${Date.now()}`;

    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = useBearerAuth ?
        this.getBearerHeaders() :
        (useCollectionAuth ?
          this.getCollectionHeaders() :
          this.getHeaders());

      console.log(`${requestId}_REQUEST`, { url, payload: data });

      let response: AxiosResponse;

      if (method === 'POST') {
        response = await axios.post(url, data, { headers });
      } else {
        response = await axios.get(url, { headers });
      }

      console.log(`${requestId}_RESPONSE`, response.data);

      return response.data;
    } catch (error: any) {
      console.error(`${requestId}_ERROR`, {
        endpoint,
        error: error.response?.data || error.message
      });

      return {
        success: false,
        statusCode: error.response?.status || 500,
        message: error.response?.data?.message || error.message,
        data: error.response?.data
      };
    }
  }

  /**
   * Get collection-specific headers (different auth pattern)
   */
  private getCollectionHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'x-account-id': this.accountId,
      'x-secret-key': this.secretKey,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Get Bearer token headers for remittance API
   */
  private getBearerHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.authToken}`,
      'x-request-id': uuidv4()
    };
  }

  /**
   * Normalize response to standard format
   */
  private normalizeResponse(
    temboResponse: any, 
    transactionId?: string,
    amount?: number
  ): StandardResponse {

    if (temboResponse.success === false || 
        (temboResponse.statusCode && typeof temboResponse.statusCode === 'number' && temboResponse.statusCode >= 400)) {
      return {
        status: typeof temboResponse.statusCode === 'number' ? 
          temboResponse.statusCode : 500,
        message: temboResponse.message || temboResponse.reason || 'Transaction failed',
        data: temboResponse
      };
    }

    
    if (temboResponse.success === true || 
        temboResponse.statusCode === 'PENDING_ACK' || 
        temboResponse.statusCode === 'PAYMENT_ACCEPTED' ||
        temboResponse.statusCode === 201 ||
        
        (temboResponse.accountNo && temboResponse.accountName) ||
        (temboResponse.availableBalance !== undefined) ||
        (temboResponse.currentBalance !== undefined) ||
        
        temboResponse.transactionRef ||
        temboResponse.transactionId ||
        
        (!temboResponse.statusCode && Object.keys(temboResponse).length > 0)) {
      return {
        status: 200,
        message: temboResponse.message || 'Request successful',
        trans_id: temboResponse.transactionRef || temboResponse.transactionId || transactionId,
        amount_transfered: amount,
        data: temboResponse
      };
    }
    return {
      status: 500,
      message: temboResponse.message || temboResponse.reason || 'Unknown response format',
      data: temboResponse
    };
  }

  /**
   * Detect mobile network from phone number
   */
  private detectMobileNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    
    
    const prefix = cleanPhone.substring(0, 6);
    
    
    if (prefix === '255757' || prefix === '255756' || prefix === '255755' || prefix === '255754') {
      return 'TZ-VODACOM-C2B'; 
    }
    
   
    if (prefix === '255789' || prefix === '255788' || prefix === '255787' || prefix === '255786' || prefix === '255785' || prefix === '255784' || prefix === '255783' || prefix === '255782') {
      return 'TZ-AIRTEL-C2B';
    }
    
    
    if (prefix === '255715' || prefix === '255714' || prefix === '255713' || prefix === '255712' || prefix === '255711') {
      return 'TZ-TIGO-C2B';
    }
  
    return '';
  }

  /**
   * Get payout network code
   */
  private getPayoutNetwork(phone: string): string {
    const cleanPhone = phone.replace(/\+/g, '');
    const prefix = cleanPhone.substring(0, 6); 
    
    
    if (prefix === '255757' || prefix === '255756' || prefix === '255755' || prefix === '255754') {
      return 'TZ-VODACOM-B2C';
    }
    
    
    if (prefix === '255789' || prefix === '255788' || prefix === '255787' || prefix === '255786' || prefix === '255785' || prefix === '255784' || prefix === '255783' || prefix === '255782') {
      return 'TZ-AIRTEL-B2C';
    }
    
    if (prefix === '255715' || prefix === '255714' || prefix === '255713' || prefix === '255712' || prefix === '255711') {
      return 'TZ-TIGO-B2C';
    }

    return '';
  }

  async createVirtualAccount(
    merchantId: string,
    metadata: { companyName: string; tag?: string }
  ): Promise<StandardResponse> {
    const transId = `VA_${merchantId}_${Date.now()}`;

    try {
      const payload = {
        companyName: metadata.companyName,
        reference: `MUDA_${merchantId}_${Date.now()}`,
        tag: metadata.tag || process.env.TEMBO_DEFAULT_TAG || 'MUDA'
      };

      const response: TemboVirtualAccountResponse = await this.makeRequest(
        'POST',
        '/account',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Virtual account creation failed',
        data: error
      };
    }
  }

  /**
   * Get Virtual Account Balance
   * Maps to: POST /account/balance
   */
  async getVirtualAccountBalance(accountNo: string): Promise<StandardResponse> {
    const transId = `BAL_${accountNo}_${Date.now()}`;

    try {
      const payload = { accountNo };
      const response: TemboBalanceResponse = await this.makeRequest(
        'POST',
        '/account/balance',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Balance retrieval failed',
        data: error
      };
    }
  }

  /**
   * Get Virtual Account Statement
   * Maps to: POST /account/statement
   */
  async getVirtualAccountStatement(
    accountNo: string,
    startDate: string,
    endDate: string
  ): Promise<StandardResponse> {
    const transId = `STMT_${accountNo}_${Date.now()}`;

    try {
      const payload = { accountNo, startDate, endDate };
      const response: TemboStatementResponse = await this.makeRequest(
        'POST',
        '/account/statement',
        payload
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Statement retrieval failed',
        data: error
      };
    }
  }


  /**
   * Mobile Money Pull Request (Collection)
   * Maps to: POST /tembo/v1/collection
   */
  async makeMMPullRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);

      const channel = this.detectMobileNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        msisdn: cleanPhone,
        channel: channel,
        amount: amount,
        narration: `Collection for ${VendorTranId}`,
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/v1/wallet/webhooks/tembo`
      };

      const response: TemboCollectionResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/collection',
        payload,
        true
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Collection request failed',
        data: error
      };
    }
  }


  /**
   * Mobile Money Push Request (Payout)
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile
   */
  async makeMMPushRequest(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    phone: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS'; 

      
      const serviceCode = this.getPayoutNetwork(phone);
      const cleanPhone = phone.replace(/\+/g, '');

      const payload = {
        countryCode: 'TZ',
        serviceCode: serviceCode,
        accountNo: this.mainAccountNo,
        amount: amount,
        msisdn: cleanPhone,
        narration: `Payout for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: 'Customer',
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/v1/wallet/webhooks/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile',
        payload,
        true
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Payout request failed',
        data: error
      };
    }
  }

  /**
   * Bank Transfer from Main Account
   * Maps to: POST /tembo/v1/payment/wallet-to-mobile (with bank service code)
   */
  async payToBank(
    userId: string,
    VendorTranId: string,
    TranAmount: string,
    bankCode: string,
    accountNumber: string,
    recipientName: string
  ): Promise<StandardResponse> {
    try {
      const amount = parseFloat(TranAmount);
      const currency = 'TZS';

      const payload = {
        countryCode: 'TZ',
        accountNo: '**********',
        serviceCode: 'TZ-BANK-B2C', 
        amount: amount,
        msisdn: `${bankCode}:${accountNumber}`,
        narration: `Bank transfer for ${VendorTranId}`,
        currencyCode: currency,
        recipientNames: recipientName,
        transactionRef: VendorTranId,
        transactionDate: new Date().toISOString().slice(0, 19).replace('T', ' '),
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/v1/wallet/webhooks/tembo`
      };

      const response: TemboPaymentResponse = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/wallet-to-mobile',
        payload,
        true
      );

      return this.normalizeResponse(response, VendorTranId, amount);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Bank transfer failed',
        data: error
      };
    }
  }

  async getMainBalance(): Promise<StandardResponse> {
    const transId = `MAIN_BAL_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/main-balance',
        {},
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Balance retrieval failed',
        data: error
      };
    }
  }

  /**
   * Get Collection Balance
   * Maps to: POST /tembo/v1/wallet/collection-balance
   */
  async getCollectionBalance(): Promise<StandardResponse> {
    const transId = `COL_BAL_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/collection-balance',
        {},
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Collection balance retrieval failed',
        data: error
      };
    }
  }

  /**
   * Get Collection Statement
   * Maps to: POST /tembo/v1/wallet/collection-statement
   */
  async getCollectionStatement(startDate: string, endDate: string): Promise<StandardResponse> {
    const transId = `COL_STMT_${Date.now()}`;

    try {
      const payload = {
        startDate: startDate, 
        endDate: endDate    
      };

      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/wallet/collection-statement',
        payload,
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Collection statement retrieval failed',
        data: error
      };
    }
  }



  /**
   * Create Remittance
   * Maps to: POST /remittance
   * For international money transfers to Tanzania
   */
  async createRemittance(
    transactionId: string,
    remittanceData: {
      senderCurrency: string;
      senderAmount: number;
      receiverCurrency: string;
      receiverAmount: number;
      exchangeRate: number;
      receiverAccount: string;
      receiverChannel: 'MOBILE' | 'BANK';
      institutionCode: string;
      sender: {
        fullName: string;
        nationality: string;
        countryCode: string;
        idType: string;
        idNumber: string;
        idExpiryDate: string;
        dateOfBirth: string;
        phoneNumber: string;
        email: string;
        address: string;
        sourceOfFundsDeclaration: string;
        purposeOfTransaction: string;
        occupation: string;
        employer: string;
      };
      receiver: {
        fullName: string;
        phoneNumber: string;
        email?: string;
        countryCode: string;
      };
    }
  ): Promise<StandardResponse> {
    try {
      const payload = {
        paymentDate: new Date().toISOString(),
        senderCurrency: remittanceData.senderCurrency,
        senderAmount: remittanceData.senderAmount,
        receiverCurrency: remittanceData.receiverCurrency,
        receiverAmount: remittanceData.receiverAmount,
        exchangeRate: remittanceData.exchangeRate,
        receiverAccount: remittanceData.receiverAccount,
        receiverChannel: remittanceData.receiverChannel,
        institutionCode: remittanceData.institutionCode,
        partnerReference: transactionId,
        callbackUrl: `${process.env.WEBHOOK_BASE_URL}/v1/wallet/webhooks/tembo/remittance`,
        sender: remittanceData.sender,
        receiver: remittanceData.receiver
      };

      const response = await this.makeRequest(
        'POST',
        '/remittance',
        payload,
        true,
        true 
      );

      return this.normalizeResponse(response, transactionId, remittanceData.receiverAmount);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Remittance creation failed',
        data: error
      };
    }
  }

  /**
   * Get Remittance Status
   * Maps to: GET /remittance/status/{partnerReference}
   */
  async getRemittanceStatus(partnerReference: string): Promise<StandardResponse> {
    const transId = `REM_STATUS_${Date.now()}`;

    try {
      const response = await this.makeRequest(
        'GET',
        `/remittance/status/${partnerReference}`,
        undefined,
        true,
        true 
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Remittance status check failed',
        data: error
      };
    }
  }


  /**
   * Get Collection Status (for pulls/collections)
   * Maps to: POST /tembo/v1/collection/status
   */
  async getCollectionStatus(transactionRef: string, transactionId?: string): Promise<StandardResponse> {
    const transId = `COL_STATUS_${transactionRef}_${Date.now()}`;

    try {
      const payload = {
        transactionRef: transactionRef,
        ...(transactionId && { transactionId: transactionId })
      };

      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/collection/status',
        payload,
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Collection status check failed',
        data: error
      };
    }
  }

  /**
   * Get Payment Status (for payouts/payments)
   * Maps to: POST /tembo/v1/payment/status
   */
  async getPaymentStatus(transactionRef: string, transactionId?: string): Promise<StandardResponse> {
    const transId = `PAY_STATUS_${transactionRef}_${Date.now()}`;

    try {
      const payload = {
        transactionRef: transactionRef,
        ...(transactionId && { transactionId: transactionId })
      };

      const response = await this.makeRequest(
        'POST',
        '/tembo/v1/payment/status',
        payload,
        true
      );

      return this.normalizeResponse(response, transId);
    } catch (error: any) {
      return {
        status: 500,
        message: error.message || 'Payment status check failed',
        data: error
      };
    }
  }

  /**
   * Get Transaction Status - Auto-detects transaction type and uses correct endpoint
   * @deprecated Use getCollectionStatus() or getPaymentStatus() directly for better clarity
   */
  async getTransactionStatus(transactionRef: string, transactionType?: 'COLLECTION' | 'PAYMENT'): Promise<StandardResponse> {
    // If transaction type is specified, use the appropriate method
    if (transactionType === 'COLLECTION') {
      return this.getCollectionStatus(transactionRef);
    } else if (transactionType === 'PAYMENT') {
      return this.getPaymentStatus(transactionRef);
    }

    // Default to payment status for backward compatibility
    // TODO: This should be updated to detect transaction type from database
    console.warn(`⚠️ getTransactionStatus called without transaction type for ${transactionRef}. Defaulting to payment status.`);
    return this.getPaymentStatus(transactionRef);
  }



  /**
   * Verify webhook signature
   * Follows Tembo's HMAC SHA-256 signature verification
   */
  verifyWebhookSignature(
    payload: TemboWebhookPayload,
    timestamp: string,
    receivedSignature: string
  ): boolean {
    try {
      if (!this.webhookSecret) {
        this.logger.warn('Webhook secret not configured');
        return false;
      }

      
      const secret = Buffer.from(this.webhookSecret, 'base64');

      
      const concatenatedString =
        timestamp +
        payload.accountNo +
        payload.id +
        payload.transactionId +
        payload.reference +
        payload.transactionType +
        payload.channel +
        payload.transactionDate +
        payload.postingDate +
        payload.valueDate +
        payload.narration +
        payload.currency +
        Math.trunc(payload.amountCredit).toString() +
        Math.trunc(payload.amountDebit).toString() +
        Math.trunc(payload.clearedBalance).toString() +
        Math.trunc(payload.bookedBalance).toString();

      
      const hmac = crypto.createHmac('sha256', secret);
      hmac.update(Buffer.from(concatenatedString, 'utf-8'));
      const computedSignature = hmac.digest('base64');

      return computedSignature === receivedSignature;
    } catch (error: any) {
      this.logger.error('Webhook signature verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Process webhook payload
   * Normalizes webhook data to standard format
   */
  async processWebhook(
    payload: TemboWebhookPayload,
    signature: string,
    timestamp: string
  ): Promise<StandardResponse> {
    try {
   
      const isValid = this.verifyWebhookSignature(payload, timestamp, signature);
      if (!isValid) {
        return {
          status: 401,
          message: 'Invalid webhook signature',
          data: payload
        };
      }
      const creditAmount = payload.amountCredit || 0;
      const debitAmount = payload.amountDebit || 0;
      let transactionDirection: 'CREDIT' | 'DEBIT' | 'TRANSFER';
      let primaryAmount: number;

      if (creditAmount > 0 && debitAmount === 0) {
        
        transactionDirection = 'CREDIT';
        primaryAmount = creditAmount;
      } else if (debitAmount > 0 && creditAmount === 0) {
        
        transactionDirection = 'DEBIT';
        primaryAmount = debitAmount;
      } else if (creditAmount > 0 && debitAmount > 0) {
        transactionDirection = 'TRANSFER';
        primaryAmount = Math.max(creditAmount, debitAmount); 
      } else {
        transactionDirection = 'CREDIT';
        primaryAmount = 0;
      }

      
      const temboTransactionType = payload.transactionType;
      const channel = payload.channel; 

      // CRITICAL: For collections (credits), this means a customer paid into Tembo's main account
      // The payload.reference is the transactionRef we sent, which maps to our transactions table
      // This allows us to identify which merchant the funds belong to

      if (transactionDirection === 'CREDIT') {
        this.logger.info(`💰 Collection received in Tembo main account`, {
          reference: payload.reference,
          creditAmount: creditAmount,
          debitAmount: debitAmount,
          primaryAmount: primaryAmount,
          channel: payload.channel,
          payer: payload.payerName,
          temboTransactionType: temboTransactionType,
          note: 'Funds need to be attributed to merchant based on reference'
        });
      }

      return {
        status: 200,
        message: 'Tembo webhook processed - merchant attribution via reference',
        trans_id: payload.reference,
        amount_transfered: primaryAmount,
        data: {
          ...payload,
          transaction_direction: transactionDirection,
          primary_amount: primaryAmount,
          credit_amount: creditAmount,
          debit_amount: debitAmount,
          tembo_transaction_type: temboTransactionType,
          channel: channel,
          is_collection: transactionDirection === 'CREDIT',
          merchant_attribution_note: 'Use payload.reference to find merchant in transactions table',
          processed_at: new Date().toISOString()
        }
      };
    } catch (error: any) {
      this.logger.error('Tembo webhook processing failed', { error: error.message, payload });
      return {
        status: 500,
        message: 'Tembo webhook processing failed',
        data: { error: error.message, payload }
      };
    }
  }

  /**
   * Validate phone number format for Tanzania
   */
  validatePhoneNumber(phone: string): { valid: boolean; message?: string } {
    const cleanPhone = phone.replace(/[\s\-\+]/g, '');
    if (!cleanPhone.startsWith('255')) {
      return { valid: false, message: 'Phone number must start with 255 (Tanzania country code)' };
    }

    if (cleanPhone.length !== 12) {
      return { valid: false, message: 'Phone number must be 12 digits including country code' };
    }
    const network = this.detectMobileNetwork(cleanPhone);
    if (!network.includes('TZ-')) {
      return { valid: false, message: 'Unsupported mobile network' };
    }

    return { valid: true };
  }

  /**
   * Format amount for Tembo API
   */
  formatAmount(amount: number): number {
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return ['TZS'];
  }

  /**
   * Get supported mobile networks
   */
  getSupportedNetworks(): string[] {
    return [
      'TZ-TIGO-C2B', 'TZ-TIGO-B2C',
      'TZ-AIRTEL-C2B', 'TZ-AIRTEL-B2C',
      'TZ-VODACOM-C2B', 'TZ-VODACOM-B2C'
    ];
  }

  /**
   * Get supported bank codes for Tanzania
   */
  getSupportedBankCodes(): string[] {
    return [
      'CRDBTZTZ',
      'NMIBTZTZ',
      'CORUTZTZ',
      'FBMEUKTZ', 
      'TCIBTZTZ'  
    ];
  }

  /**
   * Get network display name
   */
  getNetworkDisplayName(networkCode: string): string {
    const networkNames: Record<string, string> = {
      'TZ-TIGO-C2B': 'Tigo Pesa (Collection)',
      'TZ-TIGO-B2C': 'Tigo Pesa (Payout)',
      'TZ-AIRTEL-C2B': 'Airtel Money (Collection)',
      'TZ-AIRTEL-B2C': 'Airtel Money (Payout)',
      'TZ-VODACOM-C2B': 'M-Pesa (Collection)',
      'TZ-VODACOM-B2C': 'M-Pesa (Payout)',
      'TZ-BANK-B2C': 'Bank Transfer'
    };

    return networkNames[networkCode] || networkCode;
  }

  /**
   * Check if transaction is still pending based on status
   */
  isTransactionPending(statusCode: string | number): boolean {
    const pendingStatuses = [
      'PENDING_ACK',
      'PENDING',
      'PROCESSING',
      202,
      'PAYMENT_ACCEPTED'
    ];

    return pendingStatuses.includes(statusCode);
  }

  /**
   * Check if transaction is successful
   */
  isTransactionSuccessful(statusCode: string | number): boolean {
    const successStatuses = [
      'SUCCESS',
      'COMPLETED',
      'SUCCESSFUL',
      200,
      201,
      true
    ];

    return successStatuses.includes(statusCode);
  }

  /**
   * Check if transaction failed
   */
  isTransactionFailed(statusCode: string | number): boolean {
    return !this.isTransactionPending(statusCode) && 
           !this.isTransactionSuccessful(statusCode);
  }

  /**
   * Generate unique transaction reference
   */
  generateTransactionRef(prefix: string = 'TEMBO'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Parse Tembo date format to ISO string
   */
  parseTemboDate(temboDate: string): string {
    try {
      const date = new Date(temboDate);
      return date.toISOString();
    } catch (error) {
      this.logger.warn('Failed to parse Tembo date', { temboDate });
      return new Date().toISOString();
    }
  }

  /**
   * Format date for Tembo API
   */
  formatDateForTembo(date: Date = new Date()): string {
    // Return format: YYYY-MM-DD HH:mm:ss
    return date.toISOString().slice(0, 19).replace('T', ' ');
  }

  /**
   * Calculate transaction fees (mock implementation)
   * In production, this should use Tembo's actual fee structure
   */
  calculateTransactionFee(amount: number, transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'): number {
    const feeRates = {
      'COLLECTION': 0.015, // 1.5%
      'PAYOUT': 0.02,      // 2.0%
      'BANK_TRANSFER': 0.025 // 2.5%
    };

    const rate = feeRates[transactionType] || 0.02;
    const fee = amount * rate;
  
    return Math.max(500, Math.min(5000, fee));
  }

  /**
   * Get transaction limits for different transaction types
   */
  getTransactionLimits(transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'): {
    min: number;
    max: number;
    daily: number;
  } {
    const limits = {
      'COLLECTION': { min: 1000, max: 5000000, daily: ******** },
      'PAYOUT': { min: 1000, max: 3000000, daily: 8000000 },
      'BANK_TRANSFER': { min: 10000, max: ********, daily: ******** }
    };

    return limits[transactionType] || limits['COLLECTION'];
  }

  /**
   * Validate transaction amount against limits
   */
  validateTransactionAmount(
    amount: number, 
    transactionType: 'COLLECTION' | 'PAYOUT' | 'BANK_TRANSFER'
  ): { valid: boolean; message?: string } {
    const limits = this.getTransactionLimits(transactionType);

    if (amount < limits.min) {
      return {
        valid: false,
        message: `Amount ${amount} TZS is below minimum limit of ${limits.min} TZS for ${transactionType}`
      };
    }

    if (amount > limits.max) {
      return {
        valid: false,
        message: `Amount ${amount} TZS exceeds maximum limit of ${limits.max} TZS for ${transactionType}`
      };
    }

    return { valid: true };
  }
}
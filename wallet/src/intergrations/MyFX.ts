import axios from "axios";
import { getItem, setItem } from "../helpers/connectRedis";
import { Steps } from "../helpers/model";

class MyFX {
  private baseURL: string;
  private tokenKey: string;

  constructor() {
    this.baseURL = process.env.MYFX_API_URL ?? "https://api.myfx.ca/api/";
    this.tokenKey = "myfx_token";
  }

  private async getToken(): Promise<string | null> {
    let token: any = await getItem(this.tokenKey);
    if (!token) {
      const email = process.env.MYFX_EMAIL!;
      const password = process.env.MYFX_PASSWORD!;
      const loginResponse = await this.login(email, password);
      if (loginResponse.success && loginResponse.token) {
        token = loginResponse.token;
        await setItem(this.tokenKey, token);
      }
    }
    return token;
  }

  async getJWT() {
    let token: any = null
    const email = process.env.MYFX_EMAIL!;
    const password = process.env.MYFX_PASSWORD!;
    const loginResponse = await this.login(email, password);
    if (loginResponse.success && loginResponse.token) {
      token = loginResponse.token;
      await setItem(this.tokenKey, token);
    }

    return token;
  }

  private async request(
    method: "post",
    endpoint: string,
    data?: any,
    auth = true
  ): Promise<any> {
    const headers: Record<string, string> = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    if (auth) {
      const token = await this.getToken();
      if (token) headers["Authorization"] = `Bearer ${token}`;
    }

    try {
      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        headers,
        data,
      });
      // Token is Expired
      if (response.data.message == "Token is Expired") {
        await this.getJWT()
        return this.request(method, endpoint, data, auth)
      }
      return response.data;
    } catch (error: any) {
      console.log(error)
      console.log(error?.response?.data)
      console.log(error?.response?.status)
      return {
        success: false,
        error: error?.response?.data || error.message,
      };
    }
  }

  // 1. Login
  public async login(email: string, password: string) {
    return this.request("post", "login", { email, password }, false);
  }

  // 2. Get Reference Number
  public async getReferenceNo(amount: any) {
    console.log("amount", amount)
    const response = await this.request("post", "get_reference_no", { amount }, true);
    console.log("getReferenceNo", response)
    return response;
  }

  // 3. Confirm Payment
  public async confirmPayment(reference_no: string) {
    const data = {
      reference_no
    }
    if (reference_no) data.reference_no = reference_no;
    return this.request("post", "confirm_payment", data);
  }

  // 4. Get All Payments
  public async getAllPayments(start_date?: string, end_date?: string) {
    const data: Record<string, string> = {};
    if (start_date) data.start_date = start_date;
    if (end_date) data.end_date = end_date;
    return this.request("post", "get_all_payments", data);
  }
  async getBalance() {
    return this.request("post", "get_balance");
  }
  // 5. Make Payout
  async makePayout(name:string, interact_email:string, amount:number) {
    const body = {
      name,
      interact_email,
      amount
    };
    if (!name || !interact_email || !amount) {
      return {
        success: false,
        error: "Name, interact_email, and amount are required."
      };
    }
    if (amount <= 0) {
      return {
        success: false,
        error: "Amount must be greater than 0."
      };
    }
    if (!/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(interact_email)) {
      return {
        success: false,
        error: "Invalid interact_email format."
      };
    }
    
    return this.request("post", "payout", body);
  }

}

export default new MyFX();

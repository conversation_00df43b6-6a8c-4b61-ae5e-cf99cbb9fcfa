import axios from 'axios';
import parser from 'xml2json';
import crypto from 'crypto';
import NodeRS<PERSON> from 'node-rsa';
import fs from 'fs';
import Model, { Steps } from '../helpers/model';
export interface ThirdPartyBalance {
  balance: string | number;
  statusCode: any;
}
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = "0";

export default class PegaPay {
  constructor() { }

  public async getProvider(phone: string, currency: string = 'UGX') {
    const output = phone.substring(0, 6);
    console.log(`output`, output)
    const phoneCode = output.substring(3, 6);
    console.log(`phoneCode`, phoneCode)
    const network = await new Model().getNetworkCodes(phoneCode);
    console.log(`network`, network)
    return network;
  }

  public async getProviderOld(phone: string, currency: string = 'UGX'): Promise<string> {
    if (currency == 'UGX') {
      const output = phone.substring(0, 5);

      const mtnPrefixes = ['25677', '25678', '25676', '25639'];
      const airtelPrefixes = ['25670', '25675', '25674', '25679'];
      const smilePrefixes = ['25631'];
      const lycaPrefixes = ['25641'];

      if (mtnPrefixes.includes(output)) return "MTN";
      if (airtelPrefixes.includes(output)) return "AIRTEL";
      if (smilePrefixes.includes(output)) return "SMILE";
      if (lycaPrefixes.includes(output)) return "LYCAMOBILE";
    } else if (currency == 'KES') {
      const output = phone.substring(0, 5);
      const safPrefixes = ['25470', '25471', '25472', '25473', '25474', '25475', '25476', '25477', '25478', '25479'];
      const airtelPrefixes = ['25470', '25471', '25472', '25473', '25474', '25475', '25476', '25477', '25478', '25479'];
      const smilePrefixes = ['25431'];
      const lycaPrefixes = ['25441'];
      if (safPrefixes.includes(output)) return "SAFARICOM";
      if (airtelPrefixes.includes(output)) return "AIRTEL";
      if (smilePrefixes.includes(output)) return "SMILE";
      if (lycaPrefixes.includes(output)) return "LYCAMOBILE";
    }

    return "UNKNOWN";
  }


  private async getSignatureToVerify(data: string): Promise<string> {
    const keyLocation = process.env.PegSecKey || "";
    const keyData = fs.readFileSync(keyLocation, 'utf8');
    const key = new NodeRSA(keyData);
    const privateKey = key.exportKey('pkcs8-private-pem');

    const sign = crypto.createSign('sha1WithRSAEncryption');
    sign.update(Buffer.from(data));
    return sign.sign(privateKey, 'base64');
  }




  private async sendSoapRequest(body: string, soapAction: string): Promise<any> {
    try {
      const response = await axios.post(process.env.mmURL!, body, {
        headers: {
          'Content-Type': 'text/xml; charset=utf-8',
          'SOAPAction': soapAction,
        },
      });

      return response.data;
    } catch (error) {
      console.error("SOAP Request Error:", error);
      throw new Error("Failed to process SOAP request");
    }
  }

  public async makeMMPushRequest(userId: string, VendorTranId: string, TranAmount: string, SessionId: string, phone: string) {
    const VendorCode = process.env.VENDOR_PUSH
    const Password = process.env.PUSH_PASSWORD
    phone = phone.replace("+", "")
    const tel = await this.getProvider(phone);
    if (tel == "") {
      return "Invalid phone format, phone number must start with 256"
    }
    const FromTelecom = tel;
    const ToTelecom = tel;
    const PaymentCode = "2";
    const PaymentDate = new Date().toLocaleDateString()
    const Telecom = tel;
    const CustomerRef = "BT-0001";
    const CustomerName = "MUDA";
    const TranCharge = "0";
    const ToAccount = phone;
    const FromAccount = phone;
    const Narration = "muda pay";
    const TranType = "PUSH";
    const ip = "***********"
    const dataToSign = CustomerRef + CustomerName + FromTelecom + ToTelecom + VendorTranId + VendorCode + Password + PaymentDate + TranType + PaymentCode + TranAmount + FromAccount + ToAccount;

    const signature = await this.getSignatureToVerify(dataToSign);

    const reqBody = `
      <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
      <Body>
      <PostTransaction xmlns="http://PegPayTelecomsApi/">
      <trans>
        <SessionId>${SessionId}</SessionId>
        <Narration>muda pay</Narration>
        <IP>***********</IP>
        <DigitalSignature>${signature}</DigitalSignature>
        <FromTelecom>${tel}</FromTelecom>
        <ToTelecom>${tel}</ToTelecom>
        <PaymentCode>2</PaymentCode>
        <VendorCode>${process.env.VENDOR_PUSH}</VendorCode>
        <Password>${process.env.PUSH_PASSWORD}</Password>
        <PaymentDate>${PaymentDate}</PaymentDate>
        <CustomerRef>BT-0001</CustomerRef>
        <CustomerName>MUDA</CustomerName>
        <TranAmount>${TranAmount}</TranAmount>
        <TranCharge>0</TranCharge>
        <VendorTranId>${VendorTranId}</VendorTranId>
        <ToAccount>${phone}</ToAccount>
        <FromAccount>${phone}</FromAccount>
        <TranType>PUSH</TranType>
      </trans>
      </PostTransaction>
      </Body>
      </Envelope>`;

    console.log(`sendSoapRequest_1`, reqBody)
    new Model().saveTransactionLog(VendorTranId, "PEGPAY_PUSH_REQUEST", Steps.THIRDPARTY_REQUEST, 202, "http://PegPayTelecomsApi/PostTransaction", reqBody)
    const data: any = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/PostTransaction");
    console.log(`sendSoapRequest_2`, data)
    new Model().saveTransactionLog(VendorTranId, "PEGPAY_PUSH_RESPONSE", Steps.THIRDPARTY_RESPONSE, 200, "http://PegPayTelecomsApi/PostTransaction", data)

    //  await Helpers.WalletHelper.LogMMTransaction(userId, VendorTranId, TranType, phone, TranAmount, req_body, data)
    var json = parser.toJson(data);
    var dat = JSON.parse(json);
    var transactionResult = dat['soap:Envelope']['soap:Body']['PostTransactionResponse']['PostTransactionResult'];
    console.log(`transactionResult`, transactionResult);
    const description = transactionResult['StatusDescription'];
    const statusCode = transactionResult['StatusCode'];
    return {
      statusCode,
      description,
      rawBody: dat
    }
  }

  public async makeMMPullRequest(userId: string, VendorTranId: string, TranAmount: string, SessionId: string, phone: string) {
    phone = phone.replace("+", "");
    const tel = await this.getProvider(phone);
    if (!tel) return { statusCode: 400, message: "Invalid phone format, phone number must start with 256" };


    const VendorCode = process.env.VENDOR_PULL
    const Password = process.env.PULL_PASSWORD
    phone = phone.replace("+", "")


    const FromTelecom = tel;
    const ToTelecom = tel;
    const PaymentCode = "2";
    const PaymentDate = new Date().toLocaleDateString()
    const Telecom = tel;
    const CustomerRef = "BT-0001";
    const CustomerName = "MUDA";
    const TranCharge = "0";
    const ToAccount = phone;
    const FromAccount = phone;
    const Narration = "muda pay";
    const ip = "";
    const TranType = "PULL";
    const dataToSign = CustomerRef + CustomerName + FromTelecom + ToTelecom + VendorTranId + VendorCode + Password + PaymentDate + TranType + PaymentCode + TranAmount + FromAccount + ToAccount;

    const signature = await this.getSignatureToVerify(dataToSign);



    const reqBody = `
      <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
        <Body>
          <PostTransaction xmlns="http://PegPayTelecomsApi/">
            <trans>
              <SessionId>${SessionId}</SessionId>
              <Narration>muda pay</Narration>
              <IP></IP>
              <DigitalSignature>${signature}</DigitalSignature>
              <FromTelecom>${tel}</FromTelecom>
              <ToTelecom>${tel}</ToTelecom>
              <PaymentCode>2</PaymentCode>
              <VendorCode>${process.env.VENDOR_PULL}</VendorCode>
              <Password>${process.env.PULL_PASSWORD}</Password>
              <PaymentDate>${new Date().toLocaleDateString()}</PaymentDate>
              <CustomerRef>BT-0001</CustomerRef>
              <CustomerName>MUDA</CustomerName>
              <TranAmount>${TranAmount}</TranAmount>
              <TranCharge>0</TranCharge>
              <VendorTranId>${VendorTranId}</VendorTranId>
              <ToAccount>${phone}</ToAccount>
              <FromAccount>${phone}</FromAccount>
              <TranType>PULL</TranType>
            </trans>
          </PostTransaction>
        </Body>
      </Envelope>`;
    new Model().saveTransactionLog(VendorTranId, "PEGPAY_PULL_REQUEST", Steps.THIRDPARTY_REQUEST, 202, "http://PegPayTelecomsApi/PostTransaction", reqBody)
    const data = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/PostTransaction");
    new Model().saveTransactionLog(VendorTranId, "PEGPAY_PULL_RESPONSE", Steps.THIRDPARTY_RESPONSE, 200, "http://PegPayTelecomsApi/PostTransaction", data)


    //  await Helpers.WalletHelper.LogMMTransaction(userId, VendorTranId, TranType, phone, TranAmount, req_body, data)
    var json = parser.toJson(data);
    var dat = JSON.parse(json);
    var transactionResult = dat['soap:Envelope']['soap:Body']['PostTransactionResponse']['PostTransactionResult'];
    console.log(transactionResult);
    const description = transactionResult['StatusDescription'];
    const statusCode = transactionResult['StatusCode'];

    return {
      statusCode,
      description,
      rawBody: dat
    }

  }

  public async getTransactionDetails(transID: string, opType: string = "PUSH") {
    let account = process.env.VENDOR_PUSH;
    let password = process.env.PUSH_PASSWORD;
    if (opType == "PULL") {
      account = process.env.VENDOR_PULL;
      password = process.env.PULL_PASSWORD;
    }

    const reqBody = `
      <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
        <Body>
          <GetTransactionDetails xmlns="http://PegPayTelecomsApi/">
            <VendorCode>${account}</VendorCode>
            <password>${password}</password>
            <vendorTranId>${transID}</vendorTranId>
          </GetTransactionDetails>
        </Body>
      </Envelope>`;

    try {
      const logData = {
        transID: transID,
        opType: opType
      }
      // Send SOAP request
      console.log(`GetTransactionDetailsFor`, transID)
      new Model().saveTransactionLog(transID, "PEGPAY_TRANSACTION_STATUS", Steps.TRANS_STATUS_CHECK, 202, "http://PegPayTelecomsApi/GetTransactionDetails", logData)
      const data = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/GetTransactionDetails");
      new Model().saveTransactionLog(transID, "PEGPAY_TRANSACTION_STATUS", Steps.TRANS_STATUS_RESPONSE, 200, "http://PegPayTelecomsApi/GetTransactionDetails", data)


      // Convert XML response to JSON
      const json = parser.toJson(data);
      const parsedData = JSON.parse(json);

      // Extract transaction details
      const transactionResult = parsedData['soap:Envelope']['soap:Body']['GetTransactionDetailsResponse']['GetTransactionDetailsResult'];
      console.log(`transactionResult`, transactionResult)
      const statusCode = transactionResult['StatusCode'];
      //  const description = transactionResult['StatusDescription'];
      const rawDescription = transactionResult['StatusDescription'];
      let description = typeof rawDescription === 'string' ? rawDescription : '';


      if (statusCode == "16" && opType == "PULL" && description == "") {
        description = "TRANSACTION TIMEOUT AT TELECOM"
      }

      return {
        statusCode,
        description,
        rawBody: parsedData
      };
    } catch (error) {
      new Model().saveTransactionLog(transID, "PEGPAY_TRANSACTION_STATUS", Steps.TRANS_STATUS_RESPONSE, 500, "http://PegPayTelecomsApi/GetTransactionDetails", {
        transID: transID,
        opType: opType,
        error: error
      })
      console.error('Error fetching transaction details:', error);
      return {
        statusCode: 'ERROR',
        description: 'Failed to fetch transaction details'
      };
    }
  }

  public async validatePhoneNumber(phoneNumber: string) {
    const vendorPull = process.env.VENDOR_PULL || '';
    const pullPassword = process.env.PULL_PASSWORD || '';
    const dataToSign = phoneNumber + vendorPull + pullPassword;
    const signature = await this.getSignatureToVerify(dataToSign);
    console.log("signature", signature)
    const reqBody = `
      <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
        <Body>
          <ValidatePhoneNumber xmlns="http://PegPayTelecomsApi/">
            <phoneNumber>${phoneNumber}</phoneNumber>
            <VendorCode>${process.env.VENDOR_PULL}</VendorCode>
            <password>${process.env.PULL_PASSWORD}</password>
            <signature>${signature}</signature>
          </ValidatePhoneNumber>
        </Body>
      </Envelope>`;

    const response = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/ValidatePhoneNumber");
    const parsedResponse = JSON.parse(parser.toJson(response));

    return parsedResponse['soap:Envelope']['soap:Body']['ValidatePhoneNumberResponse']['ValidatePhoneNumberResult']['Name'];
  }

  public async getPullBalance(): Promise<ThirdPartyBalance> {
    try {
      const vendorPull = process.env.VENDOR_PULL || '';
      const pullPassword = process.env.PULL_PASSWORD || '';
      const dataToSign = vendorPull + pullPassword;
      const signature = await this.getSignatureToVerify(dataToSign);
      console.log("signature", signature)
      const reqBody = `
  <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
    <Body>
        <GetAccountBalance xmlns="http://PegPayTelecomsApi/">
            <vendorcode>${vendorPull}</vendorcode>
            <password>${pullPassword}</password>
        </GetAccountBalance>
    </Body>
</Envelope>
`;
      console.log("getPullBalance", reqBody)

      // Send SOAP request
      const data = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/GetAccountBalance");
      console.log(`getPullBalanceResponse`, data)

      // Convert XML response to JSON
      const json = parser.toJson(data);
      const parsedData = JSON.parse(json);
      console.log("parsedData", parsedData)

      // Extract balance information
      const balanceResult = parsedData['soap:Envelope']['soap:Body']['GetAccountBalanceResponse']['GetAccountBalanceResult'];
      console.log("balanceResult", balanceResult)

      return {
        balance: balanceResult['Balance'],
        statusCode: balanceResult['StatusCode']
      };
    } catch (error) {
      console.error('Error fetching account balance:', error);
      return {
        balance: "0.0000",
        statusCode: "400"
      };
    }
  }


  public async getPushBalance(): Promise<ThirdPartyBalance> {
    try {
      const vendorPush = process.env.VENDOR_PUSH || '';
      const pushPassword = process.env.PUSH_PASSWORD || '';
      const dataToSign = vendorPush + pushPassword;
      const signature = await this.getSignatureToVerify(dataToSign);
      console.log("signature", signature)
      const reqBody = `
       <Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/">
    <Body>
        <GetAccountBalance xmlns="http://PegPayTelecomsApi/">
            <vendorcode>${vendorPush}</vendorcode>
            <password>${pushPassword}</password>
        </GetAccountBalance>
    </Body>
</Envelope>
`;

      // Send SOAP request
      console.log("getPushBalance", reqBody)
      const data = await this.sendSoapRequest(reqBody, "http://PegPayTelecomsApi/GetAccountBalance");
      console.log(`getPushBalanceResponse`, data)

      // Convert XML response to JSON
      const json = parser.toJson(data);
      const parsedData = JSON.parse(json);

      // Extract balance information
      const balanceResult = parsedData['soap:Envelope']['soap:Body']['GetAccountBalanceResponse']['GetAccountBalanceResult'];

      return {
        balance: balanceResult['Balance'],
        statusCode: balanceResult['StatusCode']
      };
    } catch (error) {
      console.error('Error fetching account balance:', error);
      return {
        balance: "0.0000",
        statusCode: "400"
      };
    }
  }
}
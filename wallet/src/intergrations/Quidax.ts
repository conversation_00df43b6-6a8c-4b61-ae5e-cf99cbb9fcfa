import RequestHelper from "../helpers/request.helper";

class Quidax {
  private mainURL: string;
  private mainRequestHeader: Record<string, string>;

  constructor() {
    this.mainURL = process.env.QUIDAX_URL ?? "";
    this.mainRequestHeader = {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.QUIDAX_SECRET}`,
    };
  }

  private async request(method: "get" | "post" | "put", endpoint: string, data?: any) {
    RequestHelper.setEndpoint(`${this.mainURL}${endpoint}`);
    RequestHelper.setHeaders(this.mainRequestHeader);
    if (data) RequestHelper.setData(data);

    const response = await RequestHelper[`${method}Request`]();
    const errors = await RequestHelper.getErrors();
    return errors?.status === "error" ? errors : await RequestHelper.getResults();
  }

   // Sub-Accounts
   public createSubAccount(email: string, first_name: string, last_name: string) {
    return this.request("post", "users", { email, first_name, last_name });
  }

  public fetchParentAccount(user = "me") {
    return this.request("get", `users/${user}`);
  }

  public editSubAccountDetails(user = "me", first_name: string, last_name: string) {
    return this.request("put", `users/${user}`, { first_name, last_name });
  }

  public fetchAllSubAccounts() {
    return this.request("get", "users");
  }

  public fetchSubAccountDetails(user_id: string) {
    return this.request("get", `users/${user_id}`);
  }

  // Fees & Transactions
  public getCryptoWithdrawalFees(currency: string, network: string) {
    return this.request("get", `fee?currency=${currency}&network=${network}`);
  }

  // Bank Operations
  public getBanks() {
    return this.request("get", "banks?per_page=100");
  }

  public verifyBankAccountDetails(fund_uid: string, fund_uid2: string, currency: string) {
    return this.request("post", "banks/verify_account", { fund_uid, fund_uid2, currency });
  }

  public createWithdraw(
    user = "me", 
    currency: string, 
    amount: string, 
    transaction_note: string, 
    narration: string, 
    fund_uid: string, //account
    fund_uid2:string, //bank
    reference: string,
    network:string='bep20'
  ) {
    return this.request("post", `users/${user}/withdraws`, {
      currency,
      amount,
      transaction_note,
      narration,
      fund_uid,
      fund_uid2,
      reference,
      network
    });
  }

  public getWithdrawDetailsById(user = "me", id: string) {
    return this.request("get", `users/${user}/withdraws/${id}`);
  }

  public getWithdrawDetailsByReference(user = "me", reference: string) {
    return this.request("get", `users/${user}/withdraws/reference/${reference}`);
  }

  public handleWithdrawWebhooks(data: any) {
    switch (data?.event) {
      case "withdraw.successful":
        // Handle successful withdrawal
        break;
      case "withdraw.rejected":
        // Handle rejected withdrawal
        break;
    }
    return data;
  }


  public getUserWallet(user = "me") {
    return this.request("get", `users/${user}/wallets`);
  }
  public getPaymentAddress(user = "me", currency: string) {
    return this.request("get", `users/${user}/wallets/${currency}/address`);
  }

  public createPaymentAddress(user = "me", currency: string, network: string) {
    return this.request("post", `users/${user}/wallets/${currency}/addresses?network=${network}`);
  }

  public validatePaymentAddress(currency: string, address: string) {
    return this.request("get", `${currency}/${address}/validate_address`);
  }

  // Currency Swap
  public makeCryptoFiatSwap(user = "me", from_currency: string, to_currency: string, from_amount: any) {
    return this.request("post", `users/${user}/swap_quotation`, { from_currency, to_currency, from_amount });
  }

  public confirmCryptoFiatSwap(user = "me", quotation_id: string) {
    return this.request("post", `users/${user}/swap_quotation/${quotation_id}/confirm`);
  }

  public refreshCryptoFiatSwap(
    user = "me", 
    quotation_id: string, 
    from_currency: string, 
    to_currency: string, 
    from_amount: any
  ) {
    return this.request("post", `users/${user}/swap_quotation/${quotation_id}/refresh`, { from_currency, to_currency, from_amount });
  }

  public fetchCryptoFiatSwapTransaction(user = "me") {
    return this.request("get", `users/${user}/swap_transactions`);
  }

  public fetchSingleCryptoFiatSwapTransaction(user = "me", swap_transaction_id: string) {
    return this.request("get", `users/${user}/swap_transactions/${swap_transaction_id}`);
  }
}

export default new Quidax();

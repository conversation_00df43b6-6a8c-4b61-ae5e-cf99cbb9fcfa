// IntegrationHelper.ts

export type SupportedCurrency = "UGX" | "KES" | "NGN" | "GHS";
export type ServiceName = "MOBILE_MONEY" | "BANK_TRANSFER" | "CARD" | "OTHER";
export type PaymentStatus = "SUCCESS" | "FAILED" | "INSUFFICIENT_FUNDS";
const LiveIssuer = "GCRVR6D4TL4SDI5JBOWBTQSSY3GZKBMSO5J2DQMCEPICTJ4GZ77TN3S4"
export interface PaymentRequest {
    account_number: string;
    amount: number;
    currency: SupportedCurrency;
    service_name: ServiceName;
    status?: PaymentStatus; // optional: force an outcome in tests
    pair?: string;          // optional label for FX tests; not used in logic
}

export interface MockResponse {
    status: number; // HTTP-like
    message: string;
    data: PaymentRequest;
}

export default class IntegrationHelper {
    private static readonly SUCCESS_BANK = "*********";
    private static readonly LIMIT = 100_000; // > LIMIT => INSUFFICIENT_FUNDS

    // ---- grouped samples for quick tests (not used in logic) ----
    public readonly successSamples: PaymentRequest[] = [
        { account_number: "*********", amount: 10_000, currency: "UGX", service_name: "BANK_TRANSFER" },
        { account_number: "************", amount: 5_000, currency: "UGX", service_name: "MOBILE_MONEY", status: "SUCCESS" },
        { account_number: "************", amount: 20_000, currency: "KES", service_name: "OTHER", status: "SUCCESS" },
    ];

    public readonly failedSamples: PaymentRequest[] = [
        { account_number: "*********", amount: 10_000, currency: "NGN", service_name: "BANK_TRANSFER" },
        { account_number: "************", amount: 30_000, currency: "GHS", service_name: "MOBILE_MONEY", status: "FAILED" },
        { account_number: "*************", amount: 50_000, currency: "NGN", service_name: "CARD" },
    ];

    public readonly insufficientSamples: PaymentRequest[] = [
        { account_number: "************", amount: 700_000, currency: "UGX", service_name: "MOBILE_MONEY" },
        { account_number: "************", amount: 2_000_000, currency: "KES", service_name: "OTHER" },
        { account_number: "*********", amount: 1_000_000, currency: "GHS", service_name: "BANK_TRANSFER" },
    ];


    public resolveStatus(req: PaymentRequest): PaymentStatus {
        if (req.status) return req.status;

        if (req.service_name === "BANK_TRANSFER" && req.account_number === IntegrationHelper.SUCCESS_BANK) {
            return "SUCCESS";
        }

        if (req.service_name === "MOBILE_MONEY" && req.account_number.includes("234567")) {
            return "SUCCESS";
        }

        if (req.amount > IntegrationHelper.LIMIT) {
            return "INSUFFICIENT_FUNDS";
        }

        return "FAILED";
    }

    public makeCollectionResponse(req: PaymentRequest) {
        console.log(`makeCollectionResponse`, req)
        if (process.env.STELLAR_COLLECTIONS_ISSUER_PUBLIC == LiveIssuer) {
            return this.mapCollectionResponse(400, "ACCESS DENIED", "FAILED")
        }
        const resolved = this.resolveStatus(req);

        switch (resolved) {
            case "SUCCESS":
                return this.mapCollectionResponse(0, "SUCCESS", "SUCCESS")
            case "INSUFFICIENT_FUNDS":
                return this.mapCollectionResponse(400, "INSUFFICIENT FUNDS", "FAILED")
            case "FAILED":
            default:
                return this.mapCollectionResponse(400, "ACCOUNT NOT ALLOWED TO COLLECT", "FAILED")
        }
    }

    public makePushResponse(req: PaymentRequest) {
        const resolved = this.resolveStatus(req);
        console.log(`makeCollectionResponse`, req)
        if (process.env.STELLAR_COLLECTIONS_ISSUER_PUBLIC == LiveIssuer) {
            return this.mapCollectionResponse(400, "ACCESS DENIED", "FAILED")
        }
        switch (resolved) {
            case "SUCCESS":
                return this.mapCollectionResponse(200, "SUCCESS", "SUCCESS")
            case "INSUFFICIENT_FUNDS":
                return this.mapCollectionResponse(400, "INSUFFICIENT FUNDS", "FAILED")
            case "FAILED":
            default:
                return this.mapCollectionResponse(400, "ACCOUNT NOT ALLOWED TO PUSH", "FAILED")
        }
    }

    mapCollectionResponse(statusCode: any, description: any, transStatus: any = null, data: any = null) {
        return {
            statusCode: statusCode,
            description: description,
            data: data,
            transStatus: transStatus
        }
    }

}

import axios, { AxiosInstance } from 'axios';
import jwt, { SignOptions } from 'jsonwebtoken';
import fs from 'fs';
import crypto from 'crypto';
import { getItem, setItem } from '../helpers/connectRedis';

const UTILA_BASE = 'https://api.utila.io/v2';
const UTILA_SA_EMAIL = process.env.UTILIA_SA_EMAIL;
const UTILA_PRIVATE_KEY_PATH = './keys/private_key.pem';
const UTILA_VAULT_ID = '50c18db6c59f';
const UTILA_WALLET_ID = '156acae8b285';
const BEARER_CACHE = 'utila:bearer';
const GAS_WALLET_ID = '156acae8b285';

const PRIVATE_KEY = fs.readFileSync(UTILA_PRIVATE_KEY_PATH, 'utf8');

async function safeApiCall<T>(fn: () => Promise<any>): Promise<{ data: any, error: any }> {
  try {
    const response = await fn();
    return { data: response.data, error: null };
  } catch (error: any) {
    const err = {
      code: error.response?.data.code || error.response?.status,
      message: error.response?.data?.message || error.message,
      details: error.response?.data?.details || null
    };
    return { data: err, error: err };
  }
}

// ---------- authentication (bearer + Redis) ------------

async function getBearer(): Promise<string> {
  const cached = await getItem(BEARER_CACHE);
  // if (cached) return cached;

  const now = Math.floor(Date.now() / 1000);
  const payload = { sub: UTILA_SA_EMAIL, aud: 'https://api.utila.io/', iat: now, exp: now + 3600 };
  const options: SignOptions = { algorithm: 'RS256' };
  const token = jwt.sign(payload, PRIVATE_KEY.replace(/\\n/g, '\n'), options);

  await setItem(BEARER_CACHE, token);
  return token;
}

// ---------- Axios instance ------------
const api: AxiosInstance = axios.create({ baseURL: UTILA_BASE });

api.interceptors.request.use(async cfg => {
  cfg.headers = cfg.headers || {};
  cfg.headers.Authorization = `Bearer ${await getBearer()}`;
  return cfg;
});


export const createTokenApproval = async ({
  tokenAddress,
  sourceWalletId,
  network = 'base-mainnet',
  vaultId = UTILA_VAULT_ID
}: {
  tokenAddress: string;
  sourceWalletId: string;
  network?: string;
  vaultId?: string;
}) => {
  const approveSelector = '0x095ea7b3';
  const gasWalletAddress = `vaults/${vaultId}/wallets/${GAS_WALLET_ID}`;
  const maxAmount = '115792089237316195423570985008687907853269984665640564039457584007913129639935';
  
  const paddedAddress = gasWalletAddress.slice(-40).padStart(64, '0');
  const paddedAmount = BigInt(maxAmount).toString(16).padStart(64, '0');
  const calldata = approveSelector + paddedAddress + paddedAmount;

  const payload = {
    details: {
      evmTransaction: {
        network: `networks/${network}`,
        fromAddress: `vaults/${vaultId}/wallets/${sourceWalletId}`,
        toAddress: tokenAddress,
        data: calldata
      }
    },
    priority: 'NORMAL',
    requestId: `approval_${Date.now()}_${sourceWalletId}`
  };

  return safeApiCall(() => api.post(`/vaults/${vaultId}/transactions:initiate`, payload));
};

// Tron network
export const createTronTokenApproval = async ({
  tokenAddress,
  sourceWalletId,
  vaultId = UTILA_VAULT_ID
}: {
  tokenAddress: string;
  sourceWalletId: string;
  vaultId?: string;
}) => {
  const approveSelector = '0x095ea7b3';
  const gasWalletAddress = `vaults/${vaultId}/wallets/${GAS_WALLET_ID}`;
  const maxAmount = '115792089237316195423570985008687907853269984665640564039457584007913129639935';
  
  const paddedAddress = gasWalletAddress.slice(-40).padStart(64, '0');
  const paddedAmount = BigInt(maxAmount).toString(16).padStart(64, '0');
  const calldata = approveSelector + paddedAddress + paddedAmount;

  const payload = {
    details: {
      tronTriggerSmartContract: {
        network: "networks/tron-mainnet",
        ownerAddress: `vaults/${vaultId}/wallets/${sourceWalletId}`,
        contractAddress: tokenAddress,
        callValue: "0",
        data: calldata
      }
    },
    priority: 'NORMAL',
    requestId: `tron_approval_${Date.now()}_${sourceWalletId}`
  };

  return safeApiCall(() => api.post(`/vaults/${vaultId}/transactions:initiate`, payload));
};

// Get all wallet IDs
export const getAllUserWalletIds = async (): Promise<string[]> => {
  const { data } = await listWalletsForVault();
  return data?.wallets?.map((wallet: any) => wallet.name.split('/').pop()) || [];
};
export const listVaults = () => safeApiCall(() => api.get('/vaults'));

export const getSupportedNetworks = () =>
  safeApiCall(() => api.get(`/networks`));

export const createWallet = ({ name, networks }: { name?: string, networks?: string[] } = {}) => {
  const vaultId = UTILA_VAULT_ID;
  // const networks = ['ethereum-mainnet', 'tron-mainnet', 'bitcoin-mainnet'];
  const displayName = name || 'SDK demo';

  return safeApiCall(() =>
    api.post(`/vaults/${vaultId}/wallets`, { displayName, networks })
  );
};

export const listWalletsForVault = async ({
  name
}: { walletId?: string; name?: string } = {}) => {
  const vaultId = UTILA_VAULT_ID;
  console.log('listWalletsForVault', vaultId, 'filter:', name);
  
  let url = `/vaults/${vaultId}/wallets?view=FULL`;
  
  if (name) {
    // Use regex filter to find wallets with names starting with the specified pattern
    const filter = `regex(displayName, "^${name}")`;
    url += `&filter=${encodeURIComponent(filter)}`;
  }
  
  return await safeApiCall(() => api.get(url));
}


export const getUserWallet = ({
  vaultId = UTILA_VAULT_ID,
  walletId = UTILA_WALLET_ID
}: { vaultId?: string; walletId?: string } = {}) =>
  safeApiCall(() => api.get(`/vaults/${vaultId}/wallets/${walletId}`));

export const queryBalances = (vaultId: string=UTILA_VAULT_ID) =>
  safeApiCall(() => api.post(`/vaults/${vaultId}:queryBalances`, {}));

export const getWalletBalances = ({
  vaultId = UTILA_VAULT_ID,
  walletId,
  filter,
  pageSize,
  pageToken,
  includeReferencedResources
}: { 
  vaultId?: string; 
  walletId: string;
  filter?: string;
  pageSize?: number;
  pageToken?: string;
  includeReferencedResources?: boolean;
}) => {
  const payload: any = {};
  
  if (filter) payload.filter = filter;
  if (pageSize) payload.pageSize = pageSize;
  if (pageToken) payload.pageToken = pageToken;
  if (includeReferencedResources) payload.includeReferencedResources = includeReferencedResources;
  
  return safeApiCall(() => api.post(`/vaults/${vaultId}/wallets/${walletId}:queryBalances`, payload));
};

export const createWalletAddress = ({
  walletId = UTILA_WALLET_ID,
  network
}: { walletId?: string; network?: string } = {}) => {
  const vaultId = UTILA_VAULT_ID;
  return safeApiCall(() =>
    api.post(`/vaults/${vaultId}/wallets/${walletId}/addresses`, network ? { network } : {})
  );
};

export const loadResource = (resource: string) =>
  safeApiCall(() => api.get(resource));

export const listWalletAddresses = ({
  vaultId = UTILA_VAULT_ID,
  walletId = UTILA_WALLET_ID
}: { vaultId?: string; walletId?: string } = {}) =>
  safeApiCall(() => api.get(`/vaults/${vaultId}/wallets/${walletId}/addresses`));

export const createAddress = ({
  walletId = UTILA_WALLET_ID,
  network
}: { vaultId?: string; walletId?: string; network?: string } = {}) => {
  const vaultId = UTILA_VAULT_ID;
  return safeApiCall(() =>
    api.post(`/vaults/${vaultId}/wallets/${walletId}/addresses`, network ? { network } : {})
  );
};
export const batchGetAssets = () =>
  safeApiCall(() => api.get(`/vaults/${UTILA_VAULT_ID}/assets`));

export const initiateSponsoredTransfer = ({
  destination,
  asset,
  amount,
  sourceWallet,
  note = 'Sponsored transfer',
  vaultId = UTILA_VAULT_ID,
  requestId = 'sponsored_transfer'
}: {
  destination: string;
  asset: string;
  amount: string;
  sourceWallet: string;
  note?: string;
  vaultId?: string;
  requestId?: string;
}) =>
  safeApiCall(async () => {
    const sponsorWallet = GAS_WALLET_ID;

    const payload = {
      "details": {
        "sponsoredAssetTransfer": {
          "asset": asset,
          "amount": amount,
          "source": `vaults/${vaultId}/wallets/${sourceWallet}`,
          "destination": destination,
          "sponsor": `vaults/${vaultId}/wallets/${sponsorWallet}`,
          "memo": note
        }
      },
      "priority": "NORMAL",
      "requestId": requestId
    };
    console.log("payload", payload)
    return await api.post(`/vaults/${vaultId}/transactions:initiate`, payload);
  });

export const initiatePayout = ({
  destination,
  asset,
  amount,
  memo = 'SDK demo',
  payFeeFromAmount = false,
  vaultId = UTILA_VAULT_ID,
  sourceWallet = UTILA_WALLET_ID,
  note = 'payment',
  requestId = 'payment',
  validateOnly = false
}: {
  destination: string;
  asset: string;
  amount: string;
  memo?: string;
  payFeeFromAmount?: boolean;
  vaultId?: string;
  sourceWallet?: string;
  note?: string;
  requestId?: string;
  validateOnly?: boolean;
}) =>
  safeApiCall(async () => {


    const payload = {
      "details": {
        "assetTransfer": {
          "asset": asset,
          "amount": amount,
          "source": `vaults/${vaultId}/wallets/${sourceWallet}`,
          "destination": destination,
          "payFeeFromAmount": payFeeFromAmount,
          "memo": memo
        }
      },
      "priority": "NORMAL",
      "note": requestId,
      "validateOnly": validateOnly,
      "requestId": requestId
    }
    console.log("payload", payload)
    return await api.post(`/vaults/${vaultId}/transactions:initiate`, payload)
  });

// ---------- Webhook signature check ------------

export function verifyWebhook(
  signature: string,
  rawPayload: Buffer | string,
  utilaPubKey: string = DEFAULT_UTILA_PUB
): boolean {
  const verifier = crypto.createVerify('sha512');
  verifier.update(rawPayload);
  verifier.end();
  return verifier.verify(
    { key: utilaPubKey, padding: crypto.constants.RSA_PKCS1_PSS_PADDING },
    Buffer.from(signature, 'base64')
  );
}


// balances.ts

// 1) Asset-ID constants
export const ASSET_IDS = {
  BNB_MAINNET_NATIVE:      'assets/native.bnb-smart-chain-mainnet',
  TRC20_USDT_TRON:         'assets/trc20.tron-mainnet.TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
  TRON_MAINNET_NATIVE:     'assets/native.tron-mainnet',
  BSC_TESTNET_ERC20:       'assets/erc20.bnb-smart-chain-testnet.******************************************',
  BSC_MAINNET_USDC_ERC20:  'assets/erc20.bnb-smart-chain-mainnet.******************************************',
  BASE_MAINNET_ERC20:      'assets/erc20.base-mainnet.******************************************',
  BASE_MAINNET_NATIVE:     'assets/native.base-mainnet',
} as const;

export type AssetId = typeof ASSET_IDS[keyof typeof ASSET_IDS];

// 2) Mapping from full ID → human-friendly code
export const ASSET_CODE_MAP: Record<AssetId, string> = {
  [ASSET_IDS.BNB_MAINNET_NATIVE]:     'BNB',
  [ASSET_IDS.TRC20_USDT_TRON]:        'USDT_TRON',
  [ASSET_IDS.TRON_MAINNET_NATIVE]:    'TRX',
  [ASSET_IDS.BSC_TESTNET_ERC20]:      'USDT_BSC_TESTNET',
  [ASSET_IDS.BSC_MAINNET_USDC_ERC20]: 'USDC_BSC',
  [ASSET_IDS.BASE_MAINNET_ERC20]:     'USDC_BASE',
  [ASSET_IDS.BASE_MAINNET_NATIVE]:    'BASE',
};

// 3) Raw balance shape
export interface RawBalance {
  asset: AssetId;
  value: string;
  rawValue: string;
}

// 4) Formatted balance for console output
export interface FormattedBalance {
  ASSET: string;
  NETWORK: string;
  BALANCE: string;
}

/**
 * Turn raw balances into { ASSET, NETWORK, BALANCE } rows.
 */
export function formatBalances(balances: RawBalance[]): FormattedBalance[] {
  return balances.map(({ asset, value }) => {
    // strip leading "assets/"
    const stripped = asset.replace(/^assets\//, '');
    // ["native","bnb-smart-chain-mainnet", ...] or ["trc20","tron-mainnet", ...]
    const parts = stripped.split('.');
    const assetCode = parts[0];
    const network   = parts[1];
    // lookup friendly code or fallback to the raw assetCode
    const code = ASSET_CODE_MAP[asset] ?? assetCode.toUpperCase();
    return {
      ASSET:   code,
      NETWORK: network,
      BALANCE: value,
    };
  });
}

/**
 * Format wallet balances from getWalletBalances response
 */
export function formatWalletBalances(walletBalancesResponse: any): FormattedBalance[] {
  if (!walletBalancesResponse?.data?.walletBalances) {
    return [];
  }

  const balances = walletBalancesResponse.data.walletBalances;
  
  return balances.map((balance: any) => {
    const asset = balance.asset;
    const value = balance.value || '0';
    
    // strip leading "assets/"
    const stripped = asset.replace(/^assets\//, '');
    // ["native","bnb-smart-chain-mainnet", ...] or ["trc20","tron-mainnet", ...]
    const parts = stripped.split('.');
    const assetCode = parts[0];
    const network = parts[1];
    
    // lookup friendly code or fallback to the raw assetCode
    // Type assertion to handle the any type from API response
    const code = ASSET_CODE_MAP[asset as AssetId] ?? assetCode.toUpperCase();
    
    return {
      ASSET: code,
      NETWORK: network,
      BALANCE: value,
    };
  });
}

/**
 * Get formatted wallet balances for a specific wallet
 */
export async function getFormattedWalletBalances(walletId: string): Promise<FormattedBalance[]> {
  const walletBalancesResponse = await getWalletBalances({ walletId });
  return formatWalletBalances(walletBalancesResponse);
}

 
export async function getFormattedBalances(): Promise<any> {
  const { data: { balances } } = await queryBalances();
  return formatBalances(balances as RawBalance[]);
}



export const DEFAULT_UTILA_PUB = `-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAtKQFy6rsWf8rlmKmGwiZ
RILeMB4UWjS47fAprIe6mxYjY8anb59VVNoI5mOl0S+i9pFkXTUPQIzFKVXxoH8W
HZwbOH5we6F8DtQnhK0rjoCIJEnMDMR3aT0mDJPfGjtDSh4K4i8LyUYNTtJz85tD
EBXpOu8iphY+GKGppqK5B4web0anz+B3vI8wDP0UQ5/Jyk7ZZMOic61Jm9a64Cm2
L9eoZFsFo9DY6qLIVWl2zazFK6z+l6U0n/VYwy5qnUNGoEBjeBYtUlKXXkYEETVV
qws12ZSBJHKPT8zRlJEwtYBaM/zPHpjY/Bwwp6FjSco4uMssvw2smdG5AxYJhn4l
VUbc3hEtHGXpwDBnHIUMnFjtpFsf2U8ybBi2z4bJAkQd7z5YnBCkci3/HZYk+6Sk
DNU1lhnwhK5XYO1udQavUojJY8Apn6wDN1qRDPvQ+yqn8GmvUEM/bqlOOSI8IM9U
Y9iFcrGNCYi0OUa+mDAjqHRbHwcZQW02+vXBv4tL2bTpWVtx5aMJ5AeGjyLyHtSh
BsW1fczVUihmQisY2rxcbVtMZ7opf79F0GUR9JZDSob/6DFbwrVtbKBYJHFlrudx
tRAnOkn/PjHBJ/VgK5BgTFYlj+YDbjARiel+MSFrZiWbxrOUgXOW0jCifsXC0pm3
yKAZihS8RBFU2ldAb1Ckn/kCAwEAAQ==
-----END PUBLIC KEY-----`;

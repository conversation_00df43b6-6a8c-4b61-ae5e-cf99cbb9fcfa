-- Stellar Trading Database Schema
-- This schema supports on-chain orders with database tracking

-- Table to track Stellar DEX orders
CREATE TABLE stellar_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(255) UNIQUE NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    stellar_offer_id VARCHAR(255), -- Stellar DEX offer ID
    selling_asset VARCHAR(20) NOT NULL,
    selling_issuer VARCHAR(255),
    buying_asset VARCHAR(20) NOT NULL,
    buying_issuer VARCHAR(255),
    amount DECIMAL(20,7) NOT NULL,
    price DECIMAL(20,7) NOT NULL,
    filled_amount DECIMAL(20,7) DEFAULT 0,
    order_type ENUM('buy', 'sell') NOT NULL,
    status ENUM('active', 'completed', 'cancelled', 'partially_filled', 'failed') DEFAULT 'active',
    stellar_hash VARCHAR(255), -- Transaction hash when order was placed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_client_orders (client_id, status),
    INDEX idx_pair_orders (selling_asset, buying_asset, status),
    INDEX idx_stellar_offer (stellar_offer_id),
    INDEX idx_created_at (created_at)
);

-- Table to track executed trades
CREATE TABLE stellar_trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trade_id VARCHAR(255) UNIQUE NOT NULL,
    order_id VARCHAR(255) NOT NULL,
    maker_id VARCHAR(255) NOT NULL, -- User who created the order
    taker_id VARCHAR(255) NOT NULL, -- User who took the order
    amount DECIMAL(20,7) NOT NULL,
    price DECIMAL(20,7) NOT NULL,
    stellar_hash VARCHAR(255), -- Transaction hash of the trade
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES stellar_orders(order_id),
    INDEX idx_maker_trades (maker_id),
    INDEX idx_taker_trades (taker_id),
    INDEX idx_order_trades (order_id),
    INDEX idx_created_at (created_at)
);

-- Table to track swaps/path payments
CREATE TABLE stellar_swaps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    swap_id VARCHAR(255) UNIQUE NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    source_asset VARCHAR(20) NOT NULL,
    source_issuer VARCHAR(255),
    destination_asset VARCHAR(20) NOT NULL,
    destination_issuer VARCHAR(255),
    source_amount DECIMAL(20,7) NOT NULL,
    destination_amount DECIMAL(20,7) NOT NULL,
    stellar_hash VARCHAR(255), -- Transaction hash of the swap
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_client_swaps (client_id),
    INDEX idx_created_at (created_at)
);

-- Table for audit logging of trading actions
CREATE TABLE trade_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255) NOT NULL,
    action VARCHAR(50) NOT NULL, -- CREATE_OFFER, TAKE_OFFER, CANCEL_ORDER, SWAP, etc.
    order_id VARCHAR(255),
    data JSON, -- Additional data about the action
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_client_logs (client_id),
    INDEX idx_action_logs (action),
    INDEX idx_created_at (created_at)
);

-- Table to track asset information (for reference)
CREATE TABLE stellar_assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_code VARCHAR(20) NOT NULL,
    asset_issuer VARCHAR(255),
    asset_type ENUM('native', 'credit_alphanum4', 'credit_alphanum12') DEFAULT 'credit_alphanum4',
    is_active BOOLEAN DEFAULT TRUE,
    display_name VARCHAR(100),
    description TEXT,
    home_domain VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_asset (asset_code, asset_issuer),
    INDEX idx_active_assets (is_active)
);

-- Insert some common assets
INSERT INTO stellar_assets (asset_code, asset_issuer, asset_type, display_name, description) VALUES
('XLM', '', 'native', 'Stellar Lumens', 'Native Stellar asset'),
('USDC', 'GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN', 'credit_alphanum4', 'USD Coin', 'USD stablecoin'),
('UGX', 'GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR', 'credit_alphanum4', 'Ugandan Shilling', 'Ugandan Shilling token'),
('KES', 'GCUKD6VBHTZJ4PLCBCP2A2SO5N6R4CWFAEM3FQUAAP6TD7XHCZ4YBTGS', 'credit_alphanum4', 'Kenyan Shilling', 'Kenyan Shilling token');

-- Example queries:

-- Get order book for UGX/USDC pair
-- SELECT * FROM stellar_orders 
-- WHERE selling_asset = 'UGX' AND buying_asset = 'USDC' AND status = 'active' 
-- ORDER BY price ASC;

-- Get client's trading history
-- SELECT o.*, t.trade_id, t.stellar_hash as trade_hash 
-- FROM stellar_orders o 
-- LEFT JOIN stellar_trades t ON o.order_id = t.order_id 
-- WHERE o.client_id = 'client123' 
-- ORDER BY o.created_at DESC;

-- Get trading volume for a pair in last 24h
-- SELECT 
--     COUNT(*) as trade_count,
--     SUM(amount) as total_volume,
--     AVG(price) as avg_price
-- FROM stellar_trades t
-- JOIN stellar_orders o ON t.order_id = o.order_id
-- WHERE o.selling_asset = 'UGX' AND o.buying_asset = 'USDC'
-- AND t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR); 
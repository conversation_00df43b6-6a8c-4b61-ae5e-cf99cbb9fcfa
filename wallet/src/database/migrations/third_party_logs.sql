-- Table: bava_aggregator.third_party_logs
-- Purpose: Logs all interactions with third-party services for auditing, debugging, and monitoring
-- This table tracks API calls, responses, and errors when interacting with external services

CREATE TABLE IF NOT EXISTS bava_aggregator.third_party_logs (
    id SERIAL PRIMARY KEY,
    client_id VARCHAR(255) NOT NULL,
    trans_id VARCHAR(255),
    service_name VARCHAR(100) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_method VARCHAR(10) NOT NULL,
    request_headers JSONB,
    request_payload JSONB,
    response_code INT,
    response_body JSONB,
    error_message TEXT,
    processing_time_ms INT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_third_party_logs_client_id ON bava_aggregator.third_party_logs(client_id);
CREATE INDEX IF NOT EXISTS idx_third_party_logs_trans_id ON bava_aggregator.third_party_logs(trans_id);
CREATE INDEX IF NOT EXISTS idx_third_party_logs_service_name ON bava_aggregator.third_party_logs(service_name);
CREATE INDEX IF NOT EXISTS idx_third_party_logs_status ON bava_aggregator.third_party_logs(status);
CREATE INDEX IF NOT EXISTS idx_third_party_logs_created_at ON bava_aggregator.third_party_logs(created_at);

-- Comments for better documentation
COMMENT ON TABLE bava_aggregator.third_party_logs IS 'Logs all interactions with third-party services for auditing and debugging';
COMMENT ON COLUMN bava_aggregator.third_party_logs.id IS 'Unique identifier for the log entry';
COMMENT ON COLUMN bava_aggregator.third_party_logs.client_id IS 'ID of the client making the request';
COMMENT ON COLUMN bava_aggregator.third_party_logs.trans_id IS 'Transaction ID if applicable';
COMMENT ON COLUMN bava_aggregator.third_party_logs.service_name IS 'Name of the third-party service (e.g., PegaPay, Stellar, etc.)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.endpoint IS 'API endpoint that was called';
COMMENT ON COLUMN bava_aggregator.third_party_logs.request_method IS 'HTTP method used (GET, POST, PUT, DELETE)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.request_headers IS 'Headers sent with the request (sensitive data redacted)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.request_payload IS 'Body of the request (sensitive data redacted)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.response_code IS 'HTTP status code received';
COMMENT ON COLUMN bava_aggregator.third_party_logs.response_body IS 'Response body received (sensitive data redacted)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.error_message IS 'Error message if the request failed';
COMMENT ON COLUMN bava_aggregator.third_party_logs.processing_time_ms IS 'Time taken to process the request in milliseconds';
COMMENT ON COLUMN bava_aggregator.third_party_logs.status IS 'Status of the request (PENDING, SUCCESS, FAILED)';
COMMENT ON COLUMN bava_aggregator.third_party_logs.created_at IS 'Timestamp when the log was created';
COMMENT ON COLUMN bava_aggregator.third_party_logs.updated_at IS 'Timestamp when the log was last updated'; 
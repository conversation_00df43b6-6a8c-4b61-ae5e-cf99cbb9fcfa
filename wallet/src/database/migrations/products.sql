-- Table: bava_aggregator.products
-- Purpose: Stores information about available payment products and services
-- This table defines the products that clients can use for transactions

CREATE TABLE IF NOT EXISTS bava_aggregator.products (
    id SERIAL PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    product_type VARCHAR(50) NOT NULL,
    service_provider VARCHAR(50) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    min_amount DECIMAL(15,2),
    max_amount DECIMAL(15,2),
    fee_type VARCHAR(20) NOT NULL DEFAULT 'PERCENTAGE', -- PERCENTAGE, FIXED, TIERED
    fee_value DECIMAL(10,4) NOT NULL DEFAULT 0,
    fee_currency VARCHAR(10),
    fee_cap DECIMAL(15,2),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    requires_approval BOOLEAN NOT NULL DEFAULT FALSE,
    processing_time_minutes INT,
    available_countries TEXT[], -- Array of country codes where this product is available
    integration_details JSONB, -- Provider-specific configuration
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_products_product_code ON bava_aggregator.products(product_code);
CREATE INDEX IF NOT EXISTS idx_products_product_type ON bava_aggregator.products(product_type);
CREATE INDEX IF NOT EXISTS idx_products_service_provider ON bava_aggregator.products(service_provider);
CREATE INDEX IF NOT EXISTS idx_products_currency ON bava_aggregator.products(currency);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON bava_aggregator.products(is_active);

-- Comments for better documentation
COMMENT ON TABLE bava_aggregator.products IS 'Stores information about available payment products and services';
COMMENT ON COLUMN bava_aggregator.products.id IS 'Unique identifier for the product';
COMMENT ON COLUMN bava_aggregator.products.product_code IS 'Unique code for the product (e.g., MOBILE_MONEY_UGX)';
COMMENT ON COLUMN bava_aggregator.products.name IS 'Display name of the product';
COMMENT ON COLUMN bava_aggregator.products.description IS 'Detailed description of the product';
COMMENT ON COLUMN bava_aggregator.products.product_type IS 'Type of product (e.g., COLLECTION, PAYOUT, WALLET)';
COMMENT ON COLUMN bava_aggregator.products.service_provider IS 'Third-party provider for this product (e.g., PEGAPAY, FLUTTERWAVE)';
COMMENT ON COLUMN bava_aggregator.products.currency IS 'Currency code for the product (e.g., UGX, USD)';
COMMENT ON COLUMN bava_aggregator.products.min_amount IS 'Minimum transaction amount allowed';
COMMENT ON COLUMN bava_aggregator.products.max_amount IS 'Maximum transaction amount allowed';
COMMENT ON COLUMN bava_aggregator.products.fee_type IS 'Type of fee calculation (PERCENTAGE, FIXED, TIERED)';
COMMENT ON COLUMN bava_aggregator.products.fee_value IS 'Fee value (percentage or fixed amount)';
COMMENT ON COLUMN bava_aggregator.products.fee_currency IS 'Currency for the fee if different from product currency';
COMMENT ON COLUMN bava_aggregator.products.fee_cap IS 'Maximum fee amount when using percentage';
COMMENT ON COLUMN bava_aggregator.products.is_active IS 'Whether the product is currently active';
COMMENT ON COLUMN bava_aggregator.products.requires_approval IS 'Whether transactions using this product require manual approval';
COMMENT ON COLUMN bava_aggregator.products.processing_time_minutes IS 'Expected processing time in minutes';
COMMENT ON COLUMN bava_aggregator.products.available_countries IS 'Array of country codes where this product is available';
COMMENT ON COLUMN bava_aggregator.products.integration_details IS 'JSON with provider-specific configuration details';
COMMENT ON COLUMN bava_aggregator.products.created_at IS 'Timestamp when the product was created';
COMMENT ON COLUMN bava_aggregator.products.updated_at IS 'Timestamp when the product was last updated';
COMMENT ON COLUMN bava_aggregator.products.created_by IS 'User who created the product';
COMMENT ON COLUMN bava_aggregator.products.updated_by IS 'User who last updated the product'; 
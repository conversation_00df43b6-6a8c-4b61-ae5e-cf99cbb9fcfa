import express from 'express';
import {
    createOffer,
    getOrderBook,
    takeOffer,
    executeSwap,
    cancelOrder,
    getTradingHistory,
    getUserOrders,
    healthCheck
} from '../controllers/trading';

const router = express.Router();

// Health check
router.get('/health', healthCheck);

// Order management
router.post('/offers', createOffer);
router.post('/offers/take', takeOffer);
router.post('/offers/cancel', cancelOrder);

// Order book and market data
router.get('/orderbook', getOrderBook);
router.get('/clients/:clientId/orders', getUserOrders);
router.get('/clients/:clientId/history', getTradingHistory);

// Trading operations
router.post('/swap', executeSwap);

export default router; 
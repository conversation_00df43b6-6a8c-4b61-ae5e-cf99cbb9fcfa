import { Request, Response } from 'express';
import StellarTrading from '../models/StellarTrading';

const stellarTrading = new StellarTrading();

/**
 * Create a new trading offer on Stellar DEX
 */
export async function createOffer(req: Request, res: Response) {
    try {
        const {
            clientId,
            sellingAsset,
            sellingIssuer,
            buyingAsset,
            buyingIssuer,
            amount,
            price,
            orderType
        } = req.body;

        // Validate required fields
        if (!clientId || !sellingAsset || !buyingAsset || !amount || !price || !orderType) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required fields',
                data: null
            });
        }

        const result = await stellarTrading.createOffer({
            clientId,
            sellingAsset,
            sellingIssuer: sellingIssuer || '',
            buyingAsset,
            buyingIssuer: buyingIssuer || '',
            amount,
            price,
            orderType
        });

        return res.status(result.status).json(result);

    } catch (error: any) {
        console.error('❌ Error in createOffer controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Get order book for a trading pair
 */
export async function getOrderBook(req: Request, res: Response) {
    try {
        const { sellingAsset, buyingAsset, sellingIssuer, buyingIssuer } = req.query;

        if (!sellingAsset || !buyingAsset) {
            return res.status(400).json({
                status: 400,
                message: 'sellingAsset and buyingAsset are required',
                data: null
            });
        }

        const result = await stellarTrading.getOrderBook(
            sellingAsset as string,
            buyingAsset as string,
            sellingIssuer as string,
            buyingIssuer as string
        );

        return res.status(result.status).json(result);

    } catch (error: any) {
        console.error('❌ Error in getOrderBook controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Take/match an existing offer
 */
export async function takeOffer(req: Request, res: Response) {
    try {
        const { clientId, orderId, takeAmount } = req.body;

        if (!clientId || !orderId) {
            return res.status(400).json({
                status: 400,
                message: 'clientId and orderId are required',
                data: null
            });
        }

        const result = await stellarTrading.takeOrder({
            clientId,
            orderId,
            amount: takeAmount
        });

        return res.status(result.status).json(result);
    } catch (error: any) {
        console.error('❌ Error in takeOffer controller:', error);
        return res.status(500).json({
            status: 500,
            message: 'Internal server error',
            data: { error: error.message }
        });
    }
}

/**
 * Execute an atomic swap
 */
export async function executeSwap(req: Request, res: Response) {
    try {
        const {
            clientId,
            sourceAsset,
            sourceIssuer,
            destinationAsset,
            destinationIssuer,
            sourceAmount,
            minDestAmount
        } = req.body;

        if (!clientId || !sourceAsset || !destinationAsset || !sourceAmount || !minDestAmount) {
            return res.status(400).json({
                status: 400,
                message: 'Missing required fields for swap',
                data: null
            });
        }

        const result = await stellarTrading.executeSwap({
            clientId,
            sourceAsset,
            sourceIssuer: sourceIssuer || '',
            destinationAsset,
            destinationIssuer: destinationIssuer || '',
            sourceAmount,
            minDestAmount
        });

        return res.status(result.status).json(result);

    } catch (error: any) {
        console.error('❌ Error in executeSwap controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Cancel an order
 */
export async function cancelOrder(req: Request, res: Response) {
    try {
        const { clientId, orderId } = req.body;

        if (!clientId || !orderId) {
            return res.status(400).json({
                status: 400,
                message: 'clientId and orderId are required',
                data: null
            });
        }

        const result = await stellarTrading.cancelOrder({
            clientId,
            orderId
        });

        return res.status(result.status).json(result);

    } catch (error: any) {
        console.error('❌ Error in cancelOrder controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Get user's trading history
 */
export async function getTradingHistory(req: Request, res: Response) {
    try {
        const { clientId } = req.params;

        if (!clientId) {
            return res.status(400).json({
                status: 400,
                message: 'clientId is required',
                data: null
            });
        }

        const result = await stellarTrading.getTradingHistory(clientId);

        return res.status(result.status).json(result);

    } catch (error: any) {
        console.error('❌ Error in getTradingHistory controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Get active orders for a user
 */
export async function getUserOrders(req: Request, res: Response) {
    try {
        const { clientId } = req.params;
        const { status = 'active' } = req.query;

        if (!clientId) {
            return res.status(400).json({
                status: 400,
                message: 'clientId is required',
                data: null
            });
        }

        // This would need to be implemented in the StellarTrading model
        // const result = await stellarTrading.getUserOrders(userId, status as string);

        return res.status(200).json({
            status: 200,
            message: 'User orders retrieved successfully',
            data: [] // Placeholder
        });

    } catch (error: any) {
        console.error('❌ Error in getUserOrders controller:', error);
        return res.status(500).json({
            status: 500,
            message: error.message || 'Internal server error',
            data: null
        });
    }
}

/**
 * Health check for trading service
 */
export async function healthCheck(req: Request, res: Response) {
    try {
        return res.status(200).json({
            status: 200,
            message: 'Trading service is healthy',
            data: {
                service: 'stellar-trading',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            }
        });

    } catch (error: any) {
        console.error('❌ Error in trading health check:', error);
        return res.status(500).json({
            status: 500,
            message: 'Trading service is unhealthy',
            data: null
        });
    }
} 
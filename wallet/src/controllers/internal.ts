import express, { Request, Response } from "express";
import { JWTMiddleware } from "../helpers/jwt.middleware";
import Internal from "../models/internal";
import ThirdParty from "../models/thirdParty";
import TemboDiagnosticsService from "../services/tembo.diagnostics.service";


const router = express.Router();
const internal = new Internal();
const thirdParty = new ThirdParty();
const temboDiagnostics = new TemboDiagnosticsService();


const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
    // next()
};

const saveApiLog = (req: Request, res: Response, next: any) => {
    // thirdPartyHandler.saveApiLog(req.body, req.ip || "");  
    next();
}
router.post("/send-callback",  sendCallBack);
router.post("/webhooks/honeycoin",  sendHonryCoinWebhook);
router.post("/webhooks/tembo",  sendTemboWebhook);
router.post("/initiate-sweep",  initiateSweep);

// Tembo diagnostics endpoints
router.get("/tembo/diagnostics", temboDiagnosticsReport);
router.post("/tembo/force-check/:transactionId", forceTemboStatusCheck);
router.post("/tembo/bulk-force-check", bulkForceTemboCheck);


async function sendHonryCoinWebhook(req: Request, res: Response) {
  try {

    const result = await thirdParty.sendHonryCoinWebhook(req.body);
    res.status(200).json(result);

  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendTemboWebhook(req: Request, res: Response) {
  try {
    console.log("🔹 Tembo webhook received:", req.body);
    const result = await thirdParty.sendTemboWebhook(req.body);
    res.status(200).json(result);

  } catch (error: any) {
    console.error("❌ Error processing Tembo webhook:", error);
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendCallBack(req: Request, res: Response) {
  try {
    const result = await internal.sendCallBack(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function initiateSweep(req: Request, res: Response) {
  try {
    const result = await internal.initiateSweep(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

// Tembo diagnostics endpoints
async function temboDiagnosticsReport(req: Request, res: Response) {
  try {
    const hoursBack = parseInt(req.query.hours as string) || 24;
    const result = await temboDiagnostics.runDiagnostics(hoursBack);
    res.status(200).json({
      success: true,
      message: "Tembo diagnostics completed",
      data: result
    });
  } catch (error: any) {
    console.error("❌ Error running Tembo diagnostics:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function forceTemboStatusCheck(req: Request, res: Response) {
  try {
    const { transactionId } = req.params;
    const result = await temboDiagnostics.forceStatusCheck(transactionId);
    res.status(200).json({
      success: true,
      message: "Force status check completed",
      data: result
    });
  } catch (error: any) {
    console.error("❌ Error in force status check:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function bulkForceTemboCheck(req: Request, res: Response) {
  try {
    const maxTransactions = parseInt(req.body.maxTransactions) || 50;
    const result = await temboDiagnostics.bulkForceCheck(maxTransactions);
    res.status(200).json({
      success: true,
      message: "Bulk force check completed",
      data: result
    });
  } catch (error: any) {
    console.error("❌ Error in bulk force check:", error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
}


export default router;

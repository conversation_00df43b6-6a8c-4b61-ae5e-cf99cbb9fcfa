import express, { Request, Response } from "express";
import Transactions from "../models/transactions";
import StellarListener from "../helpers/stellar.listner";
import { JWTMiddleware } from "../helpers/jwt.middleware";
import PegaPay from "../intergrations/PegPay";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
// import Exchange from "../models/exchange";  // Not needed in wallet service
// import Admin from "../models/admin";  // Not needed in wallet service
import { JWTMiddlewareAdmin } from "../helpers/jwt.middleware.admin";
import { Steps } from "../helpers/model";
import { v4 as uuidv4 } from 'uuid';
import packageJson from '../../package.json';
import Internal from "../models/internal";

const router = express.Router();
const transactions = new Transactions();
// const exchange = new Exchange();  // Not needed in wallet service
// const admin = new Admin();  // Not needed in wallet service
const thirdPartyHandler = new ThirdPartyHandler();

const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
    //  next()
};
const applyJWTConditionallyAdmin = (req: Request, res: Response, next: any) => {
    JWTMiddlewareAdmin.verifyToken(req, res, next);
    //  next()
};

const saveApiLog = (req: Request, res: Response, next: any) => {
    const ip = req.ip || "";
    thirdPartyHandler.saveApiLog(req.body, ip);
    next();
}

const getCleanIp = (ip: string): string => {
    try {
        if (ip.includes(",")) {
            ip = ip.split(",")[0]; // If behind multiple proxies
        }
        if (ip.startsWith("::ffff:")) {
            return ip.replace("::ffff:", "");
        }
        return ip;
    } catch (error: any) {
        console.error("❌ Error in getCleanIp:", error);
        return ip;
    }
};

const checkIpWhitelist = async (req: Request, res: Response, next: any) => {
    const ip = getCleanIp(req.body.ip);
    const clientId = req.body.clientId || "";
    const isWhitelisted = await thirdPartyHandler.checkIpWhitelist(ip, clientId);

    if (isWhitelisted == false) {
        return res.status(403).json({ message: "IP not allowed" });
    }
    next();
};

//API ENDPOINTS
router.get("/transaction/:id", applyJWTConditionally, getTransaction);
router.get("/transactionReference/:id", applyJWTConditionally, geTransactionByRefId); //old endpoint
router.get("/transactionReferenceId/:id", applyJWTConditionally, geTransactionByRefIdNew); //new endpoint
router.post("/direct-payout", applyJWTConditionally, saveApiLog, checkIpWhitelist, directPayout);
router.post("/direct-collection", applyJWTConditionally, saveApiLog, checkIpWhitelist, directCollection);
router.post("/validate-request", applyJWTConditionally, checkIpWhitelist, validateRequest);
router.post("/validate-account", applyJWTConditionally, checkIpWhitelist, validateRequest);
// Send transaction (internal use)
router.post("/approveDepositTransaction", applyJWTConditionallyAdmin, saveApiLog, checkIpWhitelist, issueTokens);
router.post("/approve-swap", applyJWTConditionally, saveApiLog, checkIpWhitelist, SwapTokens);
router.get("/products", applyJWTConditionally, products);
router.get("/products/:id", applyJWTConditionally, getProductDetails);
router.get("/transaction-provider-details/:id", applyJWTConditionally, getTransactionProviderDetails);

// ENDPOINTS FOR CLIENTS
// everything on the api, is accessed here
router.get("/balance", applyJWTConditionally, getBalance);
router.get("/statement", applyJWTConditionally, getStatement);
router.get("/statement/collections", applyJWTConditionally, getCollections);
router.get("/statement/payouts", applyJWTConditionally, getPayouts);
router.post("/depositRequest", applyJWTConditionally, depositRequest);
router.post("/webhook-utilia", saveApiLog, webhookUtilia);
router.post("/webhook/pegpay", saveApiLog, webhookPegPay);
router.post("/webhook-utilia-sign", saveApiLog, webhookUtiliaStatus);
router.post("/admin/send-transaction", applyJWTConditionally, saveApiLog, sendTransaction);
router.post("/admin/approve-send-transaction", applyJWTConditionally, saveApiLog, approveSendTransaction);


// TODO: Implement these methods in Transactions model
router.post("/admin/request-exchange", applyJWTConditionally, saveApiLog, requestExchange);
router.get("/admin/get-orders", applyJWTConditionally, getOrders);
router.post("/admin/take-orders", applyJWTConditionally, saveApiLog, takeOrders);
router.get("/admin/get-my-orders", applyJWTConditionally, getMyOrders);
router.post("/admin/reverse-transaction", applyJWTConditionallyAdmin, saveApiLog, ReverseTransaction);
router.post("/admin/retry-transaction", applyJWTConditionallyAdmin, saveApiLog, retryTransaction);
router.get("/thirdparty-balance", getThirdPartyBalance);
router.post("/complete-admin-transaction", completeAdminTransaction);
router.get("/version", version);
router.get("/health", health);

async function version(req: Request, res: Response) {
    try {
        const result = { version: packageJson.version };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function health(req: Request, res: Response) {
    try {
        const result = { status: "ok" };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function ReverseTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.ReverseTransaction(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function approveSendTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.approveSendTransaction(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function webhookUtiliaStatus(req: Request, res: Response) {
    try {
        const result = {
            "signTransaction": true
        }
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function webhookPegPay(req: Request, res: Response) {
    try {
        const cl_id = new ThirdPartyHandler().getTransId()
        const clientId = "PEGPAY"
        const transId = cl_id
        const callbackUrl = ""
        const event = "status_update"
        const webhookData = req.body
        const status = 202
        await new ThirdPartyHandler().saveWebhookLog(cl_id, clientId, transId, callbackUrl, event, webhookData, status)
        const result = await transactions.webhookPegPay(req.body);
        await new ThirdPartyHandler().updateWebhookLog(cl_id, result, 200, new ThirdPartyHandler().formatedDate(new Date()))
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function webhookUtilia(req: Request, res: Response) {
    try {
        const cl_id = new ThirdPartyHandler().getTransId()
        const clientId = "UTILIA"
        const transId = cl_id
        const callbackUrl = ""
        const event = req.body.type || "transaction_status"
        const webhookData = req.body
        const status = 200
        console.log("webhookData", webhookData)
        await new ThirdPartyHandler().saveWebhookLog(cl_id, clientId, transId, callbackUrl, event, webhookData, status)

        if (event == "TRANSACTION_STATE_UPDATED") {
            const result = await transactions.webhookUtilia(req.body,cl_id, req.headers);
            await new ThirdPartyHandler().updateWebhookLog(cl_id, result, result.status, new ThirdPartyHandler().formatedDate(new Date()))
            res.status(200).json(result);
        } else {
            res.status(200).json({ message: "Event not supported" })
        }
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function depositRequest(req: Request, res: Response) {
    try {
        const result = await transactions.depositRequest(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function validateRequest(req: Request, res: Response) {
    try {
        const result = await transactions.validateRequest(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}




async function getBalance(req: Request, res: Response) {
    try {
        const result = await transactions.getBalance(req.body.clientId);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}


async function getPayouts(req: Request, res: Response) {
    try {
        const result = await transactions.FilterTransactions(req.body.clientId, "PUSH");
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function getCollections(req: Request, res: Response) {
    try {
        const result = await transactions.FilterTransactions(req.body.clientId, "PULL");
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function getStatement(req: Request, res: Response) {
    try {
        const result = await transactions.getStatement(req.body.clientId);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function products(req: Request, res: Response) {
    try {
        const { clientId } = req.params;
        const result = await transactions.products(clientId);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
async function getProductDetails(req: Request, res: Response) {
    try {
        const result = await transactions.getProductDetails(req.params.id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function getTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.getTransaction(req.params.id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

//new endpoint
async function geTransactionByRefIdNew(req: Request, res: Response) {
    try {
        const result = await transactions.geTransactionByRefIdNew(req.params.id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
//old endpoint
async function geTransactionByRefId(req: Request, res: Response) {
    try {
        const result = await transactions.geTransactionByRefId(req.params.id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}
/// provider details
async function getTransactionProviderDetails(req: Request, res: Response) {
    try {
        const result = await transactions.geTransactionProviderDetailsByRefId(req.params.id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function directPayout(req: Request, res: Response) {
    try {
        const trans_id = uuidv4();
        await transactions.saveTransactionLog(trans_id, "PENDING", Steps.START, 200, "DIRECT_PAYOUT_REQUEST", req.body)
        const result = await transactions.directPayout(req.body, trans_id);
        await transactions.saveTransactionLog(trans_id, "SUCCESS", Steps.END, 200, "DIRECT_PAYOUT_RESPONSE", result)

        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

/**
 * Handles direct collection requests from various payment sources
 * @param req Request object
 * @param res Response object
 */
async function directCollection(req: Request, res: Response) {
    try {
        const trans_id = uuidv4();
        await transactions.saveTransactionLog(trans_id, "PENDING", Steps.START, 200, "DIRECT_COLLECTION_REQUEST", req.body)
        const result = await transactions.directCollection(req.body, trans_id);
        await transactions.saveTransactionLog(trans_id, "SUCCESS", Steps.END, 200, "DIRECT_COLLECTION_RESPONSE", result)

        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function sendTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.sendTransaction(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function issueTokens(req: Request, res: Response) {
    try {
        const result = await transactions.adminApproveDeposit(req.body);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function SwapTokens(req: Request, res: Response) {
    try {
        const result = await transactions.SwapTokens(req.body.trans_id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function BurnTokens(req: Request, res: Response) {
    try {
        const result = await transactions.BurnTokens(req.body.trans_id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}


async function requestExchange(req: Request, res: Response) {
    try {
        // const result = await exchange.requestExchange(req.body);  // Exchange functionality moved
        const result = { message: "Exchange functionality not implemented in wallet service" };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function getOrders(req: Request, res: Response) {
    try {
        // const result = await exchange.getOrders(req.body.clientId);  // Exchange functionality moved
        const result = { message: "Exchange functionality not implemented in wallet service" };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function takeOrders(req: Request, res: Response) {
    try {
        // const result = await exchange.takeOrders(req.body);  // Exchange functionality moved  
        const result = { message: "Exchange functionality not implemented in wallet service" };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function getMyOrders(req: Request, res: Response) {
    try {
        // const result = await exchange.getMyOrders(req.body.clientId);  // Exchange functionality moved
        const result = { message: "Exchange functionality not implemented in wallet service" };
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function retryTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.retryPayout(req.body.trans_id);
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

async function completeAdminTransaction(req: Request, res: Response) {
    try {
        const result = await transactions.completeAdminReqest(req.body)
        res.status(200).json(result)
    } catch (error: any) {
        console.error(`Error in completeAdminTransaction:`, error);
        res.status(500).json({
            message: "Server error in admin transaction",
            error: error.message
        });
    }
}

async function getThirdPartyBalance(req: Request, res: Response) {
    try {
        const result = await new Internal().balanceManagement();
        res.status(200).json(result);
    } catch (error: any) {
        res.status(500).json({ message: "Server error", error });
    }
}

export default router;

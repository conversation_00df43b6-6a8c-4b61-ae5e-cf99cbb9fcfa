import Model, { Steps } from "../helpers/model";
import { v4 as uuidv4 } from "uuid";
import { object, array, symbol, z } from "zod";
import dotenv from "dotenv";
import crypto from "crypto";
import StellarService from "../helpers/StellarService";
import PegaPay from "../intergrations/PegPay";
import { post } from "../helpers/httpRequest";
import { CallbackData, systemProductCodes, TransactionInterface, TransactionInterfaceMini } from '../helpers/interface'
import { Console } from "console";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import MyFX from "../intergrations/MyFX";
import { batchGetAssets, getUserWallet, loadResource } from "../intergrations/Utilia";
import { CryptoTransactionInterface, StableCoinTransaction, TransactionResponseInterface, TransactionStatementInterface, UtiliaInterface, StatusCodes } from "../intergrations/interfaces";
import TwoFactorAuthHelper from "../helpers/2fa.helper";
import LiquidityClient from "../intergrations/LR";
import HoneyCoin from "../intergrations/HoneyCoin";
import PollingService from "../services/polling.collections.service";
import PollingPayoutsService from "../services/polling.payouts.service";
import Internal from "./internal";
import { query } from "express";
import Decimal from 'decimal.js';
import { SweeperService } from "../services/sweeper.service";
import IntegrationHelper from "../intergrations/IntegrationHelper";
const sweeper = new SweeperService();
const lr = new LiquidityClient();

dotenv.config();
const stellar = new StellarService()
const pg = new PegaPay()
const internal = new Internal();
const thirdPartyHandler = new ThirdPartyHandler();
const pollingService = new PollingService();
const pollingPayoutsService = new PollingPayoutsService();

interface webhookUtiliaInterface {
    id: string;
    vault: string;
    type: string;
    details: any;
    resourceType: string;
    resource: string;
}

class Transactions extends Model {
    private issuerPublicKey: string;
    private honeycoin: HoneyCoin;
    constructor() {
        super();
        this.issuerPublicKey = process.env.STELLAR_ISSUER_PUBLIC || "";
        this.honeycoin = new HoneyCoin();
    }

    async adminApproveDeposit(data: any) {
        thirdPartyHandler.saveApiLog(data, "adminApproveDeposit")
        console.log('adminApproveDeposit', data)
        const { trans_id, userId, token } = data
        const authCheck = await this.checkAuthentication(userId, token)
        console.log('authCheck', authCheck)
        if (!authCheck) {
            //   return this.makeResponse(400, "You are not authorized to approve this deposit")
        }
        const result = await this.issueTokens(trans_id)
        return this.makeResponse(StatusCodes.SUCCESS.code, "Deposit approved", result)
    }

    async checkAuthentication(userId: string, code: any) {
        const client = await this.selectDataQuerySafe("system_users", { id: userId });
        if (client.length === 0) {
            return false
        }
        return true
    }

    async depositRequest(data: any) {
        try {
            console.log('depositRequest', data)
            const { clientId, from, to, currency, narration, pin, amount } = data;

            const fiatAddresses: any = await this.callQuerySafe(`SELECT * FROM banks WHERE currency = ?`, [currency]);

            const fromAsset = currency;
            console.log(fromAsset)
            // Check if the client exists
            const client = await this.selectDataQuerySafe("clients", { client_id: clientId });
            if (client.length === 0) {
                return this.makeResponse(StatusCodes.FAILED.code, "Client not found");
            }

            //  return this.makeResponse(203, "Not activated for this service");



            const session = client[0].session_id;
            const memo = this.generateRandomDigitNumber(12)


            const refId = this.getRandomString()


            if (from == "BANK") {

                if (fiatAddresses.length === 0) {
                    return this.makeResponse(404, "No bank account found for this currency");
                }
                const transType = "BANK_DEPOSIT"
                const getSystemproductCode = this.systemProductCodes(transType)
                const trans_id = this.getTransId()

                const creationInfo: TransactionInterfaceMini = {
                    reference_id: memo,
                    validation_id: refId,
                    trans_id: trans_id,
                    product_id: getSystemproductCode,
                    client_id: clientId,
                    trans_type: "PULL",
                    amount,
                    service_name: transType,
                    currency: fromAsset,
                    receiver_account: clientId
                }
                await this.handleTransactionCreated(creationInfo)

                const resp = fiatAddresses[0]
                resp.reference_code = memo
                return this.makeResponse(StatusCodes.SUCCESS.code, "Request sent successfully", resp);

            } else {
                const depositAddress: any = await this.callQuerySafe(`SELECT * FROM dp_crypto_deposits c INNER JOIN utilia_assets a ON c.network=a.network WHERE client_id = ?`, [clientId]);
                if (depositAddress.length > 0) {
                    return this.makeResponse(200, "Deposit address already exists", depositAddress);
                }

                const pay = { clientId, chain: "tron-mainnet", tag: "wallet" };
                const response = await internal.generateDepositAddress(pay);
                if (response.status == 200) {
                    const depositAddress2: any = await this.callQuerySafe(`SELECT * FROM dp_crypto_deposits c INNER JOIN utilia_assets a ON c.network=a.network WHERE client_id = ?`, [clientId]);
                    if (depositAddress2.length > 0) {
                        return this.makeResponse(200, "Deposit address already exists", depositAddress2);
                    }
                }

            }
            // await this.insertData("transactions", transactionData);

            // Perform the swap (this is a placeholder, actual implementation may vary)
            // const swapResult = await new StellarService().swapCurrency(clientId, fromCurrency, toCurrency, amount);
            // if (!swapResult.success) {
            //   }

            return this.makeResponse(200, "Swap successful");
        } catch (error: any) {
            console.error("Error requesting swap:", error);
            return this.makeResponse(StatusCodes.ERROR.code, "Error requesting swap");
        }
    }
    systemProductCodes(productCode: string): any {
        if (productCode == "BANK_DEPOSIT") {
            return systemProductCodes.BANK_DEPOSIT
        } else if (productCode == "BANK_WITHDRAWAL") {
            return systemProductCodes.BANK_WITHDRAWAL
        } else if (productCode == "SWAP_FROM") {
            return systemProductCodes.SWAP_FROM
        } else if (productCode == "SWAP_TO") {
            return systemProductCodes.SWAP_TO
        } else if (productCode == "CRYPTO") {
            return 90011
        } else if (productCode == "REVERSE") {
            return 90012
        }
    }


    async getValidation(validationId: string) {
        const transaction = await this.callQuerySafe(
            "SELECT * FROM transactions WHERE validation_id = ? AND status = 'pending' AND trans_type = 'PULL'",
            [validationId]
        );
        return transaction;
    }


    async validateRequest(data: any) {
        try {
            // 🛠 Validate Input
            const validationSchema = z.object({
                clientId: z.number().min(5),
                product_id: z.string().min(3),
                account_number: z.string().min(12),
                reference_id: z.string().min(5),
                trans_type: z.string().min(3),
                currency: z.string().min(3),
                amount: z.number().min(1)
            });
            const productInfo = await this.selectDataQuerySafe("products", { product_id: data.product_id });
            if (productInfo.length == 0) {
                return this.makeResponse(StatusCodes.FAILED.code, "Product not found");
            }

            const clientInfo = await this.getDecryptedApiKey(data.clientId)
            if (clientInfo == null) {
                return this.makeResponse(400, "Client not found")
            }

            const { public_key } = clientInfo

            const phoneNumberSchema = z.string().regex(/^\d{10,15}$/, "Invalid phone number format");
            const phoneValidation = phoneNumberSchema.safeParse(data.account_number);
            if (!phoneValidation.success) {
                console.error("Phone Number Validation Error:", phoneValidation.error.errors);
                return this.makeResponse(400, "Invalid phone number format", phoneValidation.error.errors);
            }

            const validation = validationSchema.safeParse(data);
            if (!validation.success) {
                console.error("Validation Error:", validation.error.errors);
                return this.makeResponse(400, "Invalid data", validation.error.errors);
            }

            const { clientId, currency, product_id, account_number, amount } = validation.data;


            const { currency: productCurrency, trans_type, fee_type, fee_amount, product_code: service_name } = productInfo[0];
            console.log(`productInfo`, productInfo[0])
            let fee = 100; // Number(fee_amount);
            if (fee_type == "PERCENTAGE") {
                // fee = (Number(fee_amount) / 100) * Number(amount);
            }


            const result = await thirdPartyHandler.handleValidation({
                phone: data.account_number,
                service_name: service_name,
                currency: productCurrency || "UGX"
            });

            if (result.status != 200) {
                return result
            }


            // ✅ Extract Data After Validation

            let assetCode = currency;
            if (trans_type == "PULL") {
                assetCode = "c" + currency
            }
            // 🔑 Generate Unique Request ID
            const validateRequestId = uuidv4();
            const trans_id = uuidv4();

            const validation_id = this.generateRandom4DigitNumber()
            // 🛠 Create Pending Transaction
            const transactionData = {
                validation_id: validateRequestId,
                client_id: clientId,
                product_id: product_id,
                trans_type,
                trans_id: trans_id,
                amount: amount,
                asset_code: assetCode,
                fee,
                currency,
                sender_account: clientId,
                receiver_account: account_number,
                memo: validateRequestId,
                status: "pending",
            };

            const quote = {
                ...result.data,
                fee
            }
            const resp = {
                ...quote, ...transactionData,
            }

            // await this.insertData("transactions", transactionData);

            // ✅ Return Response
            return this.makeResponse(201, "Transaction validated successfully", resp);

        } catch (error: any) {
            console.error("Error validating transaction request:", error);
            return this.makeResponse(StatusCodes.ERROR.code, "Server error");
        }
    }


    async validateTwoFactor(clientId: string, verificationToken: string) {

        const data: any = {};

        data.clientId = clientId // admin[0].id;
        data.user_type = 'client';
        const client: any = await this.userFaAuthAccountStatus({ ...data, "user_type": 'client' })

        if (client.length === 0) {
            return this.makeResponse(201, "Unknown 2fa secret");
        }
        const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0].secret, verificationToken)
        return responseData;
    }
    async approveSendTransaction(data: any) {
        const { trans_id, status } = data
        const transaction: any = await this.callQuerySafe(
            "SELECT * FROM transactions WHERE trans_id = ? AND status = ? AND trans_type = 'PUSH'",
            [trans_id, StatusCodes.PENDING.code]
        );
        if (transaction.length == 0) {
            return this.makeResponse(StatusCodes.FAILED.code, "Transaction not found");
        }
        const { client_id, amount, asset_code, receiver_account } = transaction[0];
        const apiInfo = await this.getDecryptedApiKey(client_id);
        if (apiInfo == null) {
            return this.makeResponse(StatusCodes.FAILED.code, "Client not found");
        }
        this.makeResponse(StatusCodes.FAILED.code, "Error approving transaction");
    }

    //withdraw crypto tokens manually and send to the LR
    async sendTransaction(data: any) {
        try {
            const { clientId, amount, asset, token, payment_method_id, userId, receive_currency } = data;
            let chain = data.chain || "BSC"
            let to_address = data.to_address;
            let sent_type = data.trans_type;
            const allowedtypes = ["LIQUIDITY_RAIL", "CRYPTO_TRANSFER"]
            if (!allowedtypes.includes(sent_type)) {
                return this.makeResponse(StatusCodes.FAILED.code, "Invalid transaction type");
            }
            const trans_type = sent_type

            const responseData: any = await this.validateTwoFactor(userId, token)
            if (!responseData?.status) {
                return this.makeResponse(StatusCodes.NOT_AUTHORIZED.code, "Invalid authentication token");
            }
            const productInfo = await this.selectDataQuerySafe("products", { product_name: trans_type });
            if (productInfo.length == 0) {
                return this.makeResponse(StatusCodes.FAILED.code, "Product not found");
            }



            const reference_id = this.getTransId()
            const trans_id = uuidv4();

            const { product_id } = productInfo[0];

            let provider: any = null;
            let extra_data: any = null
            let send_amount: any = amount;


            if (sent_type == "LIQUIDITY_RAIL") {
                const provider_id = data.provider_id
                const service_id = data.service_id
                let converted_asset = new ThirdPartyHandler().convertAsset(chain, asset)

                //check if the asset is supported by the service
                const resp = await lr.sendToLiquidityRail(clientId, amount, converted_asset, receive_currency, payment_method_id, service_id, provider_id, chain, trans_id)
                console.log('LR Response:', resp);
                if (resp.status != 200) {
                    return resp
                }
                extra_data = resp.data.chainInfo
                console.log('extra_data', extra_data)
                chain = resp.data.chainInfo.chain
                to_address = resp.data.chainInfo.pay_in_address
                send_amount = resp.data.chainInfo.send_amount

            } else {
                extra_data = { chain: chain }
            }

            const assetInfo: any = await this.callQuerySafe(
                `SELECT asset_id FROM utilia_assets WHERE asset_code = ? AND lower(chain) = ?`,
                [asset, chain.toLowerCase()]
            );
            if (assetInfo.length == 0) {
                return this.makeResponse(StatusCodes.FAILED.code, "Invalid asset code");
            }
            const { asset_id } = assetInfo[0]
            extra_data.asset_id = asset_id
            const payOutData = {
                clientId,
                amount: send_amount,
                account_number: to_address,
                payment_method_id,
                trans_type: "PUSH",
                service_name: sent_type,
                reference_id: reference_id,
                currency: asset.toUpperCase(),
                product_id: product_id,
                receive_currency: receive_currency,
                extra_data: extra_data
            }
            console.log('payOutData', payOutData)


            const stableCoinTransaction: StableCoinTransaction = {
                clientId: clientId,
                refId: trans_id,
                hash: "",
                direction: "OUTGOING",
                state: "PENDING",
                network: "utilia",
                asset: asset,
                asset_id: asset_id,
                amount: amount,
                source: "",
                chain: chain,
                destination: to_address,
                createTime: new Date().toISOString(),
                confirmTime: ""
            }
            await this.insertData("sc_transactions", stableCoinTransaction);
            const result = await this.directPayout(payOutData, trans_id);
            return result
        } catch (error: any) {
            console.error("Error withdrawing transaction:", error);
            return this.makeResponse(StatusCodes.ERROR.code, "Failed to withdraw transaction");
        }
    }




    async products(clientId: string) {
        const products = await this.selectDataQuerySafe("products");
        return this.makeResponse(StatusCodes.SUCCESS.code, "products", products);
    }

    async getProductDetails(id: string) {

        const product: any = await this.callQuerySafe(`SELECT * FROM products WHERE product_id = ? OR product_code = ?`, [id, id]);
        if (product.length == 0) {
            return this.makeResponse(StatusCodes.FAILED.code, "Product not found");

        }
        const productData = product[0]
        productData.fee_amount = parseFloat(productData.fee_amount)
        return this.makeResponse(StatusCodes.SUCCESS.code, "product", productData);
    }


    async handlePendingPayoutTransaction(transaction: any) {
        const { client_id, service_name, ext_reference, amount, created_at, trans_id } = transaction
        console.log(`handlePendingPayoutTransaction '${client_id}','${service_name}','${ext_reference}','${amount}','${created_at}','${trans_id}'`)

        let response: any = null
        if (process.env.INTERGRATIONS === 'AGGREGATOR') {
            response = new IntegrationHelper().makePushResponse(transaction)
        } else if (service_name == "MOBILE_MONEY") {
            response = await pollingPayoutsService.getPagasusTransaction(transaction);
        } else if (service_name == "MPESA_PAYOUT") {
            response = await pollingPayoutsService.getHoneyCoinTransactionStatus(transaction);
        } else if (service_name == "LIQUIDITY_RAIL") {
            response = await pollingPayoutsService.checkLiquidityRailPayout(trans_id);
        } else {
            return false
        }

        if (response.statusCode === 200) {
            await new Model().updateTransaction(trans_id, "SUCCESS", response.description)
        } else if (response.statusCode === 400) {
            await new Model().updateTransaction(trans_id, "FAILED", response.description)
        } else if (response.statusCode === 202) {
            const isExpired = await this.expireTransaction(trans_id, created_at, client_id, response)
            if (!isExpired) {
                return false
            }
        } else if (response.statusCode === 203) {
            await new Model().updateTransaction(trans_id, "ONHOLD", response.description)
        }
        await new ThirdPartyHandler().saveWebhook(response.statusCode, client_id, trans_id, "transaction_status", response.transStatus, response.description)

    }

    async expireTransaction(trans_id: string, created_at: string, client_id: string, pgStatus: any) {
        const now = new Date();
        const created_at_date = new Date(created_at);
        const time_difference = now.getTime() - created_at_date.getTime();
        const minutes_difference = Math.floor(time_difference / (1000 * 60));
        if (minutes_difference > 60) {
            await new ThirdPartyHandler().saveWebhook(
                StatusCodes.EXPIRED.code,
                client_id.toString(),
                trans_id,
                "transaction_status",
                "FAILED",
                StatusCodes.EXPIRED.message
            );
            await this.updateTransaction(trans_id, "FAILED", StatusCodes.EXPIRED.message, pgStatus)
            this.saveTransactionLog(trans_id, "EXPIRED", Steps.TRANS_STATUS_CHECK, 400, "Transaction Expired", pgStatus)
            return false
        }
        return true
    }

    async handlePendingCollectionTransaction(transaction: any) {
        try {
            const { client_id, service_name, ext_reference, amount, created_at, trans_id } = transaction
            console.log(`handlePendingTransaction '${client_id}','${service_name}','${ext_reference}','${amount}','${created_at}','${trans_id}'`)

            let pgStatus: any = null

            if (process.env.INTERGRATIONS === 'AGGREGATOR') {
                pgStatus = new IntegrationHelper().makeCollectionResponse(transaction)
            } else if (service_name == "MOBILE_MONEY") {
                pgStatus = await pollingService.CheckMoMoStatus(trans_id)
            } else if (service_name == "INTERAC") {
                pgStatus = await pollingService.checkInterac(ext_reference, amount)
            } else if (service_name == "MPESA_COLLECTION") {
                pgStatus = await pollingService.getHoneyCoinTransaction(trans_id)
            } else if (service_name == "TZS_COLLECTIONS") {
                // ✅ Add Tembo collection status checking
                pgStatus = await pollingService.checkTemboCollectionStatus(trans_id)
            } else {
                console.log(`⚠️ No polling logic for service: ${service_name}`);
                return false
            }

            const pg_status = pgStatus.statusCode;
            const description = await this.mapDescription(pgStatus.description)
            if (pg_status == "122" && description == "PENDING") {
                // Transaction still pending, do nothing
                const isExpired = await this.expireTransaction(trans_id, created_at, client_id, pgStatus)
                if (!isExpired) {
                    return false
                }
            } else if (pg_status == 0 && description == "SUCCESS") {
                await this.updateTransaction(trans_id, "RECEIVED", description, pgStatus)
                await this.issueTokens(trans_id);
            } else if (pg_status == 500) {
                await this.updateTransaction(trans_id, "ONHOLD", description, pgStatus)
                await new ThirdPartyHandler().saveWebhook(
                    StatusCodes.ONHOLD.code,
                    client_id.toString(),
                    trans_id,
                    "transaction_status",
                    "ONHOLD",
                    description
                );

            } else if (pg_status == 400) {
                await this.updateTransaction(trans_id, "FAILED", description, pgStatus)
                await new ThirdPartyHandler().saveWebhook(
                    StatusCodes.FAILED.code,
                    client_id.toString(),
                    trans_id,
                    "transaction_status",
                    "FAILED",
                    description
                );
                return false
            } else {
                this.saveTransactionLog(trans_id, "EXCEPTION", Steps.TRANS_STATUS_CHECK, 404, "Transaction NOT FORMATTED", pgStatus)
            }
            return true
        } catch (error) {
            console.error("Error handling pending transaction:", error);
            return false
        }
    }


    allowId(id: string, amount: any) {
        if (parseFloat(amount.toString()) < 5 && id.startsWith("mdx")) {
            return true
        }
        return false
    }


    async BurnTokens(trans_id: string) {
        try {
            console.log(`🔹 Processing Token Issuance for Transaction ID: ${trans_id}`);
            const transaction: any = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE trans_id = ? AND status = 'pending' AND trans_type = 'PUSH'",
                [trans_id]
            );

            if (transaction.length === 0) {
                console.error("❌ Distribution Failed: Transaction not found.");
                return this.makeResponse(404, "Transaction not found");
            }

            const { trans_id: transId, currency, account_number, client_id: clientId, amount, asset_code, receiver_account: receiverPublicKey } = transaction[0];

            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (apiInfo === null) {
                console.error(`❌ API Keys not found for client ID: ${clientId}`);
                return this.makeResponse(404, "API Keys not found for the client");
            }

            const { public_key: clientPublicKey, secret_key } = apiInfo
            const assetCode = asset_code;
            const assetIssuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";
            const senderSecretKey = process.env.STELLAR_PAYOUT_ISSUER_SECRET;
            const destinationPrivateKey = secret_key;


            const balances: any = await new StellarService().getBalance(clientId);
            console.log(balances)
            const fromCurrencyBalance = balances.find((balance: any) => balance.code === currency);
            if (!fromCurrencyBalance || fromCurrencyBalance.balance < amount) {
                return this.makeResponse(StatusCodes.INSUFFICIENT_BALANCE.code, StatusCodes.INSUFFICIENT_BALANCE.message);
            }


            // 🛠 Create Stellar Transaction Object
            const txArray: any = []

            const transactionObject = {
                publicKey: assetIssuer,
                amount: amount,
                asset_code: currency,
                asset_issuer: assetIssuer,
                senderSecretKey: secret_key,
                creditPrivateKey: senderSecretKey,
            };

            txArray.push(transactionObject)
            console.log(`📦 Stellar Transaction Object:`, transactionObject);
            await this.updateData("transactions", `trans_id='${transId}'`, { status: "INITIATED" });
            const memo = "Deposit";
            const stellarResponse = await stellar.makeBatchTransfers(trans_id, memo, txArray);
            console.log(`🚀 Stellar Response:`, stellarResponse);
            const responseCode = stellarResponse.response === 1 ? 100 : 203;
            const status = stellarResponse.response === 1 ? "WALLET_DEBITED" : "DEBIT_FAILED";
            await this.updateData("transactions", `trans_id='${transId}'`, { status });

            if (status == "WALLET_DEBITED") {
                await this.completeTransaction(stellarResponse.txHash, transId);
            }
            // send reques to pegasus
            // const pegResponse = await new PegaPay().makeMMPushRequest(clientId, transId, amount, "1234", account_number)

            return this.makeResponse(200, status, stellarResponse);
        } catch (error: any) {
            console.error("❌ Error in issueTokens:", error);
            return this.makeResponse(500, "Server error");
        }
    }

    async geTransactionById(trans_id: string) {
        try {
            console.log(`🔹 Processing Token Issuance for Transaction ID: ${trans_id}`);
            // 🔎 Fetch Transaction Details
            const transaction = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE trans_id = ? AND status = 'pending'",
                [trans_id]
            );
            return this.makeResponse(200, "Server error", transaction);
        } catch (error) {

        }
    }

    async SwapTokens(trans_id: string) {
        try {
            console.log(`🔹 Processing Token Issuance for Transaction ID: ${trans_id}`);
            const swapRequest: any = await this.callQuerySafe(
                "SELECT * FROM swap_requests WHERE trans_id = ? AND status = 'pending' AND trans_type = 'SWAP'",
                [trans_id]
            );

            if (swapRequest.length === 0) {
                console.error("❌ Distribution Failed: Transaction not found.");
                return this.makeResponse(404, "Transaction not found");
            }

            // ✅ Extract Transaction Data
            const { amount, asset_code, currency, product_id, client_id } = swapRequest[0];

            // 🔎 Fetch Client API Keys
            const apiInfo = await this.getDecryptedApiKey(client_id);
            if (apiInfo === null) {
                console.error(`❌ API Keys not found for client ID: ${client_id}`);
                return this.makeResponse(404, "API Keys not found for the client");
            }

            const { public_key: clientPublicKey, secret_key } = apiInfo
            const assetIssuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";
            const senderSecretKey = process.env.STELLAR_PAYOUT_ISSUER_SECRET;
            const destinationPrivateKey = secret_key;

            let send_amount = amount
            let receive_amount = amount

            let receive_asset = "c" + currency
            if (asset_code != receive_asset) {
                console.log("Invalid asset", asset_code, currency, receive_asset)
                // return this.makeResponse(400, "Invalid asset");
            }
            // 🛠 Create Stellar Transaction Object
            const txArray: any = []

            const transactionObject1 = {
                publicKey: assetIssuer,
                amount: send_amount,
                asset_code: asset_code,
                asset_issuer: assetIssuer,
                senderSecretKey: secret_key,
                creditPrivateKey: senderSecretKey,
            };


            const transactionObject = {
                publicKey: clientPublicKey,
                amount: receive_amount,
                asset_code: currency,
                asset_issuer: assetIssuer,
                senderSecretKey: senderSecretKey,
                creditPrivateKey: destinationPrivateKey,
            };

            txArray.push(transactionObject1)
            txArray.push(transactionObject)

            const sendTransId = this.getTransId()
            const receiveTransId = this.getTransId()
            const refId = this.getTransId()
            const getSystemproductCode = this.systemProductCodes("SWAP_FROM")
            const getSystemproductCode2 = this.systemProductCodes("SWAP_TO")
            const memo = refId

            const creationInfo1: TransactionInterfaceMini = {
                trans_id: sendTransId,
                reference_id: memo,
                validation_id: refId,
                product_id: getSystemproductCode,
                client_id: client_id,
                trans_type: "PULL",
                amount: send_amount,
                service_name: "SWAP_FROM",
                currency: asset_code,
                receiver_account: client_id
            }

            const creationInfo2: TransactionInterfaceMini = {
                trans_id: receiveTransId,
                reference_id: memo,
                validation_id: refId,
                product_id: getSystemproductCode2,
                client_id: client_id,
                trans_type: "PUSH",
                amount: receive_amount,
                service_name: "SWAP_TO",
                currency: currency,
                receiver_account: client_id
            }
            const transactionResponse1 = await this.handleTransactionCreated(creationInfo1)
            const transactionResponse2 = await this.handleTransactionCreated(creationInfo2)
            if (transactionResponse1.status == 200) {
                await this.updateData("swap_requests", `trans_id='${trans_id}'`, { status: "MINT_INITIATED" });

            } else {
                return transactionResponse1
            }



            const stellarResponse = await stellar.makeBatchTransfers(trans_id, memo, txArray);
            console.log(`🚀 Stellar Response:`, stellarResponse);
            const responseCode = stellarResponse.response === 1 ? 100 : 203;
            const status = stellarResponse.response === 1 ? "SUCCESS" : "ONHOLD";
            await this.updateData("transactions", `trans_id='${trans_id}'`, { system_status: "MINT_FAILED" });
            await this.updateData("swap_requests", `trans_id='${trans_id}'`, { status });

            await this.updateTransaction(sendTransId, status, "Transaction initiated", stellarResponse)
            await this.updateTransaction(receiveTransId, status, "Transaction initiated", stellarResponse)
            return this.makeResponse(200, status, stellarResponse);
        } catch (error: any) {
            console.error(" Error in issueTokens:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }


    async ReverseTransaction(data: any) {
        try {
            const { token, userId } = data
            if (token != "mdxQaUg") {
                // return this.makeResponse(401, "Unauthorized");
            }


            const transaction: any = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE status = 'PENDING_REVERSAL' AND trans_type = 'PUSH'"
            );
            if (transaction.length === 0) {
                return this.makeResponse(404, "Transaction not found");
            }

            for (const item of transaction) {
                const { amount, asset_code, trans_id, fee, service_name, currency, product_id, client_id } = item;
                const totalAmount = new Decimal(amount).plus(new Decimal(fee)).toString();
                console.log("totalAmount", totalAmount)
                const sendTransId = this.getTransId()
                const getSystemproductCode = this.systemProductCodes("REVERSE")
                const creationInfo1: TransactionInterfaceMini = {
                    trans_id: sendTransId,
                    reference_id: trans_id,
                    validation_id: sendTransId,
                    product_id: getSystemproductCode,
                    client_id: client_id,
                    trans_type: "REVERSAL",
                    memo: "REV " + trans_id,
                    amount: totalAmount,
                    service_name: "REVERSAL",
                    currency: asset_code,
                    receiver_account: client_id
                }
                const transactionResponse1 = await this.handleTransactionCreated(creationInfo1)
                console.log("transactionResponse1", transactionResponse1)

                if (transactionResponse1.status == 200) {
                    await this.updateData("transactions", `trans_id='${trans_id}'`, { status: "CANCELLED" });
                    await this.updateData("transactions", `trans_id='${sendTransId}'`, { status: "RECEIVED" });
                    await this.issueTokens(sendTransId)
                } else {
                    continue
                }


            }
            return this.makeResponse(200, "Transaction reversed successfully");




        } catch (error: any) {
            console.error(" Error in issueTokens:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }

    async webhookPegPay(data: any) {
        try {
            console.log("webhookPegPay", data)
            const { transId, status, description } = data
            const transaction = await this.selectDataQuerySafe("transactions", { trans_id: transId });
            if (transaction.length === 0) {
                return this.makeResponse(404, "Transaction not found");
            }

            //   await this.handlePendingTransaction(transaction[0])
            return this.makeResponse(200, "Webhook received successfully", data);
        } catch (error: any) {
            console.error(" Error in webhookPegPay:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }


    async webhookUtilia(data: webhookUtiliaInterface, cl_id: any, headers: any = null) {
        try {

            const signature = headers?.['x-utila-signature'];
            const utiliaPublicKey = process.env.UTILIA_PUBLIC_KEY;

            if (!signature || !utiliaPublicKey || !this.verifySignature(signature, JSON.stringify(data), utiliaPublicKey)) {
                console.log("Invalid signature")
                //  return this.makeResponse(400, "Invalid signature");
            }
            // Check and handle event type
            const eventType = data?.type;
            if (!eventType) {
                return this.makeResponse(StatusCodes.FAILED.code, "Missing event type");
            }


            const utila: webhookUtiliaInterface = data
            const resourceType = utila.resourceType
            console.log("resourceType:::", utila)
            if (resourceType != "TRANSACTION") {
                return this.makeResponse(StatusCodes.FAILED.code, "Invalid resource type");
            }

            const getResource = await loadResource(utila.resource)
            console.log("transaction:::", JSON.stringify(getResource))
            this.saveTransactionLog(cl_id, "SUCCESS", Steps.THIRDPARTY_RESPONSE, 200, "TRANSACTION_RECEIVED", getResource)
            const transaction: UtiliaInterface = getResource.data.transaction
            console.log("transaction:::Information", JSON.stringify(transaction))
            const { transfers } = transaction


            const { amount, asset, sourceAddress, destinationAddress } = transfers[0]
            const network = transaction.network

            const supportedNetworks: any = await this.callQuerySafe(
                "SELECT * FROM utilia_networks WHERE name = ? AND is_muda_supported = 1",
                [network]
            );
            if (supportedNetworks.length === 0) {
                const message = "Network not supported"
                const response_code = StatusCodes.FAILED.code
                this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                return this.makeResponse(StatusCodes.FAILED.code, "Network not supported");
            }
            let isMainnet = false
            const { id, testnet, is_muda_supported } = supportedNetworks[0]
            if (network.includes("mainnet") && testnet == 0) {
                isMainnet = true
            } else {
                isMainnet = false
            }




            const { newState } = utila.details.transactionStateUpdated
            console.log("newState", newState)
            console.log("transactionasset", asset)

            const assetInfo = await loadResource(asset)
            console.log("assetInfo", assetInfo)
            const assetCode = assetInfo.data.asset.symbol

            console.log("assetCode:::", assetCode)

            const refId = uuidv4()

            let env = process.env.ENVIRONMENT || "stage"


            if (is_muda_supported == 0) {
                const message = "Network not supported"
                const response_code = StatusCodes.FAILED.code
                this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                return this.makeResponse(StatusCodes.FAILED.code, "Network not supported");
            }
            if (isMainnet && env == "stage") {
                const message = "Invalid network"
                const response_code = StatusCodes.FAILED.code
                //     this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                //   return this.makeResponse(StatusCodes.FAILED.code, "Invalid network");
            }

            const supportedAssets: any = await this.callQuerySafe(
                "SELECT * FROM utilia_assets WHERE asset = ? AND is_muda_supported = 1",
                [asset]
            );
            if (supportedAssets.length === 0) {
                const message = "Invalid asset"
                const response_code = StatusCodes.FAILED.code
                this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                return this.makeResponse(StatusCodes.FAILED.code, "Invalid asset");
            }
            const { asset_id, asset_code } = supportedAssets[0]

            if (transaction.direction == "OUTGOING") {
                return await this.HandleOutgoingTransaction(transfers[0], transaction, newState, eventType, assetCode, cl_id)
            }

            const clientId = await this.getReceiverAccount(destinationAddress.value)
            if (clientId == false) {
                const message = "Client not found"
                const response_code = StatusCodes.FAILED.code
                this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                return this.makeResponse(StatusCodes.FAILED.code, "Client not found");
            }

            const stableCoinTransaction: StableCoinTransaction = {
                clientId: clientId,
                refId: refId,
                hash: transaction.hash,
                direction: transaction.direction,
                state: newState,
                network: network,
                asset: asset,
                asset_id: asset,
                amount: amount,
                source: sourceAddress.value,
                destination: destinationAddress.value,
                // Fix for MySQL datetime format: remove microseconds if present
                createTime: transaction.createTime ? transaction.createTime.split(".")[0] + "Z" : "",
                confirmTime: transaction.confirmTime ? transaction.confirmTime.split(".")[0] + "Z" : ""
            }
            const hashExists = await this.selectDataQuerySafe("sc_transactions", { hash: transaction.hash });
            if (hashExists.length == 0) {
                await this.insertData("sc_transactions", stableCoinTransaction);
            }




            const sentAsset = assetCode
            const allowedCurrencies = ["USDT", "USDC"]
            if (!allowedCurrencies.includes(assetCode)) {
                console.log("Invalid currency", assetCode)
                const message = "Invalid currency"
                const response_code = 400
                this.saveTransactionLog(cl_id, "FAILED", Steps.THIRDPARTY_RESPONSE, response_code, message, data)
                return this.makeResponse(response_code, message);
            }




            //  switch (eventType) {
            if (eventType == "TRANSACTION_STATE_UPDATED") {

                const creationInfo: TransactionInterfaceMini = {
                    reference_id: refId,
                    validation_id: refId,
                    client_id: clientId,
                    amount,
                    currency: sentAsset,
                    product_id: this.systemProductCodes("CRYPTO"),
                    trans_type: "PULL",
                    service_name: "CRYPTO",
                    receiver_account: clientId,
                    trans_id: refId
                }

                if (transaction.direction == "INCOMING") {
                    console.log("TRANSACTION_STATE_UPDATED1", creationInfo)
                    await this.handleTransactionCreated(creationInfo)
                    console.log("TRANSACTION_STATE_UPDATED", transaction.hash)
                    await this.issueTokens(refId)

                    //    const dryRunResult = await sweeper.sweepFromWallet({walletId: walletId.toString()});
                    //  console.log("dryRunResult", dryRunResult)

                } else {
                    console.log("TRANSACTION_STATE_UPDATED2", creationInfo)

                }

            }
            //      break;
            //  default:
            // Handle unknown event type
            console.log(`Unknown event type: ${eventType}`);
            //}
            return this.makeResponse(StatusCodes.SUCCESS.code, "Webhook received successfully", data);
        } catch (error: any) {
            console.error("Error in webhookUtilia:", error);
        }
    }


    async HandleOutgoingTransaction(transaction: any, transactionState: any, state: any, eventType: any, assetCode: any, cl_id: any) {
        try {
            const { note, name } = transactionState
            const parts = name.split('/');
            const transactionId = parts[parts.length - 1];
            console.log("UtilaTransactionId:::", transactionId, note)
            this.saveTransactionLog(transactionId, "START", Steps.OUTGOING_TRANSACTION, 200, state, transactionState)

            let findTransaction: any = await this.selectDataQuerySafe("transactions", { reference_id: note });
            if (findTransaction.length == 0) {
                const { amount, asset, sourceAddress, destinationAddress } = transaction
                findTransaction = await this.callQuerySafe(
                    `SELECT * FROM transactions WHERE LOWER(receiver_account) = ? AND (status='ONHOLD' OR status='PENDING') AND trans_type='PUSH' AND asset_code = ? AND amount = ?`,
                    [destinationAddress.value.toLowerCase(), assetCode, amount]
                );
                if (findTransaction.length == 0) {
                    return this.makeResponse(StatusCodes.FAILED.code, "Transaction not found");
                }
            }
            const { trans_id, service_name, client_id, status } = findTransaction[0]

            // also, block by the sourceAddress
            const finalSTATUSES = ["SUCCESS", "FAILED", "CANCELLED"]

            if (finalSTATUSES.includes(status)) {
                return this.makeResponse(StatusCodes.SUCCESS.code, "Transaction already confirmed", transactionState)
            }

            if (state == "CONFIRMED") {
                await this.updateData("sc_transactions", `refId='${trans_id}'`, { hash: transactionState.hash, state: "CONFIRMED" });
                this.saveTransactionLog(trans_id, "SUCCESS", Steps.OUTGOING_TRANSACTION, 200, "Transaction confirmed", transactionState)

                if (service_name == "LIQUIDITY_RAIL") {
                    await this.updateTransaction(trans_id, "PROCESSING", StatusCodes.SUCCESS.message, transactionState)
                } else {
                    await this.updateTransaction(trans_id, "SUCCESS", StatusCodes.SUCCESS.message, transactionState)
                }

            } else if (state == "FAILED") {
                this.saveTransactionLog(trans_id, "FAILED", Steps.OUTGOING_TRANSACTION, StatusCodes.FAILED.code, "Transaction failed", transaction)
                await this.updateTransaction(trans_id, "PENDING_REVERSAL", StatusCodes.FAILED.message, transaction)
            } else if (state == "DECLINED") {
                this.saveTransactionLog(trans_id, "PENDING_REVERSAL", Steps.OUTGOING_TRANSACTION, StatusCodes.PENDING_REVERSAL.code, "Transaction declined", transaction)
                await this.updateTransaction(trans_id, "PENDING_REVERSAL", StatusCodes.PENDING_REVERSAL.message, transactionState)
            } else if (state == "AWAITING_APPROVAL") {
                this.saveTransactionLog(trans_id, "AWAITING_APPROVAL", Steps.OUTGOING_TRANSACTION, StatusCodes.PENDING_REVERSAL.code, "Transaction declined", transaction)
                await this.updateTransaction(trans_id, "ONHOLD", StatusCodes.ONHOLD.message, transactionState)
            }
            //    this.saveTransactionLog(trans_id, "LAST_CASE", Steps.THIRDPARTY_RESPONSE, 200, "LAST ENTRY", transaction)
            return this.makeResponse(StatusCodes.SUCCESS.code, state, transactionState)

        } catch (error: any) {
            console.error("Error in HandleOutgoingTransaction:", error);
            return this.makeResponse(StatusCodes.ERROR.code, "Server error");
        }
    }


    async getReceiverAccount(destinationAddress: string) {
        const client: any = await this.callQuerySafe(
            `SELECT * FROM dp_crypto_deposits WHERE LOWER(deposit_address) = ?`,
            [destinationAddress.toLowerCase()]
        );
        if (client.length === 0) {
            return false
        }
        return client[0].client_id;
    }

    async handleTransactionCreated(data: TransactionInterfaceMini) {
        try {
            const product_id = data.product_id

            const { reference_id, validation_id, client_id, amount, currency } = data
            const mudaFee = 0
            const providerFee = 0
            const memo = data.memo || data.service_name
            const phone = client_id.toString()
            const SessionId = this.getTransId()
            const trans_id = data.trans_id || this.getTransId()

            const refIdExists = await this.selectDataQuerySafe("transactions", { reference_id });
            if (refIdExists.length > 0) {
                return this.makeResponse(400, "Transaction already exists");
            }

            const transactionData: TransactionInterface = {
                reference_id: reference_id,
                validation_id: this.getTransId(),
                client_id: client_id.toString(),
                trans_type: data.trans_type,
                trans_id: trans_id,
                amount: amount.toString(),
                req_amount: amount.toString(),
                asset_code: currency,
                fee: mudaFee.toString(),
                provider_fees: providerFee.toString(),
                currency,
                service_name: data.service_name,
                product_id: product_id,
                sender_account: phone,
                receiver_account: client_id.toString(),
                memo: memo,
                status: "PENDING",
                SessionId,
                payment_method_id: "-",
                running_balance: "0"
            };

            await this.insertData("transactions", transactionData);
            return this.makeResponse(200, "Transaction created successfully", data);
        } catch (error: any) {
            console.error("Error in webhookUtilia:", error);
            return this.makeResponse(500, "Transaction failed");
        }
    }
    //admin funtion
    async issueTokens(trans_id: string) {
        try {
            console.log(`🔹 Processing Token Issuance for Transaction ID: ${trans_id}`);

            // 🔎 Fetch Transaction Details
            const transaction: any = await this.callQuerySafe(
                `SELECT * FROM transactions WHERE trans_id = ? AND (status = 'RECEIVED' OR status = 'pending')`,
                [trans_id]
            );

            if (transaction.length === 0) {
                console.error("❌ Distribution Failed: Transaction not found.");
                return this.makeResponse(404, "Transaction not found");
            }


            // ✅ Extract Transaction Data
            const { trans_id: transId, fee, trans_type, client_id: clientId, amount, asset_code, receiver_account: receiverPublicKey } = transaction[0];

            if (trans_type == "SWAP") {
                return await this.SwapTokens(transId)
            }
            // 🔎 Fetch Client API Keys
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (apiInfo === null) {
                console.error(`❌ API Keys not found for client ID: ${clientId}`);
                return this.makeResponse(404, "API Keys not found for the client");
            }

            console.log(`apiInformation`, apiInfo)
            const { public_key: clientPublicKey, secret_key } = apiInfo
            const assetCode = asset_code;
            const callback_url = ""
            const assetIssuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";
            const senderSecretKey = process.env.STELLAR_PAYOUT_ISSUER_SECRET;
            const destinationPrivateKey = secret_key;




            // 🛠 Create Stellar Transaction Object
            const txArray: any = []

            const transactionObject = {
                publicKey: clientPublicKey,
                amount: amount,
                asset_code: assetCode,
                asset_issuer: assetIssuer,
                senderSecretKey: senderSecretKey,
                creditPrivateKey: destinationPrivateKey,
            };
            const logData = {
                publicKey: clientPublicKey,
                amount: amount,
                asset_code: assetCode,
                asset_issuer: "",
                senderSecretKey: "",
                creditPrivateKey: "",
            }
            txArray.push(transactionObject)

            const feeAcc = process.env.FEES_ACCOUNT || ""
            const feesAccount = await this.getDecryptedApiKey(feeAcc);
            if (feesAccount !== null && parseFloat(fee.toString()) > 0) {
                const { public_key: feePublic, secret_key: feeSecrete } = feesAccount
                const feeTransactionObject = {
                    publicKey: feePublic,
                    amount: fee.toString(),
                    asset_code: assetCode,
                    asset_issuer: assetIssuer,
                    senderSecretKey: destinationPrivateKey,
                    creditPrivateKey: feeSecrete,
                };
                txArray.push(feeTransactionObject);
            }



            console.log(`📦 Stellar Transaction Object:`, transactionObject);
            await this.updateData("transactions", `trans_id='${transId}'`, { system_status: "MINT_INITIATED" });
            const memo = "Deposit";
            this.saveTransactionLog(transId, "SUCCESS", Steps.STELLAR_REQUEST, 200, "Transaction confirmed", logData)
            const stellarResponse = await stellar.makeBatchTransfers(transId, memo, txArray);
            console.log(`🚀 Stellar Response:`, stellarResponse);
            const status = stellarResponse.response === 1 ? "SUCCESS" : "RECEIVED";
            const message = stellarResponse.response === 1 ? StatusCodes.SUCCESS.message : StatusCodes.RECEIVED.message
            this.saveTransactionLog(transId, status, Steps.STELLAR_RESPONSE, 200, message, stellarResponse)
            await this.updateData("transactions", `trans_id='${transId}'`, { system_status: status });
            await this.updateTransaction(transId, status, message, stellarResponse)
            await new ThirdPartyHandler().saveWebhook(200, clientId.toString(), transId, "transaction_status", status, StatusCodes.SUCCESS.message)
            return this.makeResponse(200, status, stellarResponse);
        } catch (error: any) {
            console.error("❌ Error in issueTokens:", error);
            this.saveTransactionLog(trans_id, "ERROR", Steps.STELLAR_RESPONSE, 500, "Transaction error", error)
            return this.makeResponse(500, "Server error", error.message);
        }
    }



    async getTransaction(trans_id: string) {
        const data = await this.getTransactionObj(trans_id)
        if (data == false) {
            return this.makeResponse(404, "Transactoion id not found");
        }
        const callbackData = await this.composeThirdPartyTransaction(data);
        return this.makeResponse(200, "success", callbackData);

    }

    async geTransactionByRefId(trans_id: string) {
        return await this.selectDataQuerySafe("transactions", { reference_id: trans_id });
    }

    async geTransactionByRefIdNew(trans_id: string) {
        const transaction = await this.selectDataQuerySafe("transactions", { reference_id: trans_id });
        if (transaction.length === 0) {
            return this.makeResponse(404, "Transaction not found");
        }
        const callbackData = await this.composeThirdPartyTransaction(transaction[0]);
        return this.makeResponse(200, "success", callbackData);
    }

    async getTransactionObj(trans_id: string) {

        const transaction = await this.selectDataQuerySafe("transactions", { trans_id });
        if (transaction.length === 0) {
            return false
        }
        return transaction[0]

    }
    async geTransactionProviderDetailsByRefId(trans_id: string) {
        try {
            // const transaction = await this.selectDataQuery("transactions", `trans_id = '${trans_id}'`);
            // if (transaction.length === 0) {
            //     return this.makeResponse(404, "Transaction not found");
            // }
            // const trxDetails = transaction[0]
            // console.log("🔹 Transaction Details:", trxDetails)
            let transactionDetails: any = {};
            // if (trxDetails.service_name == "MOBILE_MONEY") {
            // } else if (trxDetails.service_name == "INTERAC") {
            // } else if (trxDetails.service_name == "MPESA_COLLECTION" || trxDetails.service_name == "MPESA_PAYOUT") {
            //   transactionDetails = await this.honeycoin.getTransaction(trans_id);
            // } else {
            //   transactionDetails = await this.honeycoin.getTransaction(trans_id);
            // }
            transactionDetails = await this.honeycoin.getTransaction(trans_id);
            return this.makeResponse(200, "success returned provider details", transactionDetails);

        } catch (error: any) {
            console.error("Error completing transaction:", error);
            return this.makeResponse(500, "Failed to return provider det", error.message);
        }
    }


    async completeTransaction(stellarTxId: string, validateRequestId: string) {
        try {
            // 🔎 Find Pending Transaction
            const transaction: any = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE validation_id = ? AND status = 'pending'",
                [validateRequestId]
            );
            if (transaction.length === 0) {
                return this.makeResponse(404, "No matching pending transaction found");
            }
            const validationId = transaction[0].trans_id

            await this.updateData("transactions", `validation_id = '${validationId}'`, { status: "INITIATED" });

            // Extract transaction data
            const transId = transaction[0].trans_id
            const amount = transaction[0].amount
            const SessionId = transaction[0].SessionId
            const phone = transaction[0].receiver_account
            const clientId = transaction[0].client_id
            const service_name = "MOBILE_MONEY"
            const currency = transaction[0].currency

            // Use ThirdPartyHandler to handle the payout
            const result = await thirdPartyHandler.handlePayout({
                trans_id: transId,
                clientId: clientId.toString(),
                amount: Number(amount).toString(),
                phone,
                SessionId,
                service_name,
                currency
            });
            // Update transaction status
            if (result.status === 200) {
                await this.updateData("transactions", `validation_id = '${validationId}'`, { status: "SUCCESS" });
            } else if (result.status === 202) {
                await this.updateData("transactions", `validation_id = '${validationId}'`, { status: "PENDING_CLIENT" });
            } else {
                await this.updateData("transactions", `validation_id = '${validationId}'`, { status: "FAILED" });
            }
            return result;

        } catch (error: any) {
            console.error("Error completing transaction:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }


    // ✅ Get Account Balance
    async getBalance(clientId: string) {
        try {
            const balances = await stellar.getBalance(clientId)
            return this.makeResponse(200, "Balance fetched successfully", balances);
        } catch (error: any) {
            console.error("Error fetching balance:", error);
            return this.makeResponse(500, "Failed to fetch balance", error.message);
        }
    }


    async FilterTransactions(clientId: string, transType: string) {
        console.log(`getStatement`, clientId)
        try {
            let operations: any = []
            const transactions: any = await this.callQuerySafe(
                `SELECT * FROM transactions WHERE client_id = ? AND trans_type = ? ORDER BY id DESC`,
                [clientId, transType]
            );
            for (let i = 0; i < transactions.length; i++) {
                const transaction = transactions[i]
                const service_name = transaction.service_name || ""
                if (service_name.includes("CRYPTO") || service_name.includes("LIQUIDITY_RAIL")) {
                    const cryptoTransaction = await this.cryptoTransaction(transaction.trans_id);
                    if (cryptoTransaction) {
                        transaction.cryptoTransaction = cryptoTransaction
                    }
                }
                if (service_name.includes("LIQUIDITY_RAIL")) {
                    const liquidityRailTransaction = await pollingPayoutsService.checkLiquidityRailPayout(transaction.trans_id);
                    if (liquidityRailTransaction.statusCode) {
                        transaction.railTransaction = liquidityRailTransaction.data || {}
                    }
                }
                let newTransaction: TransactionStatementInterface = transaction as TransactionStatementInterface
                operations.push(newTransaction)
            }
            return this.makeResponse(200, "Transaction history fetched", operations);
        } catch (error: any) {
            console.error("Error fetching transactions:", error);
            return this.makeResponse(500, "Failed to fetch transactions", error.message);
        }
    }

    async getStatement(clientId: string) {
        console.log(`getStatement`, clientId)
        try {
            let operations: any = []
            const transactions: any = await this.callQuerySafe(
                `SELECT * FROM transactions WHERE client_id = ? ORDER BY id DESC`,
                [clientId]
            );
            for (let i = 0; i < transactions.length; i++) {
                const transaction = transactions[i]
                const service_name = transaction.service_name || ""
                if (service_name.includes("CRYPTO")) {
                    const cryptoTransaction = await this.cryptoTransaction(transaction.trans_id);
                    if (cryptoTransaction) {
                        transaction.cryptoTransaction = cryptoTransaction
                    }
                } else if (service_name.includes("LIQUIDITY_RAIL")) {
                    const liquidityRailTransaction = await pollingPayoutsService.checkLiquidityRailPayout(transaction.trans_id);
                    if (liquidityRailTransaction.statusCode) {
                        transaction.railTransaction = liquidityRailTransaction.data || {}
                    }
                }
                let newTransaction: TransactionStatementInterface = transaction as TransactionStatementInterface
                operations.push(newTransaction)
            }
            return this.makeResponse(200, "Transaction history fetched", operations);
        } catch (error: any) {
            console.error("Error fetching transactions:", error);
            return this.makeResponse(500, "Failed to fetch transactions", error.message);
        }
    }

    async cryptoTransaction(reference_id: any) {
        const response: any = await this.callQuerySafe(
            `SELECT * FROM sc_transactions t INNER JOIN utilia_assets a ON t.asset_id = a.asset WHERE t.refId = ?`,
            [reference_id]
        );
        if (response.length > 0) {
            return response[0] as CryptoTransactionInterface
        } else {
            return null
        }

    }


    async directCollection(data: any, trans_id: string) {
        try {
            //  const trans_id = uuidv4();

            // Validate input
            const validationSchema = z.object({
                clientId: z.string().or(z.number()),
                amount: z.string().or(z.number()),
                reference_id: z.string().min(5),
                currency: z.string().min(3),
                product_id: z.number().min(1),
                trans_type: z.string().min(3),
                memo: z.string().optional()
            });
            const { phone, trans_type } = data
            const allowedType = "PULL"
            if (trans_type != allowedType) {
                return this.makeResponse(400, "Transaction should be of type PULL");
            }
            const phoneNumberSchema = z.string().regex(/^\d{10,15}$/, "Invalid phone number format");

            const clientInfo = await this.getClientWallet(data.clientId)
            if (clientInfo.length == 0) {
                return this.makeResponse(404, "Client wallet not found")
            }
            const validation = validationSchema.safeParse(data);
            if (!validation.success) {
                console.error("Validation Error:", validation.error.errors);
                return this.makeResponse(400, "Invalid data", validation.error.errors);
            }

            const productInfo = await this.getProductInfo(data.product_id, data.clientId)
            if (productInfo == null) {
                return this.makeResponse(400, "Product not found");
            }
            const { currency: productCurrency, has_c_account, min_amount, max_amount, transaction_type, fee_type, provider_fee, fee_amount, product_code: service_name } = productInfo;

            // Extract validated data
            const { clientId, amount, reference_id, currency, memo = "Direct Collection" } = validation.data;

            if (currency != productCurrency) {
                return this.makeResponse(400, "Currency not allowed for this product");
            }

            if (trans_type != transaction_type) {
                return this.makeResponse(400, "Transaction type not allowed for this product");
            }

            // Check if transaction with this reference_id already exists
            const existingTrans = await this.geTransactionByRefId(reference_id);
            if (existingTrans.length > 0) {
                return this.makeResponse(400, "Reference ID already exists");
            }

            // Generate transaction IDs
            const validation_id = uuidv4();
            const SessionId = this.generateRandom4DigitNumber();


            const parsedAmount = parseFloat(String(amount));

            if (parsedAmount < min_amount) {
                return this.makeResponse(400, `Amount must be greater than ${min_amount}`);
            }
            if (parsedAmount > max_amount) {
                return this.makeResponse(400, `Amount must be less than ${max_amount}`);
            }


            const { mudaFee, providerFee } = await this.calculateFee(fee_type, fee_amount, provider_fee, parsedAmount, productInfo);


            let account_number = phone || data.account_number || ""
            if (service_name == "MOBILE_MONEY") {

                const phoneNumberSchema = z.string().regex(/^\d{12}$/, "Phone number must be exactly 12 digits");
                const phoneNumber = phoneNumberSchema.safeParse(account_number);
                if (!phoneNumber.success) {
                    return this.makeResponse(400, "Invalid account number, should be a valid phone number");
                }
                const network = await new PegaPay().getProvider(account_number, currency)
                if (network == "UNKNOWN") {
                    return this.makeResponse(400, "Phone number network not supported");
                }

            } else if (service_name == "INTERAC") {

                const extra_data = data.extra_data || {}
                const interact_email = extra_data.interact_email || "<EMAIL>"
                if (interact_email == null) {
                    return this.makeResponse(400, "Invalid interac email address");
                }
                account_number = interact_email

            } else if (service_name == "MPESA_COLLECTION") {

                const phoneNumberSchema = z.string().regex(/^\d{12}$/, "Phone number must be exactly 12 digits");
                const phoneNumber = phoneNumberSchema.safeParse(account_number);
                if (!phoneNumber.success) {
                    return this.makeResponse(400, "Invalid account number, should be a valid phone number");
                }

                if (!account_number.startsWith("254")) {
                    return this.makeResponse(400, "Invalid account number, should start with 254");
                }
            }

            const asset_code = has_c_account == 'yes' ? "c" + currency : currency

            // Create transaction record
            const transactionData = {
                reference_id,
                validation_id,
                client_id: clientId.toString(),
                trans_type: "PULL",
                trans_id,
                amount: amount.toString(),
                req_amount: amount.toString(),
                asset_code: asset_code,
                fee: mudaFee.toString(),
                provider_fees: providerFee.toString(),
                currency,
                service_name,
                product_id: data.product_id,
                sender_account: account_number,
                receiver_account: clientId.toString(),
                memo: validation_id,
                status: "PENDING",
                SessionId
            };

            // Insert transaction record
            await this.insertData("transactions", transactionData);


            if (service_name == "MOBILE_MONEY") {
                const rs = await thirdPartyHandler.handleCollection({
                    trans_id,
                    clientId: clientId.toString(),
                    amount: Number(amount).toString(),
                    phone,
                    SessionId,
                    service_name,
                    currency
                });

                await this.updateData("transactions", `trans_id='${trans_id}'`, { status: "INITIATED" });
                return rs;

            } else if (service_name == "MPESA_COLLECTION") {
                const rs = await thirdPartyHandler.handleCollection({
                    trans_id,
                    clientId: clientId.toString(),
                    amount: Number(amount).toString(),
                    phone,
                    SessionId,
                    service_name,
                    currency,
                    momoOperatorId: "MPESA"
                });
                await this.updateData("transactions", `trans_id='${trans_id}'`, { status: "INITIATED" });
                return rs;

            } else if (service_name == "INTERAC") {

                const info: any = await this.selectDataQuerySafe("third_party_accounts", { service_name });
                let primary_email = "<EMAIL>"
                let secondary_email = "<EMAIL>"
                let account_name = "Remit Payment"
                if (info.length > 0) {
                    let { account_name, primary_email, secondary_email } = info[0];
                    primary_email = primary_email
                    secondary_email = secondary_email
                    account_name = account_name
                }
                const interacRef = await MyFX.getReferenceNo(amount)
                if (interacRef.success == true) {
                    const { reference_code } = interacRef
                    await this.updateData("transactions", `trans_id='${trans_id}'`, { ext_reference: reference_code, status: "INITIATED" });

                    const instructions = {
                        "reference_code": reference_code,
                        "currency": currency,
                        "account_name": account_name,
                        "primary_email": primary_email,
                        "secondary_email": secondary_email,
                        "amount": amount,
                        "details": "Please send the exact amount to the reference code provided",
                        "valid_until": new Date(Date.now() + 60 * 60 * 1000).toISOString()
                    }
                    return this.makeResponse(200, "Transaction validated successfully", instructions);
                } else {
                    return this.makeResponse(400, "Failed to get interac reference code");
                }
            }

        } catch (error: any) {
            console.error("Error in directCollection:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }


    async directPayout(data: any, trans_id: string) {
        try {
            const validationSchema = z.object({
                clientId: z.string().or(z.number()),
                amount: z.string().or(z.number()),
                reference_id: z.string().min(5),
                currency: z.string().min(3),
                product_id: z.number().min(1),
                trans_type: z.string().min(3),
                memo: z.string().optional()
            });
            // const trans_id = uuidv4();
            this.saveTransactionLog(trans_id, "PENDING", Steps.START, 200, "DIRECT_PAYOUT", data)

            const { receiverId, trans_type } = data || ""

            const allowedType = "PUSH"
            if (trans_type != allowedType) {
                const message = "Transaction should be of type PUSH"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            // Check for replay attacks - prevent duplicate transactions within 2 minutes
            const checkDuplicate = await this.checkDuplicateTransaction(data.clientId, data.amount, data.account_number, data.currency);
            if (checkDuplicate) {
                const message = "Duplicate transaction detected. Please wait 2 minutes before retrying."
                const response_code = 410
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }
            const extra_data = data.extra_data || {}
            const validation = validationSchema.safeParse(data);
            if (!validation.success) {
                const message = "Invalid data"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message, validation.error.errors);
            }


            const productInfo = await this.getProductInfo(data.product_id, data.clientId)
            if (productInfo == null) {
                return this.makeResponse(400, "Product not found");
            }

            const { currency: productCurrency, fee_amount, min_amount, max_amount, fee_type, transaction_type, provider_fee, product_code: service_name } = productInfo


            // Extract validated data
            const { clientId, amount, reference_id, currency, memo = "Direct Payout" } = validation.data;

            try {
                const apiKey = data.apiKey
                if (apiKey.includes("test")) {
                    if (Number(amount) > 5000) {
                        return this.makeResponse(400, "You are using a test api_key, maximum limit is 5000");
                    }
                }
            } catch (error) {

            }

            if (currency != productCurrency) {
                const message = "Currency mismatch"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            if (trans_type != transaction_type) {
                const message = "Transaction type not allowed for this product"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            // Check if transaction with this reference_id already exists
            const existingTrans = await this.geTransactionByRefId(reference_id);
            if (existingTrans.length > 0) {
                const message = "Reference ID already exists"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            // Get client API keys
            const apiInfo = await this.getDecryptedApiKey(clientId.toString());
            if (apiInfo === null) {
                const message = "API Keys not found for the client"
                const response_code = 404
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            let creditWallet = "issuer"
            let receiverInfo: any = null

            if (service_name == "WALLET_PAY") {
                receiverInfo = await this.getDecryptedApiKey(receiverId);
                if (receiverInfo === null) {
                    const message = "Receiver wallet not found"
                    const response_code = 404
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }

            }


            // Generate transaction IDs
            const validation_id = uuidv4();
            const SessionId = this.generateRandom4DigitNumber();


            const parsedAmount = parseFloat(String(amount));

            if (parsedAmount < min_amount) {
                const message = `Amount must be greater than ${min_amount}`
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }
            if (parsedAmount > max_amount) {
                const message = `Amount must be less than ${max_amount}`
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }


            const { mudaFee, providerFee } = await this.calculateFee(fee_type, fee_amount, provider_fee, parsedAmount, productInfo);

            console.log("mudaFeeproviderFee", mudaFee, providerFee)

            const payment_method_id = data.payment_method_id
            const account_number = data.account_number
            if (payment_method_id == null && account_number == null) {
                const message = "Payment method or account number is required"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }

            if (service_name == "MOBILE_MONEY") {
                const phoneNumberSchema = z.string().regex(/^\d{12}$/, "Phone number must be exactly 12 digits");
                const phoneNumber = phoneNumberSchema.safeParse(account_number);
                if (!phoneNumber.success) {
                    const message = "Invalid account number, should be a valid phone number"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }
                const network = await new PegaPay().getProvider(account_number, currency)
                if (network == "UNKNOWN") {
                    const message = "Phone number network not supported"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }

            } else if (service_name == "MPESA_PAYOUT") {
                const phoneNumberSchema = z.string().regex(/^\d{12}$/, "Phone number must be exactly 12 digits");
                const phoneNumber = phoneNumberSchema.safeParse(account_number);
                if (!phoneNumber.success) {
                    const message = "Invalid account number, should be a valid phone number"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }


                const network = await new PegaPay().getProvider(account_number, currency)
                if (network == "UNKNOWN") {
                    const message = "Phone number network not supported"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }

                const account_name = extra_data.account_name
                if (account_name == "" || account_name == null) {
                    const message = "Account name is required"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }


            } else if (service_name == "INTERAC") {
                const interact_email = extra_data.interact_email
                const interact_name = extra_data.interact_name
                if (account_number != interact_email) {
                    const message = "both account number and email should be the same and valid interac email address"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }
                if (interact_email == null || interact_name == null) {
                    const message = "Invalid interact email or name"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }
                const emailSchema = z.string().email("Invalid email address");
                const email = emailSchema.safeParse(account_number);
                if (!email.success) {
                    const message = "Invalid account number, should be a valid interac email address"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }
            } else if (service_name == "BANK_TRANSFER") {
                const paymentmethod = await this.selectDataQuerySafe("payment_methods", { id: payment_method_id });
                if (paymentmethod.length == 0) {
                    const message = "Invalid account number or bank name"
                    const response_code = 400
                    this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                    return this.makeResponse(response_code, message);
                }
            }


            // Check balance before proceeding
            const balances: any = await stellar.getBalance(clientId.toString());
            const fromCurrencyBalance = balances.find((balance: any) => balance.code === currency);
            const totalDebit = parseFloat(amount.toString()) + parseFloat(mudaFee.toString());

            if (!fromCurrencyBalance || parseFloat(fromCurrencyBalance.balance) < parseFloat(totalDebit.toString())) {
                await this.updateData("transactions", `trans_id='${trans_id}'`, { status: "FAILED" });
                const message = "Insufficient balance"
                const response_code = 400
                this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, response_code, message, data)
                return this.makeResponse(response_code, message);
            }
            this.updateBalance(trans_id, clientId.toString(), currency, "PENDING", fromCurrencyBalance.balance, "START")

            const newBalance = parseFloat(fromCurrencyBalance.balance) - parseFloat(totalDebit.toString());
            const requiresApproval = await this.checkRules(clientId.toString(), amount.toString(), data.product_id);
            const txStatus = requiresApproval ? "PENDING" : "INITIATED";
            // Create transaction record
            const transactionData: TransactionInterface = {
                reference_id,
                validation_id,
                product_id: data.product_id,
                client_id: clientId.toString(),
                trans_type: "PUSH",
                trans_id,
                amount: amount.toString(),
                asset_code: currency,
                fee: mudaFee.toString(),
                currency,
                provider_fees: providerFee.toString(),
                req_amount: amount.toString(),
                service_name,
                sender_account: clientId.toString(),
                receiver_account: account_number,
                receiver_account_name: data?.extra_data?.account_name || "",
                memo: validation_id,
                payment_method_id: payment_method_id,
                running_balance: newBalance.toString(),
                status: txStatus,
                receive_currency: data.receive_currency || currency,
                SessionId
            };

            // Insert transaction record
            await this.insertData("transactions", transactionData);

            if (txStatus == "PENDING") {
                return this.makeResponse(201, "Transaction waiting for approval", transactionData);
            }

            // Debit the stellar wallet
            const { public_key: clientPublicKey, secret_key } = apiInfo;
            const assetIssuer = process.env.STELLAR_PAYOUT_ISSUER_PUBLIC || "";
            const senderSecretKey = process.env.STELLAR_PAYOUT_ISSUER_SECRET || "";
            let receiverPublicKey = assetIssuer
            let receiverSecretKey = senderSecretKey

            if (service_name == "WALLET_PAY") {
                receiverPublicKey = receiverInfo.public_key;
                receiverSecretKey = receiverInfo.secret_key;
            }
            const txArray: any = [];

            const transactionObject = {
                publicKey: receiverPublicKey,
                amount: amount.toString(),
                asset_code: currency,
                asset_issuer: assetIssuer,
                senderSecretKey: secret_key,
                creditPrivateKey: receiverSecretKey,
            };
            txArray.push(transactionObject);
            // Add fee transaction if applicable
            const feeAcc = process.env.FEES_ACCOUNT || ""
            const feesAccount = await this.getDecryptedApiKey(feeAcc);
            if (feesAccount !== null && parseFloat(mudaFee.toString()) > 0) {
                const { public_key: feePublic, secret_key: feeSecrete } = feesAccount
                const feeTransactionObject = {
                    publicKey: feePublic,
                    amount: parseFloat(mudaFee.toString()).toFixed(2),
                    asset_code: currency,
                    asset_issuer: assetIssuer,
                    senderSecretKey: secret_key,
                    creditPrivateKey: feeSecrete,
                };
                txArray.push(feeTransactionObject);
            }


            // Update transaction status
            const stellarResponse = await stellar.makeBatchTransfers(trans_id, memo, txArray);
            if (stellarResponse.response !== 1) {
                await this.updateData("transactions", `trans_id='${trans_id}'`, { system_status: "DEBIT_FAILED" });
                await this.updateTransaction(trans_id, "FAILED", StatusCodes.FAILED.message, stellarResponse)
                const message = "TRANSACTION FAILED"
                const response_code = 400
                this.saveTransactionLog(trans_id, "DEBIT_FAILED", Steps.VALIDATION, response_code, message, stellarResponse)
                return this.makeResponse(response_code, message);
            }

            const hasThirdpartyBalance = await this.checkThirdpartyBalance(trans_id, amount, currency, service_name, clientId)
            if (hasThirdpartyBalance == false) {
                this.sendEmail("BALANCE_ALERT", "AWAITING_ADMIN_APPROVAL", "AWAITING_ADMIN_APPROVAL")
                await this.updateTransaction(trans_id, "ONHOLD", "AWAITING_ADMIN_APPROVAL", stellarResponse)
                //   return this.makeResponse(200, `Transaction being processed`);
            }

            await this.updateTransaction(trans_id, "RECEIVED", StatusCodes.RECEIVED.message, stellarResponse)
            const payoutResponse = await thirdPartyHandler.handlePayout({
                trans_id,
                clientId: clientId.toString(),
                amount: amount.toString(),
                phone: data.account_number,
                SessionId,
                service_name,
                currency,
                extra_data,
                reference_id: reference_id
            });
            // await this.updateTransaction(trans_id, "PENDING", payoutResponse)
            return payoutResponse;
        } catch (error: any) {
            console.error("Error in directPayout:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }
    async calculateFee(fee_type: any, fee_amount: any, provider_fee: any, parsedAmount: number, productInfo: any): Promise<{ mudaFee: any; providerFee: any; }> {

        const { originalFeeType, originalFeeAmount } = productInfo
        let fallBackFeeAmount = originalFeeAmount;

        if (originalFeeType == "PERCENTAGE") {
            fallBackFeeAmount = (originalFeeAmount / 100) * parsedAmount;
        }

        let mudaFee = fee_amount;
        let providerFee = provider_fee;
        if (fee_type == "PERCENTAGE") {
            mudaFee = (fee_amount / 100) * parsedAmount;
            providerFee = (provider_fee / 100) * parsedAmount;
        } else if (fee_type == "TIER") {
            const { custome_fee_id } = productInfo;
            const customeFee: any = await this.callQuerySafe(
                "SELECT * FROM tier_fees WHERE custome_fee_id = ? AND ? BETWEEN min_amount AND max_amount",
                [custome_fee_id, parsedAmount]
            );
            if (customeFee.length > 0) {
                const customeFeeType = customeFee[0].fee_type;
                if (customeFeeType == "PERCENTAGE") {
                    mudaFee = (customeFee[0].fee_value / 100) * parsedAmount;
                } else if (customeFeeType == "FLAT") {
                    mudaFee = customeFee[0].fee_value;
                }
            } else {
                mudaFee = fallBackFeeAmount;
            }
        }
        return { mudaFee, providerFee };
    }


    async checkThirdpartyBalance(trans_id: any, amount: any, currency: any, service_name: any, clientId: any) {
        let utilaBalance = null
        if (service_name == "CRYPTO_TRANSFER" || service_name == "LIQUIDITY_RAIL") {
            utilaBalance = await getUserWallet()
            console.log('utilaBalance', utilaBalance)
        } else if (service_name == "MOBILE_MONEY") {
            utilaBalance = await new PegaPay().getPullBalance()
        }
        this.saveTransactionLog(trans_id, "PENDING", Steps.VALIDATION, 200, service_name, utilaBalance)
        return true
    }


    async checkDuplicateTransaction(clientId: any, amount: any, receiverId: any, currency: any) {
        const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000);
        const Transactoion: any = await this.callQuerySafe(
            "SELECT * FROM transactions WHERE client_id = ? AND amount = ? AND receiver_account = ? AND currency = ? AND created_at > ?",
            [clientId, amount, receiverId, currency, twoMinutesAgo.toISOString()]
        );
        if (Transactoion.length > 0) {
            return true;
        }
        return false;
    }


    async completeAdminReqest(data: any) {
        console.log()
        const { operation, adminId, transId } = data

        if (!operation || !adminId || !transId) {
            const message = "Missing required parameters: operation, adminId, transId"

            return this.makeResponse(400, message)
        }

        const validOperations = ['APPROVE_DEPOSIT', 'APPROVE_SWAP'];
        if (!validOperations.includes(operation)) {
            const message = "Invalid operation. Must be one of: APPROVE_DEPOSIT, APPROVE_SWAP"
            return this.makeResponse(400, message)
        }

        let result;

        switch (operation) {
            case 'APPROVE_DEPOSIT':
                result = await this.issueTokens(transId);
                break;
            case 'APPROVE_SWAP':
                result = await this.SwapTokens(transId);
                break;
            default:
                return this.makeResponse(400, "Invalid request")
        }
        return result
    }

    async retryCollection(trans_id: string) {
        try {
            const transaction: any = await this.getTransactionObj(trans_id)
            if (transaction == false) {
                return this.makeResponse(404, "Transaction id not found");
            }
            const { client_id, service_name, sender_account, status, amount, SessionId, currency, retry_count } = transaction
            if (status == "ONHOLD" && retry_count == 0) {
                const rs = await thirdPartyHandler.handleCollection({
                    trans_id,
                    clientId: client_id.toString(),
                    amount: Number(amount).toString(),
                    phone: sender_account,
                    SessionId,
                    service_name,
                    currency
                });

                await this.updateData("transactions", `trans_id='${trans_id}'`, { status: "INITIATED", retry_count: 1 });
                return rs
            }
            return this.makeResponse(400, "Transaction not found");
        } catch (error: any) {
            console.error("Error in retryCollection:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }
    async retryPayout(trans_id: string) {
        try {
            const transaction: any = await this.getTransactionObj(trans_id)
            if (transaction == false) {
                return this.makeResponse(404, "Transaction id not found");
            }
            const { client_id, service_name, status, amount, receiver_account, reference_id, currency, memo = "Direct Payout", SessionId, receiver_account_name } = transaction
            if (status == "SUCCESS") {
                return this.makeResponse(400, "Transaction already processed");
            }
            if (status == "FAILED") {
                return this.makeResponse(400, "Transaction failed");
            }
            if (status == "ONHOLD") {
                await this.updateTransaction(trans_id, "RECEIVED", StatusCodes.RECEIVED.message, transaction)
                const payoutResponse = await thirdPartyHandler.handlePayout({
                    trans_id,
                    clientId: client_id.toString(),
                    amount: parseInt(amount).toString(),
                    phone: receiver_account,
                    SessionId: SessionId,
                    service_name,
                    currency,
                    extra_data: { "account_name": receiver_account_name },
                    reference_id: reference_id
                });
            }
            return this.makeResponse(400, "Transaction not found");
        } catch (error: any) {
            console.error("Error in retryPayout:", error);
            return this.makeResponse(500, "Server error", error.message);
        }
    }




    async checkRules(clientId: string, amount: string, product_id: any) {
        try {
            const rules: any = await this.callQuerySafe(
                `SELECT * FROM rl_clients INNER JOIN rl_product_rules ON rl_clients.client_id = rl_product_rules.client_id WHERE rl_clients.client_id = ? AND rl_product_rules.product_id = ?`,
                [clientId, product_id]
            );
            console.log(`rules`, rules)
            if (rules.length == 0) {
                return false;
            }
            const { min_amount, max_amount, requires_approval } = rules[0];
            if (amount > max_amount) {
                return true;
            }
            return requires_approval;
        } catch (error: any) {
            console.error("Error in checkRules:", error);
            return false;
        }
    }

    private verifySignature(signature: string, payload: string, publicKey: string): boolean {
        try {
            const verifier = crypto.createVerify('sha512');
            verifier.update(payload);
            verifier.end();
            return verifier.verify(
                { key: publicKey, padding: crypto.constants.RSA_PKCS1_PSS_PADDING },
                Buffer.from(signature, 'base64')
            );
        } catch (error) {
            console.error('Signature verification error:', error);
            return false;
        }
    }




}

export default Transactions;



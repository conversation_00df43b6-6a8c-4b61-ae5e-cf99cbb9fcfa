import Model from "../../../admin/src/helpers/model";
import { v4 as uuidv4 } from "uuid";
import dotenv from "dotenv";
import StellarService from "../../../admin/src/helpers/StellarService";
import PegaPay from "../intergrations/PegPay";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import Admin from "../../../admin/src/models/admin";
import LiquidityClient from "../intergrations/LR";
const lr = new LiquidityClient();

dotenv.config();
const stellar = new StellarService()
const pg = new PegaPay()
const admin = new Admin();
const thirdPartyHandler = new ThirdPartyHandler();

interface WebhookUtiliaInterface {
    id: string;
    vault: string;
    type: string;
    details: any;
    resourceType: string;
    resource: string;
}

interface OrderInterface {
    order_id: string;
    symbol: string;
    user_id: string;
    selling: string;
    buying: string;
    price: string;
    quantity: string;
}

interface ExchangeRequestData {
    clientId: string;
    amount: number;
    currency: string;
    productId?: string;
    fromCurrency?: string;
    toCurrency?: string;
    rate?: number;
}

class Exchange extends Model {
    private issuerPublicKey: string;

    constructor() {
        super();
        this.issuerPublicKey = process.env.STELLAR_ISSUER_PUBLIC || "";
    }

    /**
     * Request an exchange/swap between currencies
     */
    async requestExchange(data: ExchangeRequestData) {
        try {
            const { clientId, amount, currency, productId, fromCurrency, toCurrency, rate } = data;
            
            const orderId = uuidv4();
            const order = {
                order_id: orderId,
                client_id: clientId,
                amount: amount,
                currency: currency,
                product_id: productId || null,
                from_currency: fromCurrency || currency,
                to_currency: toCurrency || null,
                rate: rate || null,
                status: 'PENDING',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const result = await this.insertData("exchange_orders", order);
            return this.makeResponse(200, "Exchange request created successfully", {
                orderId: orderId,
                ...order
            });
        } catch (error: any) {
            console.error("Error creating exchange request:", error);
            return this.makeResponse(500, "Failed to create exchange request", error.message);
        }
    }

    /**
     * Get all available orders for a client
     */
    async getOrders(clientId: string) {
        try {
            const orders = await this.callQuerySafe(
                "SELECT * FROM exchange_orders WHERE client_id = ? OR status = 'OPEN'",
                [clientId]
            );
            
            return this.makeResponse(200, "Orders retrieved successfully", orders);
        } catch (error: any) {
            console.error("Error fetching orders:", error);
            return this.makeResponse(500, "Failed to fetch orders", error.message);
        }
    }

    /**
     * Take/accept an order
     */
    async takeOrders(data: any) {
        try {
            const { orderId, clientId, takerAmount } = data;
            
            // Check if order exists and is available
            const existingOrder:any = await this.callQuerySafe(
                "SELECT * FROM exchange_orders WHERE order_id = ? AND status = 'OPEN'",
                [orderId]
            );

            if (!existingOrder || existingOrder.length === 0) {
                return this.makeResponse(404, "Order not found or not available");
            }

            // Update order status
            const updateData = {
                taker_client_id: clientId,
                taker_amount: takerAmount,
                status: 'MATCHED',
                matched_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            await this.updateData("exchange_orders", `order_id = '${orderId}'`, updateData);
            
            return this.makeResponse(200, "Order taken successfully", {
                orderId: orderId,
                status: 'MATCHED'
            });
        } catch (error: any) {
            console.error("Error taking order:", error);
            return this.makeResponse(500, "Failed to take order", error.message);
        }
    }

    /**
     * Get orders created by a specific client
     */
    async getMyOrders(clientId: string) {
        try {
            const orders = await this.selectDataQuerySafe(
                "exchange_orders", 
                { client_id: clientId }
            );
            
            return this.makeResponse(200, "My orders retrieved successfully", orders);
        } catch (error: any) {
            console.error("Error fetching my orders:", error);
            return this.makeResponse(500, "Failed to fetch my orders", error.message);
        }
    }

    /**
     * Cancel an order
     */
    async cancelOrder(orderId: string, clientId: string) {
        try {
            const updateData = {
                status: 'CANCELLED',
                cancelled_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            await this.updateData(
                "exchange_orders", 
                `order_id = '${orderId}' AND client_id = '${clientId}' AND status IN ('OPEN', 'PENDING')`, 
                updateData
            );
            
            return this.makeResponse(200, "Order cancelled successfully");
        } catch (error: any) {
            console.error("Error cancelling order:", error);
            return this.makeResponse(500, "Failed to cancel order", error.message);
        }
    }

    /**
     * Get exchange rates
     */
    async getExchangeRates(baseCurrency?: string, quoteCurrency?: string) {
        try {
            let query = "1=1";
            if (baseCurrency) {
                query += ` AND base_currency = '${baseCurrency}'`;
            }
            if (quoteCurrency) {
                query += ` AND quote_currency = '${quoteCurrency}'`;
            }

            const rates = await this.callQuerySafe(`SELECT * FROM exchange_rates WHERE ${query}`, []);
            return this.makeResponse(200, "Exchange rates retrieved successfully", rates);
        } catch (error: any) {
            console.error("Error fetching exchange rates:", error);
            return this.makeResponse(500, "Failed to fetch exchange rates", error.message);
        }
    }
}

export default Exchange;


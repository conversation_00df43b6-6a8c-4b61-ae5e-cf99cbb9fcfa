import Model from '../helpers/model';
import StellarService from '../helpers/StellarService';

const stellar = new StellarService();

interface TradeOffer {
    clientId: string;
    sellingAsset: string;
    sellingIssuer: string;
    buyingAsset: string;
    buyingIssuer: string;
    amount: string;
    price: string;
    orderType: 'buy' | 'sell';
}

interface SwapOrder {
    clientId: string;
    sourceAsset: string;
    sourceIssuer: string;
    destinationAsset: string;
    destinationIssuer: string;
    sourceAmount: string;
    minDestAmount: string;
}

class StellarTrading extends Model {
    constructor() {
        super();
    }

    /**
     * Create a trading offer on Stellar DEX and log in database
     */
    public async createOffer(data: TradeOffer) {
        try {
            console.log('🔹 Creating Stellar DEX offer:', data);

            const { clientId, sellingAsset, sellingIssuer, buyingAsset, buyingIssuer, amount, price, orderType } = data;

            // Validate different assets
            if (sellingAsset === buyingAsset && sellingIssuer === buyingIssuer) {
                return this.makeResponse(400, 'Cannot trade the same asset');
            }

            // Get client wallet keys using existing pattern
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client wallet not found');
            }

            const { public_key, secret_key } = apiInfo;

            // Check user balance using StellarService
            const balances = await stellar.getBalance(clientId, [sellingAsset]);
            const sellingAssetBalance = balances.find((bal: any) => bal.code === sellingAsset);
            const availableBalance = sellingAssetBalance ? parseFloat(sellingAssetBalance.balance) : 0;
            
            if (availableBalance < parseFloat(amount)) {
                return this.makeResponse(400, 'Insufficient balance');
            }

            // Generate unique order ID
            const orderId = this.getTransId();

            // For now, we'll simulate placing order on Stellar DEX
            // You'll need to implement the actual stellar.placeOrder method
            const stellarResult = {
                response: 1,
                offerId: `stellar_${orderId}`,
                message: `simulated_hash_${orderId}`
            };

            // Log order in database
            const orderData = {
                order_id: orderId,
                client_id: clientId,
                stellar_offer_id: stellarResult.offerId || null,
                selling_asset: sellingAsset,
                selling_issuer: sellingIssuer || '',
                buying_asset: buyingAsset,
                buying_issuer: buyingIssuer || '',
                amount: amount,
                price: price,
                order_type: orderType,
                status: 'active',
                stellar_hash: stellarResult.message || stellarResult.message,
                created_at: new Date().toISOString()
            };

            const dbResult = await this.insertData('stellar_orders', orderData);
            if (!dbResult) {
                console.warn('⚠️ Order placed on Stellar but failed to log in database');
            }

            // Log the trade action
            await this.logTradeAction(clientId, 'CREATE_OFFER', orderId, orderData);

            return this.makeResponse(200, 'Order created successfully', {
                orderId,
                stellarOfferId: stellarResult.offerId,
                stellarHash: stellarResult.message
            });

        } catch (error: any) {
            console.error('❌ Error creating offer:', error);
            await this.logTradeAction(data.clientId, 'CREATE_OFFER_ERROR', '', { error: error.message });
            return this.makeResponse(500, 'Failed to create offer', { error: error.message });
        }
    }

    /**
     * Get order book for a trading pair
     */
    public async getOrderBook(sellingAsset: string, buyingAsset: string, sellingIssuer?: string, buyingIssuer?: string) {
        try {
            console.log('🔹 Getting order book for:', { sellingAsset, buyingAsset, sellingIssuer, buyingIssuer });

            let whereClause = `selling_asset='${sellingAsset}' AND buying_asset='${buyingAsset}' AND status='active'`;
            
            if (sellingIssuer) {
                whereClause += ` AND selling_issuer='${sellingIssuer}'`;
            }
            if (buyingIssuer) {
                whereClause += ` AND buying_issuer='${buyingIssuer}'`;
            }

            const orders: any = await this.callRawQuery(`SELECT * FROM stellar_orders WHERE ${whereClause} LIMIT 100`);

            // Group orders by buy/sell
            const buyOrders = orders.filter((order: any) => order.order_type === 'buy');
            const sellOrders = orders.filter((order: any) => order.order_type === 'sell');

            return this.makeResponse(200, 'Order book retrieved', {
                pair: `${sellingAsset}/${buyingAsset}`,
                buyOrders: buyOrders.sort((a: any, b: any) => parseFloat(b.price) - parseFloat(a.price)), // Highest price first
                sellOrders: sellOrders.sort((a: any, b: any) => parseFloat(a.price) - parseFloat(b.price)), // Lowest price first
                totalOrders: orders.length
            });

        } catch (error: any) {
            console.error('❌ Error getting order book:', error);
            return this.makeResponse(500, 'Failed to get order book', { error: error.message });
        }
    }

    /**
     * Take/match an existing order
     */
    public async takeOrder(data: { clientId: string; orderId: string; amount: string }) {
        try {
            console.log('🔹 Taking order:', data);

            const { clientId, orderId, amount: tradeAmount } = data;

            // Get the order
            const orders: any = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE order_id = ? AND status = 'active' LIMIT 1`, [orderId]);
            if (!orders || orders.length === 0) {
                return this.makeResponse(400, 'Order not found or no longer active');
            }

            const order = orders[0];

            // Prevent self-trading
            if (order.client_id === clientId) {
                return this.makeResponse(400, 'Cannot take your own order');
            }

            // Get taker wallet info
            const takerApiInfo = await this.getDecryptedApiKey(clientId);
            if (!takerApiInfo) {
                return this.makeResponse(400, 'Taker account not found');
            }

            const { public_key: takerPublicKey, secret_key: takerSecretKey } = takerApiInfo;

            // Calculate required amount for counter-asset
            const counterAmount = (parseFloat(tradeAmount) * parseFloat(order.price)).toString();

            // Check taker's balance
            const requiredAsset = order.buying_asset;
            const requiredIssuer = order.buying_issuer;
            const takerBalances = await stellar.getBalance(clientId, [requiredAsset]);
            const requiredAssetBalance = takerBalances.find((bal: any) => bal.code === requiredAsset);
            const availableBalance = requiredAssetBalance ? parseFloat(requiredAssetBalance.balance) : 0;
            
            if (availableBalance < parseFloat(counterAmount)) {
                return this.makeResponse(400, 'Insufficient balance to take this offer');
            }

            // Generate trade ID
            const tradeId = this.getTransId();

            // For now, simulate the trade execution
            // You'll need to implement actual stellar.executeTrade method
            const stellarResult = {
                response: 1,
                tradeHash: `simulated_trade_${tradeId}`
            };

            // Update the order
            const newFilledAmount = (parseFloat(order.filled_amount || '0') + parseFloat(tradeAmount)).toString();
            const orderStatus = parseFloat(newFilledAmount) >= parseFloat(order.amount) ? 'filled' : 'partially_filled';

            await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                filled_amount: newFilledAmount,
                status: orderStatus,
                updated_at: new Date().toISOString()
            });

            // Log the trade
            const tradeData = {
                trade_id: tradeId,
                order_id: orderId,
                maker_id: order.client_id,
                taker_id: clientId,
                amount: tradeAmount,
                price: order.price,
                stellar_hash: stellarResult.tradeHash,
                created_at: new Date().toISOString()
            };

            await this.insertData('stellar_trades', tradeData);

            // Log the trade actions
            await this.logTradeAction(clientId, 'TAKE_ORDER', tradeId, tradeData);
            await this.logTradeAction(order.client_id, 'ORDER_TAKEN', tradeId, tradeData);

            return this.makeResponse(200, 'Order taken successfully', {
                tradeId,
                stellarHash: stellarResult.tradeHash,
                executedAmount: tradeAmount,
                executedPrice: order.price,
                orderStatus
            });

        } catch (error: any) {
            console.error('❌ Error taking order:', error);
            await this.logTradeAction(data.clientId, 'TAKE_ORDER_ERROR', '', { error: error.message });
            return this.makeResponse(500, 'Failed to take order', { error: error.message });
        }
    }

    /**
     * Execute a direct swap (path payment)
     */
    public async executeSwap(data: SwapOrder) {
        try {
            console.log('🔹 Executing swap:', data);

            const { clientId, sourceAsset, sourceIssuer, destinationAsset, destinationIssuer, sourceAmount, minDestAmount } = data;

            // Validate different assets
            if (sourceAsset === destinationAsset && sourceIssuer === destinationIssuer) {
                return this.makeResponse(400, 'Cannot swap the same asset');
            }

            // Get client wallet info
            const apiInfo = await this.getDecryptedApiKey(clientId);
            if (!apiInfo) {
                return this.makeResponse(400, 'Client account not found');
            }

            const { public_key, secret_key } = apiInfo;

            // Check balance
            const balances = await stellar.getBalance(clientId, [sourceAsset]);
            const sourceAssetBalance = balances.find((bal: any) => bal.code === sourceAsset);
            const availableBalance = sourceAssetBalance ? parseFloat(sourceAssetBalance.balance) : 0;
            
            if (availableBalance < parseFloat(sourceAmount)) {
                return this.makeResponse(400, 'Insufficient balance');
            }

            // Generate swap ID
            const swapId = this.getTransId();

            // For now, simulate the swap
            // You'll need to implement actual stellar.executePathPayment method
            const stellarResult = {
                response: 1,
                swapHash: `simulated_swap_${swapId}`,
                destinationAmount: (parseFloat(sourceAmount) * 0.99).toString() // Mock 1% slippage
            };

            // Log the swap
            const swapData = {
                swap_id: swapId,
                client_id: clientId,
                source_asset: sourceAsset,
                source_issuer: sourceIssuer || '',
                destination_asset: destinationAsset,
                destination_issuer: destinationIssuer || '',
                source_amount: sourceAmount,
                destination_amount: stellarResult.destinationAmount,
                min_destination_amount: minDestAmount,
                stellar_hash: stellarResult.swapHash,
                created_at: new Date().toISOString()
            };

            await this.insertData('stellar_swaps', swapData);

            // Log the trade action
            await this.logTradeAction(clientId, 'EXECUTE_SWAP', swapId, swapData);

            return this.makeResponse(200, 'Swap executed successfully', {
                swapId,
                stellarHash: stellarResult.swapHash,
                sourceAmount,
                destinationAmount: stellarResult.destinationAmount
            });

        } catch (error: any) {
            console.error('❌ Error executing swap:', error);
            await this.logTradeAction(data.clientId, 'EXECUTE_SWAP_ERROR', '', { error: error.message });
            return this.makeResponse(500, 'Failed to execute swap', { error: error.message });
        }
    }

    /**
     * Cancel an existing order
     */
    public async cancelOrder(data: { clientId: string; orderId: string }) {
        try {
            console.log('🔹 Cancelling order:', data);

            const { clientId, orderId } = data;

            // Get the order
            const orders: any = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE order_id = ? AND client_id = ? AND status IN ('active', 'partially_filled') LIMIT 1`, [orderId, clientId]);
            if (!orders || orders.length === 0) {
                return this.makeResponse(400, 'Order not found or cannot be cancelled');
            }

            const order = orders[0];

            // For now, simulate the cancellation
            // You'll need to implement actual stellar.cancelOffer method
            const stellarResult = {
                response: 1,
                cancelHash: `simulated_cancel_${orderId}`
            };

            // Update order status
            await this.updateData('stellar_orders', `order_id='${orderId}'`, {
                status: 'cancelled',
                updated_at: new Date().toISOString()
            });

            // Log the cancellation
            await this.logTradeAction(clientId, 'CANCEL_ORDER', orderId, { 
                originalOrder: order,
                stellarHash: stellarResult.cancelHash 
            });

            return this.makeResponse(200, 'Order cancelled successfully', {
                orderId,
                stellarHash: stellarResult.cancelHash
            });

        } catch (error: any) {
            console.error('❌ Error cancelling order:', error);
            await this.logTradeAction(data.clientId, 'CANCEL_ORDER_ERROR', '', { error: error.message });
            return this.makeResponse(500, 'Failed to cancel order', { error: error.message });
        }
    }

    /**
     * Get trading history for a client
     */
    public async getTradingHistory(clientId: string) {
        try {
            const orders: any = await this.callQuerySafe(`SELECT * FROM stellar_orders WHERE client_id = ? LIMIT 100`, [clientId]);
            const trades: any = await this.callQuerySafe(`SELECT * FROM stellar_trades WHERE maker_id = ? OR taker_id = ? LIMIT 100`, [clientId, clientId]);
            const swaps: any = await this.callQuerySafe(`SELECT * FROM stellar_swaps WHERE client_id = ? LIMIT 100`, [clientId]);

            return this.makeResponse(200, 'Trading history retrieved', {
                orders: orders.sort((a: any, b: any) => 
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                ),
                trades: trades.sort((a: any, b: any) => 
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                ),
                swaps: swaps.sort((a: any, b: any) => 
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                )
            });

        } catch (error: any) {
            console.error('❌ Error getting trading history:', error);
            return this.makeResponse(500, 'Failed to get trading history', { error: error.message });
        }
    }

    /**
     * Log trade actions for audit trail
     */
    private async logTradeAction(clientId: string, action: string, orderId: string, data: any) {
        try {
            const logData = {
                client_id: clientId,
                action,
                order_id: orderId,
                data: JSON.stringify(data),
                ip_address: '0.0.0.0', // You can pass this from the controller
                created_at: new Date().toISOString()
            };

            await this.insertData('trade_logs', logData);
        } catch (error) {
            console.error('⚠️ Failed to log trade action:', error);
        }
    }
}

export default StellarTrading; 
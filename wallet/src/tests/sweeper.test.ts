import { SweeperService } from '../services/sweeper.service';

/**
 * Test script for the SweeperService
 * This demonstrates how to use the enhanced sweeper functionality
 */

async function testSweeperService() {
  console.log('🧪 Testing SweeperService...\n');

  const sweeper = new SweeperService();

  try {
    // Test 1: Check sweep timing
    console.log('📋 Test 1: Checking sweep timing...');
    const shouldRun = await sweeper.shouldRunSweep(60);
    console.log(`Should run sweep: ${shouldRun ? 'Yes' : 'No'}\n`);

    // Test 2: Get sweep statistics
    console.log('📊 Test 2: Getting sweep statistics...');
    const stats = await sweeper.getSweepStats();
    console.log('Sweep Statistics:', JSON.stringify(stats, null, 2), '\n');

    // Test 3: Get sweep history
    console.log('📜 Test 3: Getting sweep history...');
    const history = await sweeper.getSweepHistory(5);
    console.log(`Found ${history.length} recent sweep operations\n`);

    // Test 4: Dry run sweep (safe testing)
    console.log('🔍 Test 4: Running dry run sweep...');
    const dryRunResult = await sweeper.runAutomatedSweep({
      dryRun: true, // Safe - won't actually transfer funds
      minAmount: '1', // Minimum amount to sweep
      excludeAssets: ['native.ethereum/ETH'], // Exclude ETH from sweeping
      memo: 'Test sweep operation'
    });

    console.log('Dry Run Results:');
    console.log(`Success: ${dryRunResult.success}`);
    console.log(`Message: ${dryRunResult.message}`);
    console.log(`Total Wallets: ${dryRunResult.summary.totalWallets}`);
    console.log(`Successful Sweeps: ${dryRunResult.summary.successfulSweeps}`);
    console.log(`Failed Sweeps: ${dryRunResult.summary.failedSweeps}`);
    console.log(`Skipped Sweeps: ${dryRunResult.summary.skippedSweeps}`);
    console.log(`Total Amount: ${dryRunResult.summary.totalAmount}\n`);

    // Test 5: Sweep from specific wallet (if you have a test wallet)
    console.log('🎯 Test 5: Sweeping from specific wallet...');
    const specificWalletResult = await sweeper.sweepFromWallet({
      walletId: '10339707', // Replace with actual test wallet ID
      destinationWalletId: '9a185f915491', // Cold wallet
      minAmount: '0.1',
      dryRun: true, // Safe testing
      memo: 'Test sweep from specific wallet'
    });

    console.log('Specific Wallet Sweep Results:');
    console.log(`Success: ${specificWalletResult.success}`);
    console.log(`Message: ${specificWalletResult.message}`);
    console.log(`Total Assets: ${specificWalletResult.summary.totalAssets}`);
    console.log(`Successful Sweeps: ${specificWalletResult.summary.successfulSweeps}`);
    console.log(`Failed Sweeps: ${specificWalletResult.summary.failedSweeps}`);
    console.log(`Total Amount: ${specificWalletResult.summary.totalAmount}\n`);

    console.log('✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Example usage functions
export async function runSweepExample() {
  console.log('🚀 Running SweeperService Example...\n');

  const sweeper = new SweeperService();

  // Example 1: Run automated sweep with custom options
  const result = await sweeper.runAutomatedSweep({
    minAmount: '5', // Only sweep amounts above 5
    excludeAssets: ['native.ethereum/ETH'], // Don't sweep ETH
    includeAssets: ['assets/ethereum/USDC', 'assets/bsc/USDT'], // Only sweep USDC and USDT
    dryRun: false, // Set to true for safe testing
    memo: 'Automated daily sweep'
  });

  console.log('Sweep Result:', JSON.stringify(result, null, 2));
}

export async function runManualSweepExample() {
  console.log('🔧 Running Manual Sweep Example...\n');

  const sweeper = new SweeperService();

  // Example 2: Manual sweep from specific wallet
  const result = await sweeper.sweepFromWallet({
    walletId: '10339707', // Replace with actual wallet ID
    destinationWalletId: '9a185f915491', // Cold wallet
    minAmount: '1',
    excludeAssets: ['native.ethereum/ETH'],
    dryRun: true, // Safe testing
    memo: 'Manual sweep operation'
  });

  console.log('Manual Sweep Result:', JSON.stringify(result, null, 2));
}

// Run tests if this file is executed directly
if (require.main === module) {
  testSweeperService()
    .then(() => {
      console.log('\n🎉 SweeperService tests completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 SweeperService tests failed:', error);
      process.exit(1);
    });
}

export { testSweeperService }; 
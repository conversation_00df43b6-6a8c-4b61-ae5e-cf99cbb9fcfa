import { createAddress, getFormattedBalances, getWalletBalances, initiatePayout, listWalletAddresses, queryBalances } from "../intergrations/Utilia";
import Transactions from "../models/transactions";
import { v4 as uuidv4 } from 'uuid';
import LR, { QuoteInfo } from "../intergrations/LR";
import Internal from "../models/internal";
import PegaPay from "../intergrations/PegPay";
import HoneyCoin from "../intergrations/HoneyCoin";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import { getFormattedWalletBalances } from '../intergrations/Utilia';
import { getGasWalletId, getColdWalletId, getPayoutWalletId } from '../helpers/walletConfig';
import  EmailSender  from "../helpers/email";
import Model from "../helpers/model";
import { SweeperService } from "../services/sweeper.service";

const thirdPartyHandler = new ThirdPartyHandler();

export default class Test {
  constructor() {
    const username = 'iranks20'
 //   this.main()
    //  this.runHoneyCoinTests()
    this.notification()
  }


  // send notification when the deployment happens
  async notification() {
    const message = "Deployment completed successfully"
    const result = await new Model().sendEmail("DEPLOYMENT_NOTIFICATION","<EMAIL>")

    console.log("result", result)
  }
  public async main(): Promise<void> {
    console.log('getPayoutWalletId()', getPayoutWalletId())
    // const network = await new PegaPay().getProvider("************")
    // console.log(`network`, network)

    const data = {
      clientId: "10819033",
      amount: "10",
      asset: "USDC",
      emailCode: "",
      payment_method_id: "1ffa377bad4e04480801b1c0df7cd4473",
      quote_id: "1",
      receive_amount: "3582.64",
      receive_currency: "UGX",
      token: "068679",
      trans_type: "LIQUIDITY_RAIL"
    }



    //  const reverse = await new LR().getRate("USDC", "UGX", 10)
    //  console.log('Reverse:', reverse);

    //  const reverse = await new Transactions().sendTransaction(data)
    //  console.log('Reverse:', reverse);

    //  const transfer = await MyFX.makePayout("payment", "<EMAIL>", 1 )
    // console.log('Transfer Result:', transfer);
    // const balance  = await MyFX.getBalance();

    //  const paymentCode = await MyFX.getReferenceNo(1000);
    //  console.log('Payment Code:', paymentCode);
    //  console.log('Balance:', balance);
    //   const payment =  await new PegPay().validatePhoneNumber("+************")
    // console.log('Hello World',payment)
    //   new Accounts().addCurrencies();
    // await this.testQuote();
    //  await this.utliAsset();
  }

  ///  TESTS FOR HONEYCOIN
  async runHoneyCoinTests() {
    // this.testHoneyCoinPayout()
    // this.testHoneyCoinCollection()
    // this.testHoneyCoinRate()
    // this.testHoneyCoinOffRampRate()
    //this.testHoneyCoinTransaction()
  }

  async testUtiliaBalances() {
    const utilaBalances = await getFormattedBalances() || [];

    const gasBalances = await getFormattedWalletBalances(getGasWalletId());
    const coldBalances = await getFormattedWalletBalances(getColdWalletId());
    const payoutBalances = await getFormattedWalletBalances(getPayoutWalletId());

    const balances = {
      utilaBalances,
      gasBalances,
      payoutBalances,
      coldBalances,
      walletIds: {
        gas: getGasWalletId(),
        cold: getColdWalletId(),
        payout: getPayoutWalletId()
      }
    }
    console.log('Balances:', JSON.stringify(balances, null, 2));
  }
  async testHoneyCoinPayout() {
    const amountToSend: number = Math.trunc(parseFloat(Number(200).toString()));
    if (amountToSend > 0) {

      const reference = await this.uniqueReference()
      const honeycoin = new HoneyCoin();
      const healthResponse20 = await honeycoin.createPayout(
        amountToSend,
        "UGX",
        "UG",
        `${reference}`,
        "Damba Paul",
        "256776439250",
        "MTN-UGX-PT", // AIRTEL-UGX-PT
        "MoMo",
        "UG54147"
      );
      console.log('payout status >>>>>>>>>>>>>>>>>>>>> :', healthResponse20);
    }
  }

  async testHoneyCoinCollection() {
    const reference = await this.uniqueReference()
    const honeycoin = new HoneyCoin();
    const healthResponse202 = await honeycoin.createCollection(
      1000,
      "256705770761",
      "UGX",
      `${reference}`,
      "AIRTEL-UGX-PT")
    console.log('collection payment status:', healthResponse202);
  }

  async testHoneyCoinRate() {
    const honeycoin = new HoneyCoin();
    const healthResponse3: any = await honeycoin.getFXRate("USDT", "UGX", 2);
    console.log('off rate:', healthResponse3);
  }

  async testHoneyCoinOffRampRate() {
    const honeycoin = new HoneyCoin();
    const healthResponse3: any = await honeycoin.getOffRampRate("USDT", "UGX", 2);
    console.log('off ramp rate:', healthResponse3);
  }

  async testHoneyCoinTransaction() {
    const honeycoin = new HoneyCoin();
    const healthResponse3: any = await honeycoin.getTransaction("**********");
    console.log('transaction status:', healthResponse3);
  }

  async uniqueReference() {
    return `MUDA_PAYMENT${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
  }

  async getBanks() {
  }
  async getBankBranch() {
  }
  async getMobileMoneyProviders() {
  }

  ///  END TESTS FOR HONEYCOIN
  async utliAsset() {
    const assets = await queryBalances();
    console.log('Assets:', JSON.stringify(assets, null, 2));
  }
  async getRate(symbol: string, currency: string, amount: number) {
    const rate = await new LR().getRate(symbol, currency, amount);
    console.log('Rate:', rate);
  }

  async testQuote() {

    const quoteInfo: QuoteInfo = {
      quote_id: "**********",
      provider_id: "3",
      reference_id: "**********",
      service_name: "LIQUIDITY_RAIL",
      send_asset: "USDT",
      send_amount: "1",
      service_id: "1",
      chain: "TRON",
      source: process.env.LR_SOURCE || "LR_PAYOUT",
      payment_method_id: "**********"
    };
    const quote = await new LR().createQuote(quoteInfo);
    console.log('Quote:', quote);
  }
  async testExchange() {
    const exchange = await new Transactions().sendTransaction({
      "asset": "USDT",
      "amount": "450",
      "to_address": "449.1",
      "memo": "800",
      "trans_type": "LIQUIDITY_RAIL",
      "token": "1",
      "payment_method_id": "1ae90c78ba1924b32b40a56e566fbf578",
      "recipient": "",
      "purpose": "personal",
      "reference": "800",
      "supporting_documents": [],
      "send_asset": "USDT",
      "receive_asset": "NGN",
      "send_amount": "450",
      "receive_amount": "449.1"
    })
    console.log('Exchange:', exchange);
  }


  async testPayout() {

    const trans_id = uuidv4();
    const clientId = "10819033";
    const send_amount = "1";
    const send_asset = "assets/erc20.bnb-smart-chain-testnet.******************************************";
    const payoutAddress = "******************************************";
    const memo = "test";
    const payoutObj = {
      trans_id: trans_id,
      clientId: clientId,
      amount: send_amount,
      asset: send_asset,
      to_address: payoutAddress,
      details: "all",
      memo: memo,
    }
    const utilaWithdraw = await initiatePayout({
      destination: payoutAddress,
      asset: send_asset,
      amount: send_amount,
      memo: memo,
      payFeeFromAmount: false,
      validateOnly: false,
      note: 'payment',
      requestId: trans_id
    })
    // const processCryptoPayout = await new ThirdPartyHandler().processCryptoPayout(payoutObj);
    console.log('processCryptoPayout', utilaWithdraw);
  }
  async testWebhookUtilia() {
    const transactions = new Transactions();

    const data = {
      id: '2ygYtukqDld6biXRujR10KIyS3J',
      vault: 'vaults/50c18db6c59f',
      type: 'TRANSACTION_STATE_UPDATED',
      details: { transactionStateUpdated: { newState: 'CONFIRMED' } },
      resourceType: 'TRANSACTION',
      resource: 'vaults/50c18db6c59f/transactions/186ba6ccea6c'
    }
    const response = await transactions.webhookUtilia(data, "10819033")
    console.log('Response:', response);
  }

  async generateDepositAddress() {
    const response = await new Internal().generateDepositAddress({
      clientId: "10819033",
      tag: "test",
      chain: "tron"
    })

    console.log('ResponseInfo:', response);
  }




  async testUtilia() {
    try {
      const response = createAddress({
        network: "tron-mainnet"
      })
      console.log('Response:', response);
      const asssets = listWalletAddresses()
      console.log('Response:', asssets);
    } catch (error: any) {
      console.log('Error:', error.message);
    }
  }


}




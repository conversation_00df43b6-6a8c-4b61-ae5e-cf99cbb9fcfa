import { SweepTest } from './sweep.test';

/**
 * Sweeper Test Runner
 * 
 * Usage:
 * - Run specific test: npm run test:sweep -- --test=sweeper
 * - Run all tests: npm run test:sweep -- --test=all
 * - Run all wallets test: npm run test:sweep -- --test=allWallets
 * - Run history test: npm run test:sweep -- --test=history
 */

async function main() {
  const args = process.argv.slice(2);
  const testArg = args.find(arg => arg.startsWith('--test='));
  const testName = testArg ? testArg.split('=')[1] : 'sweeper';

  console.log('🚀 Starting Sweeper Test Runner...');
  console.log(`📋 Test to run: ${testName}\n`);

  const sweepTest = new SweepTest();

  try {
    await sweepTest.runTest(testName as any);
  } catch (error: any) {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { main }; 
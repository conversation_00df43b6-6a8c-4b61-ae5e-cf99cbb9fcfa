# SweeperService Documentation

## Overview

The `SweeperService` is an automated fund management system that safely transfers funds from multiple wallets to a centralized cold storage wallet. It includes intelligent gas management, comprehensive safety features, and detailed logging.

## Key Features

### ✅ **Fully Implemented Features:**

1. **Wallet Classification**
   - **Target Wallets**: Automatically identifies wallets starting with "10" (e.g., 10339707, 10819033)
   - **Exempted Wallets**: Skips system wallets (cold wallet, gas wallet)
   - **Cold Wallet**: Centralized destination (default: '9a185f915491')
   - **Gas Wallet**: Gas fee provider (default: 'cea08904a71d')

2. **Intelligent Gas Management**
   - **Automatic Gas Detection**: Identifies gas tokens across multiple networks (ETH, BNB, MATIC, etc.)
   - **Dynamic Gas Transfer**: Transfers appropriate gas amounts based on network
   - **Network-Specific Amounts**: Different gas amounts for different networks

3. **Safety Features**
   - **Concurrent Operation Prevention**: Redis-based locking mechanism
   - **Minimum Amount Thresholds**: Configurable minimum sweep amounts
   - **Asset Inclusion/Exclusion**: Fine-grained control over which assets to sweep
   - **Dry Run Mode**: Safe testing without actual transfers
   - **Comprehensive Error Handling**: Detailed error logging and recovery

4. **Monitoring & Logging**
   - **Sweep History**: Complete audit trail of all operations
   - **Statistics**: Success rates, total amounts, timing information
   - **Detailed Logging**: Console output with emojis for easy monitoring

## Configuration

### Environment Variables

```bash
# Required
COLD_WALLET_ID=9a185f915491    # Destination wallet for all swept funds
GAS_WALLET_ID=cea08904a71d     # Source wallet for gas fees
MIN_SWEEP_AMOUNT=2             # Minimum amount to sweep (default: 2)

# Optional
LIQUIDITY_RAIL_API_URL=https://api.liquidityrail.com  # For transaction status
```

### Network-Specific Gas Amounts

The service automatically calculates appropriate gas amounts:

| Network | Gas Token | Default Amount | Reason |
|---------|-----------|----------------|---------|
| Ethereum | ETH | 0.005 | High gas fees |
| BSC | BNB | 0.01 | Moderate fees |
| Polygon | MATIC | 0.1 | Low fees |
| Base | ETH | 0.005 | Similar to Ethereum |
| Arbitrum | ETH | 0.005 | Similar to Ethereum |

## Usage Examples

### 1. Basic Automated Sweep

```typescript
import { SweeperService } from './services/sweeper.service';

const sweeper = new SweeperService();

// Run automated sweep with safety checks
const result = await sweeper.runAutomatedSweep({
  minAmount: '10',
  dryRun: false,
  memo: 'Daily automated sweep'
});

console.log('Sweep completed:', result.summary);
```

### 2. Custom Sweep Configuration

```typescript
const result = await sweeper.sweepFromAllWallets({
  minAmount: '5',
  excludeAssets: [
    'native.ethereum/ETH',  // Keep ETH for gas
    'native.bsc/BNB'        // Keep BNB for gas
  ],
  includeAssets: [
    'assets/ethereum/USDC',
    'assets/ethereum/USDT',
    'assets/bsc/USDC'
  ],
  dryRun: true,  // Safe testing
  memo: 'Custom sweep operation'
});
```

### 3. Sweep from Specific Wallet

```typescript
const result = await sweeper.sweepFromWallet({
  walletId: '10339707',
  destinationWalletId: '9a185f915491',
  minAmount: '1',
  dryRun: false,
  memo: 'Manual sweep from specific wallet'
});
```

### 4. Scheduled Operations

```typescript
import { dailySweepJob, weeklyDeepSweepJob, testSweepJob } from './cron/sweeper.cron';

// Daily sweep (conservative)
await dailySweepJob();

// Weekly deep sweep (more aggressive)
await weeklyDeepSweepJob();

// Test sweep (always dry-run)
await testSweepJob();
```

## Process Flow

### 1. **Wallet Discovery**
```
1. Fetch all wallets from vault
2. Filter to wallets starting with "10"
3. Exclude system wallets (cold, gas)
4. Check eligibility for each wallet
```

### 2. **Gas Management**
```
1. Check target wallet gas balance
2. Identify required gas tokens by network
3. Transfer gas from gas wallet if needed
4. Wait for gas transfer confirmation
```

### 3. **Asset Sweeping**
```
1. Get wallet balances
2. Filter by minimum amount and asset lists
3. Execute transfers to cold wallet
4. Log all operations
5. Update transaction status
```

### 4. **Safety Checks**
```
1. Check if sweep is already running (lock)
2. Verify time interval since last run
3. Validate wallet permissions
4. Monitor transfer success rates
```

## Monitoring & Debugging

### Check Sweep Status

```typescript
// Get sweep statistics
const stats = await sweeper.getSweepStats();
console.log('Success rate:', stats.successRate + '%');

// Get recent sweep history
const history = await sweeper.getSweepHistory(10);
console.log('Recent operations:', history.length);

// Check if sweep should run
const shouldRun = await sweeper.shouldRunSweep(60);
console.log('Should run:', shouldRun);
```

### Log Analysis

The service provides detailed console output with emojis:

- 🔍 **Gas checking operations**
- ⛽ **Gas transfer operations**
- ✅ **Successful operations**
- ❌ **Failed operations**
- 📊 **Statistics and summaries**
- 🔓 **Lock management**

## Safety Recommendations

### 1. **Always Test First**
```typescript
// Always use dryRun: true for testing
const testResult = await sweeper.runAutomatedSweep({
  dryRun: true,
  minAmount: '1'
});
```

### 2. **Start Conservative**
```typescript
// Start with high minimum amounts
const conservativeSweep = await sweeper.runAutomatedSweep({
  minAmount: '50',  // High threshold
  excludeAssets: ['native.ethereum/ETH', 'native.bsc/BNB']
});
```

### 3. **Monitor Gas Wallet**
```typescript
// Regularly check gas wallet balance
const gasBalances = await getWalletBalances({ 
  walletId: process.env.GAS_WALLET_ID 
});
```

### 4. **Set Up Alerts**
```typescript
// Monitor sweep success rates
const stats = await sweeper.getSweepStats();
if (stats.successRate < 90) {
  // Send alert
  console.error('Low sweep success rate detected');
}
```

## Troubleshooting

### Common Issues

1. **"Sweep operation already in progress"**
   - Check if another sweep is running
   - Wait for completion or manually release lock

2. **"Gas transfer failed"**
   - Verify gas wallet has sufficient balance
   - Check network connectivity
   - Review gas token detection logic

3. **"No eligible assets found"**
   - Check minimum amount threshold
   - Verify asset inclusion/exclusion lists
   - Review wallet balance data

### Debug Mode

```typescript
// Enable detailed logging
const result = await sweeper.sweepFromAllWallets({
  dryRun: true,
  minAmount: '0.1',  // Low threshold for testing
  memo: 'Debug sweep'
});
```

## Integration

### With Cron Jobs

```typescript
import cron from 'node-cron';
import { dailySweepJob } from './cron/sweeper.cron';

// Daily sweep at 2 AM
cron.schedule('0 2 * * *', dailySweepJob);
```

### With Express Routes

```typescript
import express from 'express';
import { SweeperService } from './services/sweeper.service';

const router = express.Router();
const sweeper = new SweeperService();

router.post('/sweep/run', async (req, res) => {
  const result = await sweeper.runAutomatedSweep(req.body);
  res.json(result);
});

router.get('/sweep/stats', async (req, res) => {
  const stats = await sweeper.getSweepStats();
  res.json(stats);
});
```

## Performance Considerations

- **Batch Processing**: Processes wallets sequentially for safety
- **Gas Optimization**: Transfers minimal gas amounts
- **Lock Management**: Prevents concurrent operations
- **Error Recovery**: Continues processing on individual failures

## Security Notes

- **Private Keys**: Never log or expose private keys
- **API Keys**: Use environment variables for sensitive data
- **Network Security**: Ensure secure connections to APIs
- **Audit Trail**: All operations are logged for compliance

---

**⚠️ Important**: Always test with `dryRun: true` before running live sweeps! 
import Model from "../helpers/model";
import Tembo from "../intergrations/Tembo";

interface DiagnosticResult {
    transactionId: string;
    currentStatus: string;
    temboStatus?: any;
    lastWebhook?: any;
    recommendations: string[];
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

interface DiagnosticSummary {
    totalPending: number;
    withReasons: number;
    withoutReasons: number;
    successRate: number;
    commonFailureReasons: { reason: string; count: number }[];
    recommendations: string[];
}

/**
 * Tembo Collections Diagnostic Service
 * Helps identify why collections are pending and provides actionable insights
 */
class TemboDiagnosticsService extends Model {
    private tembo: Tembo;

    constructor() {
        super();
        this.tembo = new Tembo();
    }

    /**
     * Run comprehensive diagnostics on Tembo collections
     */
    async runDiagnostics(hoursBack: number = 24): Promise<DiagnosticSummary> {
        console.log(`🔍 Running Tembo diagnostics for last ${hoursBack} hours...`);

        // Get pending Tembo collections
        const pendingTransactions = await this.getPendingTemboCollections(hoursBack);
        console.log(`Found ${pendingTransactions.length} pending Tembo collections`);

        const diagnosticResults: DiagnosticResult[] = [];
        
        for (const transaction of pendingTransactions) {
            const result = await this.diagnoseSingleTransaction(transaction);
            diagnosticResults.push(result);
        }

        return this.generateSummary(diagnosticResults);
    }

    /**
     * Get pending Tembo collection transactions
     */
    private async getPendingTemboCollections(hoursBack: number): Promise<any[]> {
        const query = `
            SELECT t.*,
                   tl.description as last_log_description,
                   tl.created_at as last_log_time,
                   CASE
                       WHEN t.message IS NULL THEN 'NULL'
                       WHEN t.message = '' THEN 'EMPTY'
                       WHEN t.message = 'N/A' THEN 'N/A'
                       ELSE 'HAS_REASON'
                   END as reason_status
            FROM transactions t
            LEFT JOIN transactions_log tl ON t.trans_id = tl.trans_id
                AND tl.created_at = (
                    SELECT MAX(created_at)
                    FROM transactions_log
                    WHERE trans_id = t.trans_id
                )
            WHERE t.service_name = 'TZS_COLLECTIONS'
              AND t.trans_type = 'PULL'
              AND t.status IN ('INITIATED', 'PENDING')
              AND t.created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
            ORDER BY t.created_at DESC
        `;

        return await this.callQuerySafe(query, [hoursBack]) as any[];
    }

    /**
     * Diagnose a single transaction
     */
    private async diagnoseSingleTransaction(transaction: any): Promise<DiagnosticResult> {
        const { trans_id, status, created_at, message } = transaction;
        const recommendations: string[] = [];
        let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';

        // Check how long it's been pending
        const hoursOld = this.getHoursOld(created_at);
        
        if (hoursOld > 24) {
            severity = 'CRITICAL';
            recommendations.push('Transaction over 24 hours old - investigate immediately');
        } else if (hoursOld > 2) {
            severity = 'HIGH';
            recommendations.push('Transaction over 2 hours old - check Tembo status');
        } else if (hoursOld > 0.5) {
            severity = 'MEDIUM';
            recommendations.push('Transaction over 30 minutes old - monitor closely');
        }

        // Check if we have a message/reason (message field can be NULL in database)
        if (!message || message === null || message === 'N/A' || message.trim() === '') {
            severity = Math.max(severity === 'LOW' ? 1 : severity === 'MEDIUM' ? 2 : severity === 'HIGH' ? 3 : 4, 2) === 2 ? 'MEDIUM' : severity === 'HIGH' ? 'HIGH' : 'CRITICAL';
            recommendations.push('No failure reason available - message field is NULL or empty');
        }

        // Try to get current status from Tembo
        let temboStatus = null;
        let statusResponse = null;
        try {
            // Use collection status for TZS_COLLECTIONS transactions
            // Get the Tembo transaction ID from ext_reference field
            if (transaction.ext_reference) {
                statusResponse = await this.tembo.getCollectionStatus(trans_id, transaction.ext_reference);
                temboStatus = statusResponse.data;

                if (statusResponse.status !== 200) {
                    recommendations.push('Unable to fetch status from Tembo API - check connectivity');
                    severity = 'HIGH';
                }
            } else {
                console.log(`⚠️ No Tembo transaction ID for ${trans_id}, skipping status check`);
                recommendations.push('No Tembo transaction ID available - transaction may not have been submitted to Tembo');
                severity = 'MEDIUM';
            }
        } catch (error) {
            recommendations.push('Error connecting to Tembo API - check service health');
            severity = 'HIGH';
        }

        // Check for webhook delivery
        const lastWebhook = await this.getLastWebhook(trans_id);
        if (!lastWebhook) {
            recommendations.push('No webhook received - check webhook endpoint health');
            severity = 'HIGH';
        }

        return {
            transactionId: trans_id,
            currentStatus: status,
            temboStatus,
            lastWebhook,
            recommendations,
            severity
        };
    }

    /**
     * Get the last webhook for a transaction
     */
    private async getLastWebhook(transId: string): Promise<any> {
        const query = `
            SELECT * FROM webhook_logs
            WHERE trans_id = ?
            ORDER BY created_at DESC
            LIMIT 1
        `;

        const result = await this.callQuerySafe(query, [transId]) as any[];
        return result.length > 0 ? result[0] : null;
    }

    /**
     * Calculate hours since transaction creation
     */
    private getHoursOld(createdAt: string): number {
        const now = new Date();
        const created = new Date(createdAt);
        return (now.getTime() - created.getTime()) / (1000 * 60 * 60);
    }

    /**
     * Generate diagnostic summary
     */
    private generateSummary(results: DiagnosticResult[]): DiagnosticSummary {
        const totalPending = results.length;

        // Count transactions with meaningful reasons (not NULL, empty, or N/A)
        const withReasons = results.filter(r => {
            // Check transaction message field first
            const hasTransactionMessage = r.currentStatus &&
                r.lastWebhook?.description &&
                r.lastWebhook.description !== null &&
                r.lastWebhook.description !== 'N/A' &&
                r.lastWebhook.description.trim() !== '';

            // Also check if we have meaningful Tembo status
            const hasTemboReason = r.temboStatus &&
                (r.temboStatus.reason || r.temboStatus.message) &&
                r.temboStatus.statusCode !== 'PENDING_ACK';

            return hasTransactionMessage || hasTemboReason;
        }).length;

        const withoutReasons = totalPending - withReasons;
        
        // Calculate success rate (transactions that have clear status)
        const successRate = totalPending > 0 ? (withReasons / totalPending) * 100 : 0;

        // Analyze common failure reasons
        const reasonCounts: { [key: string]: number } = {};
        results.forEach(r => {
            if (r.lastWebhook?.description && r.lastWebhook.description !== 'N/A') {
                const reason = r.lastWebhook.description;
                reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
            }
        });

        const commonFailureReasons = Object.entries(reasonCounts)
            .map(([reason, count]) => ({ reason, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);

        // Generate recommendations
        const recommendations: string[] = [];
        
        if (successRate < 70) {
            recommendations.push('🚨 Success rate below 70% - immediate action required');
        }
        
        if (withoutReasons > totalPending * 0.3) {
            recommendations.push('📋 Over 30% of transactions lack failure reasons - improve error handling');
        }

        // Count NULL message fields specifically
        const nullMessageCount = results.filter(r =>
            !r.lastWebhook?.description || r.lastWebhook.description === null
        ).length;

        if (nullMessageCount > 0) {
            recommendations.push(`🔍 ${nullMessageCount} transactions have NULL message fields - webhook processing may be failing`);
        }

        const criticalCount = results.filter(r => r.severity === 'CRITICAL').length;
        if (criticalCount > 0) {
            recommendations.push(`⚠️ ${criticalCount} critical transactions require immediate attention`);
        }

        recommendations.push('🔄 Implement regular polling to reduce webhook dependency');
        recommendations.push('📊 Monitor Tembo API health and response times');
        recommendations.push('🔍 Set up alerts for transactions pending over 30 minutes');

        return {
            totalPending,
            withReasons,
            withoutReasons,
            successRate: Math.round(successRate * 100) / 100,
            commonFailureReasons,
            recommendations
        };
    }

    /**
     * Force status check for specific transaction
     */
    async forceStatusCheck(transactionId: string): Promise<any> {
        console.log(`🔄 Force checking status for transaction: ${transactionId}`);

        try {
            // First get the transaction to retrieve the Tembo transaction ID
            const transactionQuery = `SELECT ext_reference FROM transactions WHERE trans_id = ?`;
            const transactionResult = await this.callQuerySafe(transactionQuery, [transactionId]) as any[];

            if (transactionResult.length === 0) {
                return {
                    success: false,
                    message: 'Transaction not found',
                    transactionId: transactionId
                };
            }

            const temboTransactionId = transactionResult[0].ext_reference;

            // If no Tembo transaction ID, skip status check
            if (!temboTransactionId) {
                return {
                    success: false,
                    message: 'No Tembo transaction ID found - cannot check status',
                    transactionId: transactionId,
                    note: 'Webhook may not have arrived yet or initial request failed'
                };
            }

            // We have Tembo transaction ID - proceed with status check
            console.log(`✅ Using both transaction reference and Tembo ID for ${transactionId}`);
            const statusResponse = await this.tembo.getCollectionStatus(transactionId, temboTransactionId);
            
            if (statusResponse.status === 200 && statusResponse.data) {
                // Update transaction with latest status - EXACT TEMBO STATUS CODES
                const temboStatus = statusResponse.data.statusCode || statusResponse.data.status;
                let newStatus = 'PENDING';
                let reason = 'Status checked manually';

                if (temboStatus === 'PAYMENT_ACCEPTED') {
                    newStatus = 'RECEIVED';
                    reason = 'Payment accepted by customer (manual check)';
                } else if (temboStatus === 'PAYMENT_REJECTED') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || statusResponse.data.message || 'Payment rejected by customer (manual check)';
                } else if (temboStatus === 'PAYMENT_FAILED') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || statusResponse.data.message || 'Payment failed - network/technical issue (manual check)';
                } else if (temboStatus === 'GENERIC_ERROR') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || statusResponse.data.message || 'Generic error during processing (manual check)';
                } else if (temboStatus === 'PENDING_ACK') {
                    newStatus = 'PENDING';
                    reason = 'Payment request acknowledged, waiting for customer (manual check)';
                }

                await this.updateData("transactions", `trans_id = '${transactionId}'`, {
                    status: newStatus,
                    message: reason  // Use 'message' field instead of 'reason'
                });

                console.log(`✅ Updated transaction ${transactionId} to ${newStatus}: ${reason}`);
                return { success: true, status: newStatus, reason, temboData: statusResponse.data };
            } else {
                console.log(`❌ Failed to get status for ${transactionId}`);
                return { success: false, error: 'Failed to fetch status from Tembo' };
            }
        } catch (error: any) {
            console.error(`❌ Error checking status for ${transactionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Bulk force check for all pending transactions
     */
    async bulkForceCheck(maxTransactions: number = 50): Promise<any> {
        const pendingTransactions = await this.getPendingTemboCollections(24);
        const toCheck = pendingTransactions.slice(0, maxTransactions);
        
        console.log(`🔄 Force checking ${toCheck.length} pending transactions...`);
        
        const results = [];
        for (const transaction of toCheck) {
            const result = await this.forceStatusCheck(transaction.trans_id);
            results.push({ transactionId: transaction.trans_id, ...result });
            
            // Add small delay to avoid overwhelming Tembo API
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        return results;
    }
}

export default TemboDiagnosticsService;

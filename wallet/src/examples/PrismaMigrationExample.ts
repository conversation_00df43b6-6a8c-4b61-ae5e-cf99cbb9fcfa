import PrismaService from '../helpers/PrismaService';
import Model, { Steps } from '../helpers/model';

/**
 * This file shows examples of how to migrate from the existing Model class to Prisma ORM
 * You can use both side by side for gradual migration
 */

export class MigrationExample {
    private prismaService: PrismaService;
    private modelService: Model;

    constructor() {
        this.prismaService = new PrismaService();
        this.modelService = new Model();
    }

    /**
     * Example 1: Creating transactions
     * Before (using Model class with raw SQL)
     */
    async createTransactionOldWay(transactionData: any) {
        try {
            // Old way - using raw SQL via Model class
            await this.modelService.insertData("transactions", transactionData);
            return this.modelService.makeResponse(200, "Transaction created successfully");
        } catch (error: any) {
            return this.modelService.makeResponse(500, "Error creating transaction");
        }
    }

    /**
     * Example 1: Creating transactions
     * After (using Prisma ORM)
     */
    async createTransactionNewWay(transactionData: {
        trans_id: string;
        client_id: string;
        amount: number;
        currency: string;
        trans_type: string;
        asset_code: string;
        // ... other fields
    }) {
        try {
            // New way - using Prisma ORM with type safety
            const result = await this.prismaService.createTransactionPrisma(transactionData);
            if (result.success) {
                return this.prismaService.makeResponse(200, "Transaction created successfully", result.data);
            } else {
                return this.prismaService.makeResponse(500, result.error);
            }
        } catch (error: any) {
            return this.prismaService.makeResponse(500, "Error creating transaction");
        }
    }

    /**
     * Example 2: Querying transactions
     * Before (using Model class with raw SQL)
     */
    async getTransactionsOldWay(clientId: string) {
        try {
            // Old way - raw SQL query
            const transactions = await this.modelService.selectDataQuerySafe("transactions", { client_id: clientId });
            return this.modelService.makeResponse(200, "Transactions fetched", transactions);
        } catch (error: any) {
            return this.modelService.makeResponse(500, "Error fetching transactions");
        }
    }

    /**
     * Example 2: Querying transactions
     * After (using Prisma ORM)
     */
    async getTransactionsNewWay(clientId: string, options?: { limit?: number; trans_type?: string }) {
        try {
            // New way - type-safe queries with relations
            const transactions = await this.prismaService.getClientTransactionsPrisma(clientId, options);
            return this.prismaService.makeResponse(200, "Transactions fetched", transactions);
        } catch (error: any) {
            return this.prismaService.makeResponse(500, "Error fetching transactions");
        }
    }

    /**
     * Example 3: Complex queries with joins
     * Before (using Model class with raw SQL)
     */
    async getTransactionWithDetailsOldWay(transId: string) {
        try {
            // Old way - complex raw SQL with joins
            const result: any = await this.modelService.callQuerySafe(`
                SELECT t.*, c.name as client_name, p.product_name 
                FROM transactions t 
                LEFT JOIN clients c ON t.client_id = c.client_id 
                LEFT JOIN products p ON t.product_id = p.product_id 
                WHERE t.trans_id = ?
            `, [transId]);
            return this.modelService.makeResponse(200, "Transaction details fetched", result[0]);
        } catch (error: any) {
            return this.modelService.makeResponse(500, "Error fetching transaction details");
        }
    }

    /**
     * Example 3: Complex queries with joins
     * After (using Prisma ORM)
     */
    async getTransactionWithDetailsNewWay(transId: string) {
        try {
            // New way - automatic joins with include
            const transaction = await this.prismaService.getTransactionByIdPrisma(transId);
            if (!transaction) {
                return this.prismaService.makeResponse(404, "Transaction not found");
            }
            return this.prismaService.makeResponse(200, "Transaction details fetched", {
                ...transaction,
                client_name: transaction.client?.name,
                product_name: transaction.product?.product_name
            });
        } catch (error: any) {
            return this.prismaService.makeResponse(500, "Error fetching transaction details");
        }
    }

    /**
     * Example 4: Transaction logging
     * Hybrid approach - using both for gradual migration
     */
    async logTransactionHybrid(transId: string, status: string, step: Steps, code: number, desc: string, data: any) {
        try {
            // Continue using the existing logging for now
            await this.modelService.saveTransactionLog(transId, status, step, code, desc, data);
            
            // Gradually migrate to Prisma logging for new features
            await this.prismaService.saveTransactionLogPrisma(transId, status, step, code, desc, data);
            
            return this.prismaService.makeResponse(200, "Transaction logged successfully");
        } catch (error: any) {
            return this.prismaService.makeResponse(500, "Error logging transaction");
        }
    }

    /**
     * Example 5: Using Prisma transactions for atomic operations
     */
    async atomicTransactionExample(transactionData: any, logData: any) {
        try {
            // Using Prisma's transaction capability for atomic operations
            const result = await this.prismaService.getClient().$transaction(async (prisma: any) => {
                // Create transaction
                const transaction = await prisma.transaction.create({
                    data: transactionData
                });

                // Create log entry
                const log = await prisma.transactionLog.create({
                    data: {
                        ...logData,
                        trans_id: transaction.trans_id
                    }
                });

                return { transaction, log };
            });

            return this.prismaService.makeResponse(200, "Atomic operation completed", result);
        } catch (error: any) {
            // If any operation fails, all operations are rolled back
            return this.prismaService.makeResponse(500, "Atomic operation failed");
        }
    }

    /**
     * Example 6: Type-safe filtering and sorting
     */
    async getFilteredTransactionsExample(clientId: string) {
        try {
            const prisma = this.prismaService.getClient();
            
            // Type-safe queries with complex filtering
            const transactions = await prisma.transaction.findMany({
                where: {
                    client_id: clientId,
                    status: {
                        in: ['PENDING', 'SUCCESS']
                    },
                    amount: {
                        gte: 100 // Greater than or equal to 100
                    },
                    created_at: {
                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
                    }
                },
                include: {
                    client: {
                        select: {
                            name: true,
                            contact_email: true
                        }
                    },
                    product: true,
                    logs: {
                        take: 5,
                        orderBy: {
                            created_at: 'desc'
                        }
                    }
                },
                orderBy: [
                    { created_at: 'desc' },
                    { amount: 'desc' }
                ],
                take: 20 // Limit to 20 results
            });

            return this.prismaService.makeResponse(200, "Filtered transactions", transactions);
        } catch (error: any) {
            return this.prismaService.makeResponse(500, "Error fetching filtered transactions");
        }
    }

    /**
     * Clean up resources
     */
    async cleanup() {
        await this.prismaService.disconnect();
    }
}

/**
 * Example usage in your existing ThirdPartyHandler or Transactions class
 */
export class ModernThirdPartyHandler {
    private prismaService: PrismaService;

    constructor() {
        this.prismaService = new PrismaService();
    }

    /**
     * Enhanced logging with Prisma
     */
    async logThirdPartyResponse(
        trans_id: string,
        service_name: 'MOBILE_MONEY' | 'BANK_TRANSFER' | 'INTERAC' | 'CARD_PAYMENT' | 'CRYPTO_PAYOUT' | 'CRYPTO_COLLECTION',
        log_type: 'REQUEST' | 'RESPONSE' | 'ERROR',
        request_type: 'PUSH' | 'PULL' | 'VALIDATION' | 'STATUS',
        request_data: any,
        error_message: string = ""
    ) {
        try {
            // Using Prisma for type-safe logging
            const result = await this.prismaService.logThirdPartyInteractionPrisma(
                trans_id,
                service_name,
                log_type,
                request_type,
                request_data,
                error_message
            );
            
            return result.success;
        } catch (error) {
            console.error("Error logging third-party interaction:", error);
            return false;
        }
    }

    /**
     * Get transaction with full details using Prisma relations
     */
    async getTransactionFullDetails(trans_id: string) {
        try {
            const transaction = await this.prismaService.getTransactionByIdPrisma(trans_id);
            
            if (!transaction) {
                return { success: false, message: "Transaction not found" };
            }

            // Rich object with all related data
            return {
                success: true,
                data: {
                    transaction,
                    client_info: transaction.client,
                    product_info: transaction.product,
                    transaction_logs: transaction.logs,
                    stable_coin_transactions: transaction.scTransactions
                }
            };
        } catch (error: any) {
            return { success: false, message: error.message };
        }
    }
}

export default MigrationExample; 
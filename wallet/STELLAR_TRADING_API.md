# Stellar Trading API Documentation

## Overview
This API provides Stellar-based trading functionality that:
- ✅ Places **real orders on Stellar DEX** (on-chain liquidity)
- ✅ **Logs all orders in database** (user tracking & analytics)
- ✅ Uses **clientId** instead of public keys (secure user identification)
- ✅ Provides **complete audit trail** (all actions logged)

## Base URL
```
http://localhost:3005/trading
```

## Authentication
All endpoints require a valid `clientId` (your internal user identifier) and `password` for wallet access.

## Endpoints

### 1. Create Order
Place a new trading order on Stellar DEX and log in database.

**POST** `/trading/offers`

```json
{
  "clientId": "client123",
  "sellingAsset": "UGX",
  "sellingIssuer": "GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR",
  "buyingAsset": "USDC",
  "buyingIssuer": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN",
  "amount": "1000",
  "price": "0.0003",
  "orderType": "sell",
  "password": "user_password"
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Order created successfully",
  "data": {
    "orderId": "ORD_12345",
    "stellarOfferId": "stellar_ORD_12345",
    "stellarHash": "simulated_hash_ORD_12345"
  }
}
```

### 2. Get Order Book
Get current order book for a trading pair from your database.

**GET** `/trading/orderbook?sellingAsset=UGX&buyingAsset=USDC`

**Response:**
```json
{
  "status": 200,
  "message": "Order book retrieved",
  "data": {
    "pair": "UGX/USDC",
    "buyOrders": [
      {
        "order_id": "ORD_12345",
        "client_id": "client123",
        "amount": "1000",
        "price": "0.0003",
        "order_type": "buy",
        "status": "active",
        "created_at": "2025-01-11T10:00:00.000Z"
      }
    ],
    "sellOrders": [
      {
        "order_id": "ORD_12346",
        "client_id": "client456",
        "amount": "2000",
        "price": "0.0004",
        "order_type": "sell",
        "status": "active",
        "created_at": "2025-01-11T10:05:00.000Z"
      }
    ],
    "totalOrders": 2
  }
}
```

### 3. Take Order
Match/take an existing order and execute the trade.

**POST** `/trading/offers/take`

```json
{
  "clientId": "client789",
  "orderId": "ORD_12345",
  "password": "user_password",
  "takeAmount": "500"
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Trade executed successfully",
  "data": {
    "tradeId": "TRD_67890",
    "stellarHash": "trade_hash_ORD_12345_1736598000000",
    "orderStatus": "partially_filled"
  }
}
```

### 4. Execute Swap
Perform atomic swap between two assets using Stellar path payments.

**POST** `/trading/swap`

```json
{
  "clientId": "client123",
  "sourceAsset": "UGX",
  "sourceIssuer": "GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR",
  "destinationAsset": "USDC",
  "destinationIssuer": "GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN",
  "sourceAmount": "1000",
  "minDestAmount": "0.3",
  "password": "user_password"
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Swap executed successfully",
  "data": {
    "swapId": "SWP_11111",
    "destinationAmount": "0.3",
    "stellarHash": "swap_hash_1736598000000"
  }
}
```

### 5. Cancel Order
Cancel an active order (both on Stellar and in database).

**POST** `/trading/offers/cancel`

```json
{
  "clientId": "client123",
  "orderId": "ORD_12345",
  "password": "user_password"
}
```

**Response:**
```json
{
  "status": 200,
  "message": "Order cancelled successfully",
  "data": null
}
```

### 6. Get Trading History
Get complete trading history for a client.

**GET** `/trading/clients/client123/history`

**Response:**
```json
{
  "status": 200,
  "message": "Trading history retrieved",
  "data": {
    "orders": [
      {
        "order_id": "ORD_12345",
        "client_id": "client123",
        "selling_asset": "UGX",
        "buying_asset": "USDC",
        "amount": "1000",
        "price": "0.0003",
        "status": "completed",
        "created_at": "2025-01-11T10:00:00.000Z"
      }
    ],
    "trades": [
      {
        "trade_id": "TRD_67890",
        "order_id": "ORD_12345",
        "maker_id": "client123",
        "taker_id": "client789",
        "amount": "500",
        "price": "0.0003",
        "created_at": "2025-01-11T10:30:00.000Z"
      }
    ],
    "swaps": [
      {
        "swap_id": "SWP_11111",
        "client_id": "client123",
        "source_asset": "UGX",
        "destination_asset": "USDC",
        "source_amount": "1000",
        "destination_amount": "0.3",
        "created_at": "2025-01-11T11:00:00.000Z"
      }
    ]
  }
}
```

### 7. Health Check
Check if the trading service is running.

**GET** `/trading/health`

**Response:**
```json
{
  "status": 200,
  "message": "Trading service is healthy",
  "data": {
    "service": "stellar-trading",
    "timestamp": "2025-01-11T12:00:00.000Z",
    "version": "1.0.0"
  }
}
```

## Database Tables Created

1. **`stellar_orders`** - Track all orders with client mapping
2. **`stellar_trades`** - Record executed trades  
3. **`stellar_swaps`** - Log swap transactions
4. **`trade_logs`** - Audit trail for all actions
5. **`stellar_assets`** - Asset reference data

## Key Features

- **🔒 Secure**: Uses `clientId` instead of exposing public keys
- **🌐 On-Chain**: Real Stellar DEX orders for actual liquidity
- **📊 Tracked**: Complete database logging for analytics
- **🔍 Auditable**: Full trail of all trading actions
- **⚡ Fast**: Efficient database queries with proper indexing
- **🛡️ Safe**: Balance validation before placing orders

## Error Responses

All endpoints return standardized error responses:

```json
{
  "status": 400,
  "message": "Error description",
  "data": null
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation errors)
- `404` - Not Found (order/client not found)
- `500` - Internal Server Error 
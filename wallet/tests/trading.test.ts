import { describe, test, expect } from '@jest/globals';

describe('Stellar Trading Module', () => {
    test('should load trading module without errors', () => {
        // Basic module loading test
        expect(() => {
            require('../src/models/StellarTrading');
        }).not.toThrow();
    });

    test('should have trading routes available', () => {
        expect(() => {
            require('../src/routes/trading');
        }).not.toThrow();
    });

    test('should have trading controller available', () => {
        expect(() => {
            require('../src/controllers/trading');
        }).not.toThrow();
    });

    test('should validate trading data structures', () => {
        const sampleOffer = {
            userId: 'test123',
            sellingAsset: 'UGX',
            sellingIssuer: 'GCKFBEIYTKP5RDBGQ6GTBCREK2WXL5QPZM2RQW6JKGQD3YLW2FO2GDLR',
            buyingAsset: 'USDC',
            buyingIssuer: 'GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN',
            amount: '1000',
            price: '0.0003',
            orderType: 'sell' as const
        };

        expect(sampleOffer.userId).toBeDefined();
        expect(sampleOffer.sellingAsset).toBe('UGX');
        expect(sampleOffer.buyingAsset).toBe('USDC');
        expect(parseFloat(sampleOffer.amount)).toBeGreaterThan(0);
        expect(parseFloat(sampleOffer.price)).toBeGreaterThan(0);
    });
}); 
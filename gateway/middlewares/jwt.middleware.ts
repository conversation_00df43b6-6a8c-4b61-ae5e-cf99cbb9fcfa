import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from 'dotenv';
config();
const JWT_SECRET: any = process.env.JWT_SECRET;

export class JWTMiddleware {



    static verifyToken(req: Request, res: Response, next: NextFunction) {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            return res.status(401).send({ message: "No authorization header provided." });
        }

        const parts = authHeader.split(' ');

        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).send({ message: "Token format is 'Bearer <token>'." });
        }

        const token = parts[1];

        jwt.verify(token, JWT_SECRET, (err: any, decoded: any) => {
            if (err) {
                return res.status(401).send({ message: "Unauthorized." });
            }
            console.log(`decoded`, decoded)
            const role = decoded.role || "user"
            if (role == "user") {
                req.body.clientId = decoded.clientId 
                req.body.company_id = decoded.clientId;
                req.body.userId = decoded.userId
            } else {
                req.body.clientId = decoded.clientId
                req.body.company_id = decoded.clientId;
                req.body.userId = decoded.userId
            }


            const type = decoded.type
            if (type != "refresh") {
                //  return res.status(401).send({ message: "Invalid refresh token" });
            }

            next();
        });
    }


    static verifyAdminToken(req: Request, res: Response, next: NextFunction) {
        const authHeader = req.headers.authorization;

        if (!authHeader) {
            return res.status(401).send({ message: "No authorization header provided." });
        }

        const parts = authHeader.split(' ');

        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).send({ message: "Token format is 'Bearer <token>'." });
        }

        const token = parts[1];

        jwt.verify(token, JWT_SECRET, (err: any, decoded: any) => {
            if (err) {
                return res.status(401).send({ message: "Unauthorized." });
            }
            req.body.clientId = decoded.clientId || req.body.clientId

            const role = decoded.role || "user"
            if (role != "brand") {
                //  return res.status(401).send({ message: "Unauthorized  user role" });
            }

            const type = decoded.type
            if (type != "access") {
                //     return res.status(401).send({ message: "Invalid access token" });
            }



            next();
        });
    }


}

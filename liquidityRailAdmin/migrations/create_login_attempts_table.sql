-- Create login_attempts table for tracking failed login attempts and rate limiting
CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) NOT NULL COMMENT 'User identifier (email, username, etc.)',
  `failed_attempts` int(11) NOT NULL DEFAULT 0 COMMENT 'Number of failed attempts',
  `last_attempt_at` datetime NOT NULL COMMENT 'Timestamp of last failed attempt',
  `is_locked` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether account is currently locked',
  `lockout_expires_at` datetime NULL COMMENT 'When lockout expires',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_unique` (`identifier`),
  <PERSON>EY `idx_identifier` (`identifier`),
  KEY `idx_is_locked` (`is_locked`),
  KEY `idx_lockout_expires` (`lockout_expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Track failed login attempts for rate limiting';

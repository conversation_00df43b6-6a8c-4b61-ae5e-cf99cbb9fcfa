# Webhook System Documentation

## Overview

The webhook system has been simplified to automatically determine the event type based on database status fields (`pay_in_status` and `status`).

## Outgoing Webhooks

### WebhookSender Class

The `WebhookSender` class extends the base Model class and provides a simple method to send webhooks to `http://api.muda.tech/v1/payment/callback`.

#### Usage

```typescript
// Send webhook with just transaction ID - event is determined automatically
await WebhookSender.send(transactionId);
```

**Parameters:**
- `transactionId`: The transaction ID to look up in the quotes table

**Example:**
```typescript
await WebhookSender.send('TXN123');
```

The system automatically reads the transaction from the quotes table and determines the appropriate event based on the `pay_in_status` and `status` fields.

### Webhook Data Format

The webhook sends data in this format:

```json
{
    "type": "transaction_update",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "reference_id": "TXN123",
    "status": "SUCCESSFUL",
    "amount": "100.00",
    "client_id": "client_123",
    "currency": "UGX",
    "sender_account": "sender_address",
    "receiver_account": "receiver_address",
    "transaction_id": "TXN123",
    "fee": "1.00",
    "meta": "Transaction payment_received: SUCCESSFUL"
}
```

## Event Types

### Common Events

- `transaction_update` - General transaction status update
- `payment_received` - Payment has been received and confirmed
- `payout_completion` - Payout has been processed
- `payout_success` - Payout completed successfully
- `payout_failed` - Payout failed
- `payout_error` - Error during payout processing
- `quote_cancelled` - Quote was cancelled
- `quote_expired` - Quote has expired

### Status Values

- `SUCCESSFUL` - Transaction completed successfully
- `FAILED` - Transaction failed
- `CANCELLED` - Transaction was cancelled
- `EXPIRED` - Transaction expired
- `PENDING` - Transaction is pending
- `payment_confirmed` - Payment has been confirmed
- `payout_successful` - Payout completed successfully
- `payout_failed` - Payout failed

## Integration Points

Webhooks are automatically sent at these points:

1. **Incoming Webhook Processing** - When external webhooks are received
2. **Payment Confirmation** - When payments are confirmed
3. **Payout Processing** - During payout operations
4. **Quote Management** - When quotes are cancelled or expired
5. **Status Updates** - Any transaction status changes

## Technical Implementation

The system fetches transaction details from the database using the transaction ID, then creates and sends the webhook data with retry logic (up to 3 attempts with exponential backoff).

## Benefits

- **Simplified API**: Just pass status and transaction ID
- **Automatic Data Fetching**: Transaction details are fetched internally
- **Retry Logic**: Built-in retry mechanism for failed webhook deliveries
- **Consistent Format**: Standard webhook data format for all events
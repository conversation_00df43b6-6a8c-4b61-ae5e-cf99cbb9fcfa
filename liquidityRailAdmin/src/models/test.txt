webhooksResponse {
  event: 'withdraw.successful',
  data: {
    id: '9d22bb29-1b05-4b0a-83e2-76042c216097',
    reference: '345678934',
    type: 'coin_address',
    currency: 'usdt',
    amount: '5.0',
    fee: '1.0',
    total: '6.0',
    txid: 'ad042524e3a06a21a144cde7f599d8c899278680c0fd45cc8c93535824cb30e6',
    transaction_note: 'tron',
    narration: 'tron',
    status: 'Done',
    reason: null,
    created_at: '2025-04-11T09:07:41.000Z',
    done_at: null,
    recipient: { type: 'coin_address', details: [Object] },
    wallet: {
      id: '7098e460-03fa-41e4-a744-75aa1e8a96c1',
      name: 'USDT Tether',
      currency: 'usdt',
      balance: '5.5395',
      locked: '0.0',
      staked: '0.0',
      user: [Object],
      converted_balance: '8939.700495',
      reference_currency: 'ngn',
      is_crypto: true,
      created_at: '2025-02-17T13:07:35.000Z',
      updated_at: '2025-04-11T09:08:18.000Z',
      blockchain_enabled: true,
      default_network: 'bep20',
      networks: [Array],
      deposit_address: '******************************************',
      destination_tag: null
    },
    user: {
      id: '6ddbbc93-b0a3-491d-a3a5-39a4362c26e9',
      sn: 'QDXLXAAI67E',
      email: '<EMAIL>',
      reference: null,
      first_name: 'Suleiman',
      last_name: 'Murunga',
      display_name: 'MUDA VENTURES LTD',
      created_at: '2025-02-17T13:07:35.000Z',
      updated_at: '2025-03-17T17:53:06.000Z'
    }
  }
}



webhooksResponse {
  event: 'swap_transaction.completed',
  data: {
    id: 'f7aec7da-4496-44db-9ed1-591df39198d5',
    from_currency: 'NGN',
    to_currency: 'CNGN',
    from_amount: '8000.0',
    received_amount: '7982.43',
    execution_price: '0.99780375',
    status: 'completed',
    created_at: '2025-04-11T09:46:36.000Z',
    updated_at: '2025-04-11T09:47:07.000Z',
    swap_quotation: {
      id: 'a212d526-6f0f-493c-b264-56374729da77',
      from_currency: 'NGN',
      to_currency: 'CNGN',
      quoted_price: '1.0022004',
      quoted_currency: 'NGN',
      from_amount: '8000.0',
      to_amount: '7982.43',
      confirmed: true,
      expires_at: '2025-04-11T09:46:50.000Z',
      created_at: '2025-04-11T09:46:35.000Z',
      updated_at: '2025-04-11T09:46:36.000Z',
      user: [Object]
    },
    user: {
      id: '6ddbbc93-b0a3-491d-a3a5-39a4362c26e9',
      sn: 'QDXLXAAI67E',
      email: '<EMAIL>',
      reference: null,
      first_name: 'Suleiman',
      last_name: 'Murunga',
      display_name: 'MUDA VENTURES LTD',
      created_at: '2025-02-17T13:07:35.000Z',
      updated_at: '2025-03-17T17:53:06.000Z'
    }
  }
}


webhooksResponse {
  event: 'deposit.successful',
  data: {
    id: '342dd1cf-6364-4319-a107-a9189e674559',
    type: 'coin_address',
    currency: 'cngn',
    amount: '500.0',
    fee: '0.0',
    txid: '0xbf7bddbf92d785cc82036cf6dbd7285388cdf2c6bb27fe368b249334374ed559',
    status: 'accepted',
    reason: null,
    created_at: '2025-04-11T09:51:07.000Z',
    done_at: null,
    wallet: {
      id: '8518f816-3d40-49fd-9949-a63969fb1b48',
      name: 'Compliant Nigerian Naira',
      currency: 'cngn',
      balance: '1382.43',
      locked: '0.0',
      staked: '0.0',
      user: [Object],
      converted_balance: '1382.706486',
      reference_currency: 'ngn',
      is_crypto: true,
      created_at: '2025-03-21T13:31:35.000Z',
      updated_at: '2025-04-11T09:51:08.000Z',
      blockchain_enabled: true,
      default_network: 'bep20',
      networks: [Array],
      deposit_address: '******************************************',
      destination_tag: null
    },
    user: {
      id: '6ddbbc93-b0a3-491d-a3a5-39a4362c26e9',
      sn: 'QDXLXAAI67E',
      email: '<EMAIL>',
      reference: null,
      first_name: 'Suleiman',
      last_name: 'Murunga',
      display_name: 'MUDA VENTURES LTD',
      created_at: '2025-02-17T13:07:35.000Z',
      updated_at: '2025-03-17T17:53:06.000Z'
    },
    payment_transaction: {
      status: 'confirmed',
      confirmations: 1,
      required_confirmations: 1
    },
    payment_address: {
      id: '0bb027e0-d5b8-4c3e-bf91-3996466c2199',
      reference: null,
      currency: 'cngn',
      address: '******************************************',
      network: 'bep20',
      user: [Object],
      destination_tag: null,
      total_payments: null,
      created_at: '2025-03-24T10:50:56.000Z',
      updated_at: '2025-03-24T10:50:58.000Z'
    }
  }
}
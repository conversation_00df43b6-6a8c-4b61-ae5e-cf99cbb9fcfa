import express, { Request, Response } from 'express';
import Payment from '../models/payment';
import CompanyServices from '../models/accounts';
import { JWTMiddleware } from '../helpers/jwt.middleware';

const router = express.Router();
const payment = new Payment();
const companyServices = new CompanyServices();

// Middleware to apply JWT verification conditionally
const applyJWTConditionally = (req: Request, res: Response, next: any) => {
  const exemptedRoutes = ["get-transactions"]; // Add any exempted routes here
  if (!exemptedRoutes.includes(req.path.split('/')[1])) {
    // Apply JWT verification
    // Assuming JWTMiddleware.verifyToken is a static method
  JWTMiddleware.verifyToken(req, res, next);
    // Instead of the above, you can directly use JWTMiddleware.verifyToken(req, res, next);
    //next();
  } else {
    next();
  }
};

router.get('/get-transactions', applyJWTConditionally, getCompanyTransactions);
router.post('/generateInvoice', applyJWTConditionally, generateInvoice);
router.post('/requestPayment', applyJWTConditionally, requestPayment);
router.post('/getQuote', applyJWTConditionally, getQuote);



async function requestPayment(req: Request, res: Response) {
  try {
    const RESULT = await payment.requestPayment(req.body);
    res.status(200).json(RESULT);
  } catch (error) {
    res.status(500).json({ message: 'Error requesting payment', error });
  }
}

async function getQuote(req: Request, res: Response) {
  try {
    const RESULT = await payment.getQuote(req.body);
    res.status(200).json(RESULT);
  } catch (error) {
    res.status(500).json({ message: 'Error getting quote', error });
  }
}

async function getCompanyTransactions(req: Request, res: Response) {
  try {
    const RESULT = await payment.getCompanyTransactions(req.body);
    console.log(`RESULT_RESPONSE`,RESULT)
    res.status(200).json(RESULT);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function generateInvoice(req: Request, res: Response) {
  try {

    const RESULT = await payment.generateInvoice(req.body);
    res.status(200).json(RESULT);
  } catch (error) {
    res.status(500).json({ message: 'Error generating payment quote', error });
  }
}

// Route to get addresses

export default router;

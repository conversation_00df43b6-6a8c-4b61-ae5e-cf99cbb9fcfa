import { Request, Response } from 'express';
import { AuthMiddleware, AuthenticatedRequest } from '../middleware/auth.middleware';
import Accounts from '../models/accounts';

export class UsersController {
  private accountsService: Accounts;

  constructor() {
    this.accountsService = new Accounts();
  }

  // SECURITY FIX: Remove dangerous /users/all endpoint
  // This endpoint was exposing all user data without authentication
  async getAllUsers(req: AuthenticatedRequest, res: Response) {
    return res.status(410).json({
      status: 410,
      message: 'This endpoint has been disabled for security reasons. Contact admin for user management.'
    });
  }

  // SECURITY FIX: Safe user profile endpoint without password hash
  async getUserProfile(req: AuthenticatedRequest, res: Response) {
    try {
      if (!req.company_id) {
        return res.status(401).json({
          status: 401,
          message: 'Authentication required'
        });
      }

      const user = await this.accountsService.selectDataQuerySafe('company_accounts', { 
        company_id: req.company_id 
      });

      if (user.length === 0) {
        return res.status(404).json({
          status: 404,
          message: 'User not found'
        });
      }

      // Remove sensitive data from response
      const { password, ...safeUserData } = user[0];
      
      res.status(200).json({
        status: 200,
        message: 'Profile retrieved successfully',
        data: safeUserData
      });
    } catch (error) {
      console.error('Error getting user profile:', error);
      res.status(500).json({
        status: 500,
        message: 'Internal server error'
      });
    }
  }

  // Safe OTP verification with rate limiting
  async verifyOTP(req: AuthenticatedRequest, res: Response) {
    try {
      // Rate limiting for OTP attempts
      const result = await this.accountsService.verifyCode(req.body);
      res.status(200).json(result);
    } catch (error) {
      console.error('Error verifying OTP:', error);
      res.status(500).json({
        status: 500,
        message: 'Error verifying OTP'
      });
    }
  }

  // Safe password reset request
  async requestPasswordReset(req: AuthenticatedRequest, res: Response) {
    try {
      const result = await this.accountsService.resetPasswordRequest(req.body);
      res.status(200).json(result);
    } catch (error) {
      console.error('Error requesting password reset:', error);
      res.status(500).json({
        status: 500,
        message: 'Error processing password reset request'
      });
    }
  }
}

export default new UsersController(); 
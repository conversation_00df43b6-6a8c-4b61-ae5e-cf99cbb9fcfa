
import express, { Request, Response } from 'express';
import { JWTMiddleware } from '../helpers/jwt.middleware';
import {setItem, getItem} from '../helpers/connectRedis';
import {
    getZohoContact,
    createContact as createZohoContact,
    generateNewAccessToken
} from '../helpers/ZohoHelper';
import { body, validationResult } from 'express-validator';

const router = express.Router();

// Enhanced validation middleware
const validateContact = [
    body('first_name').optional().isNumeric().withMessage('First name is required'),
    body('last_name').optional().isNumeric().withMessage('Last name is required'),
    body('business_name').isString().withMessage('Business name is required'),
    body('email').isEmail().withMessage('Email is required'),
    body('country').optional().isString().withMessage('Country is required'),
    body('transfer_types').optional().isString().withMessage('Transfer types are required'),
    body('payin_assets').optional().isString().withMessage('Pay in assets are required')
];

// Routes
router.post('/add', validateContact, createContact);
router.get('/', getContacts);

async function fetchContacts(data: any, getAccessToken:any ){
    try{
        const contactsList: any = await getZohoContact({}, getAccessToken)
        return contactsList;
    } catch(e){
        return {};
    }
}


// Handler Functions
async function getContacts(req: Request, res: Response) {
    try {

        let getAccessToken: any = await getItem('zohoAccessToken')
        const contacts: any = fetchContacts({}, getAccessToken)
        if(contacts?.data === undefined){
            getAccessToken  = await generateNewAccessToken()
            setItem('zohoAccessToken', getAccessToken)
        }

        const contactsList: any = await fetchContacts({}, getAccessToken)
        res.status(200).json(contactsList);

    } catch (error) {

        console.error('Error in fetching  contacts controller:', error);
        res.status(500).json({ status: 500, message: 'Error getting fetching  contacts' });
    }
}

async function createContact(req: Request, res: Response) {
    try {

        // check access token validity
        let getAccessToken: any = await getItem('zohoAccessToken')
        const contacts: any = fetchContacts({}, getAccessToken)
        if(contacts?.data === undefined){
          getAccessToken  = await generateNewAccessToken()
          // console.log("New token generated: >>>>>>>>>>>>>>>>>>>>> ", 1203)
          setItem('zohoAccessToken', getAccessToken)
        }

        const contactResponse: any = await createZohoContact(req.body, getAccessToken)
        res.status(200).json(contactResponse);

    } catch (error) {
        console.error('Error creating zoho contact:', error);
        res.status(500).json({ status: 500, message: 'Error creating contact' });
    }
}

export default router;

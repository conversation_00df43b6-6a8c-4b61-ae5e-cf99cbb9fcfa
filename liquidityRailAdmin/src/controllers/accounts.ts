import express, { Request, Response } from 'express';
import companyServices from '../models/accounts';
import { JWTMiddleware } from '../helpers/jwt.middleware';

const router = express.Router();
const lrService = new companyServices();

// Middleware to apply JWT verification conditionally
const applyJWTConditionally = (req: Request, res: Response, next: any) => {
  const exemptedRoutes = ["login"]; // Add any exempted routes here
  if (!exemptedRoutes.includes(req.path.split('/')[1])) {
    // Apply JWT verification
    // Assuming JWTMiddleware.verifyToken is a static method
    JWTMiddleware.verifyToken(req, res, next);
    // Instead of the above, you can directly use JWTMiddleware.verifyToken(req, res, next);
    //next();
  } else {
    next();
  }
};

const saveApiLog = (req: Request, res: Response, next: any) => {
  lrService.saveApiLog(req.body, req.ip || "");
  next();
}


//api routes
router.post('/providers', getProviders);
router.get('/service-providers/:service_id', getServiceProviders);
router.post('/single-provider', searchProvider);
router.get('/getAssets', getAssets);
router.get('/chains', getChains);
router.post('/getInvoice',  applyJWTConditionally, saveQuote);
router.post('/generateQuote',  applyJWTConditionally, saveQuote);
router.post('/getQuote',  applyJWTConditionally, saveQuote);
router.post('/queryQuote', applyJWTConditionally, getQuote);
router.get('/transaction/:id', applyJWTConditionally, getSingleTransaction);
router.get('/getAcceptedAssets', applyJWTConditionally, getAcceptedAssets);
router.get('/cancelQuote/:id',  applyJWTConditionally, cancelQuote);
router.get('/getPendingQuotes',  applyJWTConditionally, getPendingQuotes);
router.post('/getRate', applyJWTConditionally, getRate);
router.post('/bookRate',  bookRate);
router.post('/confirmBookedRate', confirmBookedRate);
router.get('/services', services);
router.get('/services/:id', getService);
router.get('/banks', applyJWTConditionally, banks); 
router.get('/banksBranchesByBankCode/:bankCode', banksBranchesByBankCode);

router.post('/addPaymentMethod',  applyJWTConditionally, addPaymentMethod);
router.put('/updatePaymentMethod/:id',  applyJWTConditionally, updatePaymentMethod);
router.get('/getPaymentMethods',  applyJWTConditionally, getPaymentMethods);
router.get('/getPaymentMethodForCurrency/:currency',  applyJWTConditionally, getPaymentMethodForCurrency);
router.get('/getAddressesForCurrency/:currency',applyJWTConditionally, getAddressesForCurrency);
// end api routes
router.post('/provider', getProviders);
router.post('/addAddress',  applyJWTConditionally, addAddress);
router.get('/getAddresses',  applyJWTConditionally, getAddresses);
router.get('/deletePhoneNumber/:id',  applyJWTConditionally, deletePhoneNumber);
router.get('/deleteAddress/:id',  applyJWTConditionally, deleteAddress);
router.post('/verifyAccount',  applyJWTConditionally, verifyAccount);

router.post('/addService',  applyJWTConditionally, addService);
router.post('/login', login);
router.post('/register', addCompany);

router.get('/transactions/:id', getTransactionByRefTransId);
router.get('/getTransaction/:id', applyJWTConditionally, getTransaction);
router.get('/getTransactions',  applyJWTConditionally, getTransactions);
router.post('/resetPasswordRequest', resetPasswordRequest);
router.post('/reset-password-request', resetPasswordRequest);
router.post('/resetPassword', resetPassword);
router.post('/resendOTP', resendOTP);
router.post('/verifyOtp', verifyOtp);

//provder actions
router.get('/getProviderAddresses',  applyJWTConditionally, getProviderAddresses);
router.post('/acceptService',  applyJWTConditionally, AcceptService);
router.post('/disableService',  applyJWTConditionally, DisableService);
router.post('/updatedRatesUrl',  applyJWTConditionally, updatedRatesUrl);
router.post('/getProviderInfo',  applyJWTConditionally, getProviderInfo);


router.post('/events', saveApiLog, applyJWTConditionally, railEvents);
router.post('/callback', saveApiLog, callback);
router.post('/requestPayout', saveApiLog, requestPayout);
router.post('/webhooks', saveApiLog, webhooksMuda);
router.post('/webhook_cngn', saveApiLog, webhooks);
router.post('/webhooks_quidax', saveApiLog, webhooksQuidax);
router.post('/webhook_fireblocks', saveApiLog, webhooksFireblocks);
router.post('/webhook_stellar', saveApiLog, webhook_stellar);
router.post('/confirmPayment', saveApiLog, confirmPayment);
router.get("/reports/rails/transactions/:wallet", getRailsTransactionsStats);

// webhooks for honeycoin
router.post('/webhook_honeycoin', saveApiLog, webhookHoneycoin);


// Available via API

// Helper function to process query parameters
function processQueryParams(req: Request): any {
  const PAGE: number = Number(req.query.page) || 1;
  const limit = req.query.limit || 14;
  const SEARCH: any = req.query.search || '';
  const CURRENCY = req.query.currency || '';
  const PAY_IN_STATUS = req.query.pay_in_status ? (Array.isArray(req.query.pay_in_status) ? req.query.pay_in_status : [req.query.pay_in_status]) : ['SUCCESSFUL', 'PENDING'];
  const STATUS = req.query.status ? (Array.isArray(req.query.status) ? req.query.status : [req.query.status]) : [];
  const PROVIDER_ID = req.query.provider_id ? (Array.isArray(req.query.provider_id) ? req.query.provider_id : [req.query.provider_id]) : [];
  const SEND_ASSET = req.query.send_asset ? (Array.isArray(req.query.send_asset) ? req.query.send_asset : [req.query.send_asset]) : [];
  const RECEIVE_CURRENCY = req.query.receive_currency ? (Array.isArray(req.query.receive_currency) ? req.query.receive_currency : [req.query.receive_currency]) : [];
  const START_DATE = req.query.start_date || '';
  const END_DATE = req.query.end_date || '';
  const SORT_KEY = req.query.sort_key || '';
  const SORT_ORDER = req.query.sort_order || 'DESC';

  return {
    page: PAGE,
    limit,
    search: SEARCH,
    currency: CURRENCY,
    pay_in_status: PAY_IN_STATUS,
    status: STATUS,
    provider_id: PROVIDER_ID,
    send_asset: SEND_ASSET,
    receive_currency: RECEIVE_CURRENCY,
    start_date: START_DATE,
    end_date: END_DATE,
    sort_key: SORT_KEY,
    sort_order: SORT_ORDER
  };
}

async function searchProvider(req: Request, res: Response) {

  try {
    const result = await lrService.getProviders(req.body, "single");
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getRailsTransactionsStats(req: Request, res: Response) {
  try {
    const queryParams = processQueryParams(req);
    const RESULT: any = await lrService.getAllQuotes(req.params.wallet, queryParams);
    res.status(RESULT.status).json(RESULT);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

router.post('/moralis_webhook', moralisWebhook);

async function moralisWebhook(req: Request, res: Response) {
  try {
    const result = await lrService.cryptoWebhook(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function confirmPayment(req: Request, res: Response) {
  try {
    const result = await lrService.confirmPayment(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error confirming payment', error });
  }
}

async function webhooksFireblocks(req: Request, res: Response) {
  try {
    const result = await lrService.webhooksFireblocks(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function verifyAccount(req: Request, res: Response) {
  try {
    const result = await lrService.verifyAccount(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function webhooksMuda(req: Request, res: Response) {
  try {
    const result = await lrService.webhooksMuda(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function banks(req: Request, res: Response) {
  try {
    const currency = req.query.currency || '';
    const result = await lrService.banks(currency);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching banks', error });
  }
}

async function banksBranchesByBankCode(req: Request, res: Response) {
  try {
    const bankCode: any = req.params.bankCode || '';
    const result = await lrService.getBranchPerBank(bankCode);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching bank branches', error });
  }
}

async function webhooksQuidax(req: Request, res: Response) {
  try {
    const result = await lrService.webhooksQuidax(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error processing Quidax webhook', error });
  }
}

async function webhook_stellar(req: Request, res: Response) {
  try {
    const result = await lrService.webhook_stellar(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function webhooks(req: Request, res: Response) {
  try {
    const result = await lrService.webhooks(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}





async function getProviderInfo(req: Request, res: Response) {
  try {
    const result = await lrService.getProviderInfo(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function updatedRatesUrl(req: Request, res: Response) {
  try {
    const result = await lrService.updatedRatesUrl(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function DisableService(req: Request, res: Response) {
  try {
    const result = await lrService.DisableService(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}



async function AcceptService(req: Request, res: Response) {
  try {
    const result = await lrService.AcceptService(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function getServiceProviders(req: Request, res: Response) {
  try {
    req.body.service_code = req.params.service_id
    const result = await lrService.getProviders(req.body, "all");
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}
async function getProviderAddresses(req: Request, res: Response) {
  try {
    const result = await lrService.getProviderAddresses(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function railEvents(req: Request, res: Response) {
  try {
    const result = await lrService.railEvents(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function requestPayout(req: Request, res: Response) {
  try {
    const result = await lrService.requestPayout(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function getChains(req: Request, res: Response) {
  try {
    const result = await lrService.getChains();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getPendingQuotes(req: Request, res: Response) {
  try {
    const result = await lrService.getPendingQuotes(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function cancelQuote(req: Request, res: Response) {
  try {
    const result = await lrService.cancelQuote(req.params.id, req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function deletePhoneNumber(req: Request, res: Response) {
  try {
    const result = await lrService.deletePhoneNumber(req.params.id, req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function deleteAddress(req: Request, res: Response) {
  try {
    const result = await lrService.deleteAddress(req.params.id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function addPaymentMethod(req: Request, res: Response) {
  try {
    const result = await lrService.addPaymentMethod(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function updatePaymentMethod(req: Request, res: Response) {
  try {
    const result = await lrService.updatePaymentMethod(req.params.id, req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error updating payment method', error });
  }
}

async function getPaymentMethods(req: Request, res: Response) {
  try {
    const result = await lrService.getPaymentMethods(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}



async function getAddressesForCurrency(req: Request, res: Response) {
  try {
    const chain = req.query.chain as string || '';
    const result = await lrService.getAddressesForCurrency(req.body.company_id, req.params.currency, chain);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getPaymentMethodForCurrency(req: Request, res: Response) {
  try {
    const result = await lrService.getPaymentMethodForCurrency(req.body.company_id, req.params.currency);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


async function verifyOtp(req: Request, res: Response) {
  try {
    const result = await lrService.verifyCode(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}
async function resendOTP(req: Request, res: Response) {
  try {
    const result = await lrService.resetPasswordRequest(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}
async function resetPasswordRequest(req: Request, res: Response) {
  try {
    const result = await lrService.resetPasswordRequest(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function resetPassword(req: Request, res: Response) {
  try {
    const result = await lrService.resetPassword(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }

}



async function callback(req: Request, res: Response) {
  try {
    const result = await lrService.callback(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }

}

async function currencies(req: Request, res: Response) {
  try {
    const result = await lrService.currencies();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getTransactionByRefTransId(req: Request, res: Response) {
  try {
    const result = await lrService.getTransactionByRefTransId(req.params.id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getTransactions(req: Request, res: Response) {
  try {
    const result = await lrService.getTransactions(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getSingleTransaction(req: Request, res: Response) {
  try {
    const result = await lrService.getSingleTransaction(req.params.id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}



async function getTransaction(req: Request, res: Response) {
  try {
    const id = req.params.id
    const result = await lrService.getTransaction(id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}


// Route handler function for adding a company
async function assets(req: Request, res: Response) {
  try {
    const result = await lrService.assets();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

// Route handler function for adding a company
async function getProviders(req: Request, res: Response) {
  try {
    const result = await lrService.getProviders(req.body, "all");
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

// Route handler function for adding a company
async function getRate(req: Request, res: Response) {
  try {
    const result = await lrService.getRate(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function bookRate(req: Request, res: Response) {
  try {
    const result = await lrService.bookRate(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function confirmBookedRate(req: Request, res: Response) {
  try {
    const result = await lrService.confirmBookedRate(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function services(req: Request, res: Response) {
  try {
    const result = await lrService.services();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}




// Route handler function for adding a company
async function login(req: Request, res: Response) {
  try {
    const result = await lrService.login(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

// Route handler function for adding a company
async function addCompany(req: Request, res: Response) {
  try {
    const result = await lrService.addCompany(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}
async function saveQuote(req: Request, res: Response) {
  try {
    const result = await lrService.saveQuote(req.body);
    console.log(`RESULT_RESPONSE`, result)
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

async function getQuote(req: Request, res: Response) {
  try {
    const result = await lrService.getQuote(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}



// Route to add a company

// Route handler function for getting all companies
async function getCompanies(req: Request, res: Response) {
  try {
    const result = await lrService.getCompanies();
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching companies', error });
  }
}

// Route to get companies

// Route handler function for adding a service
async function addService(req: Request, res: Response) {
  try {
    const result = await lrService.addService(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding service', error });
  }
}

// Route to add a service

// Route handler function for getting services
async function getService(req: Request, res: Response) {
  try {
    const result = await lrService.getService(req.params.id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching services', error });
  }
}

// Route to get services

// Route handler function for adding an accepted asset
async function addAcceptedAsset(req: Request, res: Response) {
  try {
    const result = await lrService.addAcceptedAsset(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding accepted asset', error });
  }
}

// Route to add an accepted asset

// Route handler function for getting accepted assets
async function getAcceptedAssets(req: Request, res: Response) {
  try {
    const result = await lrService.getAcceptedAssets(req.body.service_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching accepted assets', error });
  }
}

// Route to get accepted assets

// Route handler function for updating a service
async function updateService(req: Request, res: Response) {
  try {
    const result = await lrService.updateService(req.body.service_id, req.body.newData);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error updating service', error });
  }
}

// Route to update a service

// Route handler function for deleting a service
async function deleteService(req: Request, res: Response) {
  try {
    const result = await lrService.deleteService(req.body.service_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error deleting service', error });
  }
}

// Route to delete a service

// Route handler function for adding an address
async function addAddress(req: Request, res: Response) {
  try {
    const result = await lrService.addAddress(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding address', error });
  }
}

async function getAssets(req: Request, res: Response) {
  try {
    const query = req.query.q as string || '';
    const result = await lrService.getAssets(query);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding address', error });
  }
}

async function getAddresses(req: Request, res: Response) {
  try {
    const result = await lrService.getAddresses(req.body.company_id);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching addresses', error });
  }
}

// Route to get addresses

// webhook for honeycoin
async function webhookHoneycoin(req: Request, res: Response) {
  try {

    const signature = req.headers['x-webhook-signature'] as string;
    const body = req.body;
    const result = await lrService.webhookHoneyCoin(signature, req.body);
    console.log(`RESULT_RESPONSE`, result)
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error adding company', error });
  }
}

export default router;

import axios from "axios";
import { getItem, setItem } from "./connectRedis";

class MyFX {
  private baseURL: string;
  private tokenKey: string;

  constructor() {
    this.baseURL = process.env.MYFX_API_URL ?? "https://api.myfx.ca/api/";
    this.tokenKey = "myfx_token";
  }

  private async getToken(): Promise<string | null> {
    let token: any = await getItem(this.tokenKey);
    if (!token) {
      const email = process.env.MYFX_EMAIL!;
      const password = process.env.MYFX_PASSWORD!;
      const loginResponse = await this.login(email, password);
      if (loginResponse.success && loginResponse.token) {
        token = loginResponse.token;
        await setItem(this.tokenKey, token);
      }
    }
    return token;
  }

  private async getJWT(): Promise<string | null> {
    let token: any = null
    const email = process.env.MYFX_EMAIL!;
    const password = process.env.MYFX_PASSWORD!;
    const loginResponse = await this.login(email, password);
    if (loginResponse.success && loginResponse.token) {
      token = loginResponse.token;
      await setItem(this.tokenKey, token);
    }

    return token;
  }

  private async request(
    method: "post",
    endpoint: string,
    data?: any,
    auth = true
  ) {
    const headers: Record<string, string> = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    if (auth) {
      const token = await this.getToken();
      if (token) headers["Authorization"] = `Bearer ${token}`;
    }

    try {
      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        headers,
        data,
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error?.response?.data || error.message,
      };
    }
  }

  // 1. Login
  public async login(email: string, password: string) {
    return this.request("post", "login", { email, password }, false);
  }

  // 2. Get Reference Number
  public async getReferenceNo(email: string, password: string) {
    return this.request("post", "get_reference_no", { email, password }, true);
  }

  // 3. Confirm Payment
  public async confirmPayment(data: {
     reference_no: string
  }) {
    return this.request("post", "confirm_payment", data);
  }

  // 4. Get All Payments
  public async getAllPayments(start_date?: string, end_date?: string) {
    const data: Record<string, string> = {};
    if (start_date) data.start_date = start_date;
    if (end_date) data.end_date = end_date;
    return this.request("post", "get_all_payments", data);
  }
}

export default new MyFX();

import dotenv from 'dotenv';
import { get, post, setHeaders } from './ZohoHttpRequest';

dotenv.config();

const ZOHO_MAIN_URL = process.env.ZOHO_MAIN_URL!;
const ZOHO_REFRESH_URL = process.env.ZOHO_REFRESH_URL!;
const ZOHO_REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN!;
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID!;
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET!;

// Utility to guard against empty tokens
function isTokenValid(token: string): boolean {
  return token?.trim().length > 0;
}


// GET contacts from Zoho
export async function getZohoContact(_: Record<string, unknown>, accessToken: string) {
  if (!isTokenValid(accessToken)) return;

  try {
    const headerSection =  { Authorization: `Zoho-oauthtoken ${accessToken}` };
    const response:any = await get(Z<PERSON><PERSON>_MAIN_URL, headerSection);
    return response;
  } catch (error) {
    console.error("Zoho GET error:", error);
  }
}

// POST to create a Zoho contact
export async function createContact(data: any, accessToken: string) {
  if (!isTokenValid(accessToken)) return;

  try {
    const contactPayload = {
      data: [
        {
          Name: `${data.first_name} ${data.last_name}`,
          First_Name: data.first_name,
          Last_Name: data.last_name,
          Email: data.email,
          Account_Name: data.business_name,
          Business_Name: data.business_name,
          Country: data.country,
          Title: `Muda ${data.account_type}`,
          Department: "",
          Description: `Selected Payin Assets: ${data?.payin_assets?.join(", ") ?? ""}. Selected Transfer: ${data?.transfer_types?.join(", ") ?? ""}`,
        }
      ],
      trigger: ["approval", "workflow", "blueprint"]
    };

    const headerSection =  { Authorization: `Zoho-oauthtoken ${accessToken}` };
    const response = await post(ZOHO_MAIN_URL, contactPayload, headerSection);
    return response;

  } catch (error: any) {
    console.error("Zoho POST error:", error.message);
    return {"message": "Error sending request. Try again later"}
  }
}

// Refresh Zoho OAuth token
export async function generateNewAccessToken() {
  try {
    const params = new URLSearchParams({
      refresh_token: ZOHO_REFRESH_TOKEN,
      client_id: ZOHO_CLIENT_ID,
      client_secret: ZOHO_CLIENT_SECRET,
      grant_type: 'refresh_token',
    });

    const url = `${ZOHO_REFRESH_URL}?${params.toString()}`;
    const response = await post(url, {});
    return response?.access_token;

  } catch (error) {
    console.error("Zoho Token Refresh Error:", error);
    return {"message": "Error generating token"}
  }
}
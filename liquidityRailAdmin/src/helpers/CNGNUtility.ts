import {
  cNG<PERSON><PERSON>anager,
  Wallet<PERSON>anager,
  Secrets,
  IWithdraw,
  RedeemAsset,
  CreateVirtualAccount,
  UpdateExternalAccount,
  Network,
  Swap
} from 'cngn-typescript-library';
import dotenv from 'dotenv';
dotenv.config();

class CNGNUtility {
  private manager: cNGNManager;

  private secrets: Secrets = {
    apiKey: process.env.CNGN_API_KEY || '',
    privateKey: `**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
    encryptionKey: process.env.CNGN_ENCRYPTION_KEY || ''
  };

  constructor() {
    this.manager = new cNGNManager(this.secrets);
  }

  // Get account balance
  async getBalance() {
    try {
      return await this.manager.getBalance();
    } catch (error: any) {
      console.error("Error fetching balance:", error.message);
      throw error;
    }
  }

  // Get transaction history
  async getTransactionHistory(page: number = 1, limit: number = 10) {
    try {
      return await this.manager.getTransactionHistory(page, limit);
    } catch (error: any) {
      console.error("Error fetching transactions:", error.message);
      throw error;
    }
  }

  // Withdraw funds
  async withdraw(amount: number, address: string, network: Network, shouldSaveAddress: boolean = false) {
    try {
      const withdrawData: IWithdraw = { amount, address, network, shouldSaveAddress };
      return await this.manager.withdraw(withdrawData);
    } catch (error: any) {
      console.error("Error processing withdrawal:", error.message);
      throw error;
    }
  }

  // Redeem Asset
  async redeemAsset(amount: number, bankCode: string, accountNumber: string, saveDetails: boolean = true) {
    try {
      const redeemData: RedeemAsset = { amount, bankCode, accountNumber, saveDetails };
      return await this.manager.redeemAsset(redeemData);
    } catch (error: any) {
      console.error("Error redeeming asset:", error.message);
      throw error;
    }
  }

  // Create Virtual Account
  async createVirtualAccount(provider: any = 'korapay', bankCode: string = '011') {
    try {
      const mintData: CreateVirtualAccount = { provider, bank_code: bankCode };
      return await this.manager.createVirtualAccount(mintData);
    } catch (error: any) {
      console.error("Error creating virtual account:", error.message);
      throw error;
    }
  }

  // Swap Asset
  async swapAsset(destinationNetwork: Network, destinationAddress: string, originNetwork: Network, callbackUrl?: string) {
    try {
      const swapData: Swap = { destinationNetwork, destinationAddress, originNetwork, callbackUrl };
      return await this.manager.swapAsset(swapData);
    } catch (error: any) {
      console.error("Error swapping asset:", error.message);
      throw error;
    }
  }

  // Update Business
  async updateBusiness(walletAddress: string, bankName: string, bankAccountName: string, bankAccountNumber: string) {
    try {
      const updateData: UpdateExternalAccount = {
        walletAddress: { bscAddress: walletAddress },
        bankDetails: { bankName, bankAccountName, bankAccountNumber }
      };
      return await this.manager.updateExternalAccounts(updateData);
    } catch (error: any) {
      console.error("Error updating business info:", error.message);
      throw error;
    }
  }

  // Generate Wallet Address
    async generateWalletAddress(network: Network) {
    try {
      return await WalletManager.generateWalletAddress(network);
    } catch (error: any) {
      console.error("Error generating wallet address:", error.message);
      throw error;
    }
  }
}

export default CNGNUtility;

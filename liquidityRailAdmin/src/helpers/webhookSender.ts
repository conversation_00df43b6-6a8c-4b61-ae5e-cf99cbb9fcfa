import axios from 'axios';
import Model from './model';
import { CryptoDetails, DepositReceivedEvent, FiatDetails, TransactionCompleteEvent, TransactionInfo, TransactionUpdatedEvent, TransactionUpdateStatus, WithdrawSentEvent } from './webhook';
import { Quote } from '../interfaces/webhook.interfaces';





export class WebhookSender extends Model {
    private static readonly WEBHOOK_URL = process.env.WEBHOOK_URL || '';
    private static readonly TIMEOUT = 10000; // 10 seconds
    private static readonly MAX_RETRIES = 3;

    // Event types based on pay_in_status and status
    private static readonly EVENTS = {
        DEPOSIT_RECEIVED: 'deposit_received',
        TRANSFER_SENT: 'transfer_sent',
        TRANSACTION_SUCCESS: 'transaction_success',
        EXPIRED: 'expired',
        FAILED: 'failed'
    };

    /**
     * Send webhook based on transaction ID - determines event from database status
     */
    static async send(transactionId: string): Promise<boolean> {
        try {
            const instance = new WebhookSender();

            // Get transaction details from database
            const transaction = await instance.selectDataQuerySafe('quotes', { transId: transactionId });

            if (transaction.length === 0) {
                console.error('Transaction not found for webhook:', transactionId);
                return false;
            }

            const transactionData: Quote = transaction[0];
            const { pay_in_status, status } = transactionData;

            // Determine event type and status based on database fields
            let eventType: string;
            let webhookStatus: string;
            const finalStatus = ['SUCCESSFUL', 'FAILED', 'EXPIRED', 'CANCELLED'];

            if (pay_in_status === 'SUCCESSFUL' && status === 'PENDING') {
                eventType = 'deposit.received';
                webhookStatus = "RECEIVED"
            } else if (status === 'EXPIRED' || status === 'FAILED' || status === 'CANCELLED') {
                eventType = 'transaction.updated';
                webhookStatus = 'FAILED';
            } else if (pay_in_status === 'SUCCESSFUL' && status === 'SUCCESSFUL') {
                eventType = 'withdraw.sent';
                webhookStatus = 'SUCCESSFUL';
            } else {
                eventType = 'transaction.updated';
                webhookStatus = "PENDING"
            }

            if (finalStatus.includes(webhookStatus)) {
                eventType = 'transaction.complete';
                webhookStatus = status;
            }


            const transactionInfo: TransactionInfo = {
                client_id: transactionData.company_id.toString(),
                quote_id: transactionData.transId || transactionId,
                reference_id: transactionData.provider_ref_id || '',
                status: webhookStatus,
                trans_type: 'deposit',
                channel: 'crypto',
                transaction_flow: 'offramp',
                payin_asset: transactionData.send_asset,
                payout_asset: transactionData.receive_currency || 'USD',
                payin_amount: transactionData.send_amount
            }

            const cryptoDetails: CryptoDetails = {
                from_address: transactionData.sending_address,
                to_address: transactionData.receiver_address,
                amount: transactionData.send_amount,
                asset_code: transactionData.send_asset,
                contract_address: transactionData.contract_address || '',
                hash: transactionData.hash || '',
                state: transactionData.pay_in_status,
                direction: transactionData.direction || ''
            }



            const fiatDetails: FiatDetails = {
                status: transactionData.status,
                amount: transactionData.receive_amount.toString(),
                currency: transactionData.receive_currency,
                account_number: transactionData.account_number,
                payment_method: transactionData.payment_method_id || '',
                reference_id: transactionData.provider_ref_id || '',
                fee: transactionData.fee?.toString() || ''
            }

            const transactionCompleteEvent: TransactionCompleteEvent = {
                type: 'rail_event',
                event: 'transaction.complete',
                timestamp: new Date().toISOString(),
                id: transactionId,
                message: 'Transaction completed',
                transaction: transactionInfo,
                cryptoDetails: cryptoDetails,
                fiatDetails: fiatDetails
            }

            const withdrawSentEvent: WithdrawSentEvent = {
                type: 'rail_event',
                event: 'withdraw.sent',
                timestamp: new Date().toISOString(),
                id: transactionId,
                message: 'Withdrawal sent successfully',
                transaction: transactionInfo,
                fiatDetails: fiatDetails,
                cryptoDetails: cryptoDetails
            }


            const depositEvent: DepositReceivedEvent = {
                type: 'rail_event',
                event: 'deposit.received',
                timestamp: new Date().toISOString(),
                id: transactionId,
                message: 'Deposit received successfully',
                transaction: transactionInfo,
                cryptoDetails: cryptoDetails
            }

            const transactionUpdatedEvent: TransactionUpdatedEvent = {
                type: 'rail_event',
                event: 'transaction.updated',
                timestamp: new Date().toISOString(),
                id: transactionId,
                message: 'Transaction updated',
                status: webhookStatus,
                transaction: transactionInfo,
                cryptoDetails: cryptoDetails,
                fiatDetails: fiatDetails
            }

            let webhookData: any = { clientId: transactionData.company_id, transId: transactionId, event: "rail_event", webhookData: null }

            if (eventType === 'deposit.received') {
                webhookData.webhookData = depositEvent
            } else if (eventType === 'transaction.complete') {
                webhookData.webhookData = transactionCompleteEvent
            } else if (eventType === 'transaction.updated') {
                webhookData.webhookData = transactionUpdatedEvent
            } else if (eventType === 'withdraw.sent') {
                webhookData.webhookData = withdrawSentEvent
            }

            return await this.sendWebhook(webhookData);

            return false;
        } catch (error) {
            console.error('Error sending webhook:', error);
            return false;
        }
    }

    private static async sendWebhook(webhookData: any): Promise<boolean> {
        try {
            console.log('Sending webhook to:', this.WEBHOOK_URL);
            console.log('Webhook data::', webhookData);
            const response = await axios.post(this.WEBHOOK_URL, webhookData, {
                timeout: this.TIMEOUT,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'LiquidityRail-Webhook/1.0'
                }
            });
            console.log('Webhook response:', response.data);

            if (response.status >= 200 && response.status < 300) {
                console.log('Webhook sent successfully:', response.status);
                return true;
            } else {
                console.error('Webhook failed with status:', response.status);
                return false;
            }

        } catch (error: any) {
            console.error('Error sending webhook:', error.message);
            return false;
        }
    }


} 
import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

const BASE_URL = process.env.BASE_URL;
const KOTANI_API_KEY = process.env.KOTANI_API_KEY;

const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
  Accept: "application/json",
};

const buildHeaders = () => ({
  ...DEFAULT_HEADERS,
  Authorization: `Bearer ${KOTANI_API_KEY}`,
});

const handleAxiosError = (error: any) => {
  if (error.response) {
    // Server responded with a status other than 2xx
    return {
      success: false,
      status: error.response.status,
      message: error.response.data?.message || "Request failed",
      error_code: error.response.data?.error_code,
      data: error.response.data?.data || null,
    };
  } else if (error.request) {
    // No response received
    return {
      success: false,
      status: null,
      message: "No response from server",
      data: null,
    };
  } else {
    // Something else went wrong setting up the request
    return {
      success: false,
      status: null,
      message: error.message || "Unknown error",
      data: null,
    };
  }
};

export const get = async (endpoint: string, params?: any) => {
  try {
    const headers = buildHeaders();
    const response = await axios.get(`${BASE_URL}${endpoint}`, {
      headers,
      params,
    });
    return response.data;
  } catch (error: any) {
    return handleAxiosError(error);
  }
};

export const post = async (endpoint: string, data: any) => {
  try {
    const headers = buildHeaders();
    const response = await axios.post(`${BASE_URL}${endpoint}`, data, {
      headers,
    });
    return response.data;
  } catch (error: any) {
    console.log(`error`, error)
    return handleAxiosError(error);
  }
};

export const patch = async (endpoint: string, data: any) => {
  try {
    const headers = buildHeaders();
    const response = await axios.patch(`${BASE_URL}${endpoint}`, data, {
      headers,
    });
    return response.data;
  } catch (error: any) {
    return handleAxiosError(error);
  }
};

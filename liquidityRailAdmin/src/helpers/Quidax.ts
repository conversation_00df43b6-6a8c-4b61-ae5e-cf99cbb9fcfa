import RequestHelper from "./request.helper";

export interface QuidaxWithdrawData {
  user: string;
  currency: string;
  amount: string;
  transaction_note: string;
  narration: string;
  fund_uid: string;
  fund_uid2: string;
  reference: string;
  network: string;
}
class Quidax {
  private mainURL: string;
  private rampURL: string;
  private mainRequestHeader: Record<string, string>;
  private rampRequestHeader: Record<string, string>;

  constructor() {
    this.mainURL = process.env.QUIDAX_URL ?? "";
    this.rampURL = process.env.QUIDAX_RAMP_URL ?? "";

    // Off-ramp API headers (existing API)
    this.mainRequestHeader = {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.QUIDAX_SECRET}`,
    };


    // On-ramp API headers (RAMP product - different key)
    this.rampRequestHeader = {
      Accept: "application/json",
      "Content-Type": "application/json",
      "x-private-key": (process.env.QUIDAX_RAMP_SECRET || process.env.QUIDAX_SECRET) || "",
    };

    console.log('mainURL (off-ramp)', this.mainURL)
    console.log('rampURL (on-ramp)', this.rampURL)
    console.log('mainRequestHeader (off-ramp)', this.mainRequestHeader)
    console.log('rampRequestHeader (on-ramp)', this.rampRequestHeader)
    
    // Log Quidax ramp secret if it exists (first 2 chars + masked)
    if (process.env.QUIDAX_RAMP_SECRET && process.env.QUIDAX_RAMP_SECRET.length > 0) {
      const secret = process.env.QUIDAX_RAMP_SECRET;
      const maskedSecret = secret.substring(0, 2) + '*'.repeat(secret.length - 2);
      console.log('QUIDAX_RAMP_SECRET (masked)', maskedSecret);
    }
  }

  private async request(method: "get" | "post" | "put", endpoint: string, data?: any, useRampHeaders: boolean = false) {
    const baseURL = useRampHeaders ? this.rampURL : this.mainURL;
    RequestHelper.setEndpoint(`${baseURL}${endpoint}`);
    RequestHelper.setHeaders(useRampHeaders ? this.rampRequestHeader : this.mainRequestHeader);
    if (data) RequestHelper.setData(data);

    const response = await RequestHelper[`${method}Request`]();
    console.log('responsequidax', response)
    const errors = await RequestHelper.getErrors();
    return errors?.status === "error" ? errors : await RequestHelper.getResults();
  }

  // Sub-Accounts
  public createSubAccount(email: string, first_name: string, last_name: string) {
    return this.request("post", "users", { email, first_name, last_name });
  }

  public fetchParentAccount(user = "me") {
    return this.request("get", `users/${user}`);
  }

  public editSubAccountDetails(user = "me", first_name: string, last_name: string) {
    return this.request("put", `users/${user}`, { first_name, last_name });
  }

  public fetchAllSubAccounts() {
    return this.request("get", "users");
  }

  public fetchSubAccountDetails(user_id: string) {
    return this.request("get", `users/${user_id}`);
  }

  // Fees & Transactions
  public getCryptoWithdrawalFees(currency: string, network: string) {
    return this.request("get", `fee?currency=${currency}&network=${network}`);
  }

  // Bank Operations
  public getBanks() {
    return this.request("get", "banks?per_page=100");
  }

  public verifyBankAccountDetails(fund_uid: string, fund_uid2: string, currency: string) {
    return this.request("post", "banks/verify_account", { fund_uid, fund_uid2, currency });
  }

  public async createWithdraw(
    data: any
  ) {
    const send:QuidaxWithdrawData = {
      user: "me",
      currency: data.currency,
      amount: data.amount,
      transaction_note: data.transaction_note,
      narration: data.narration,
      fund_uid: data.fund_uid,
      fund_uid2: data.fund_uid2,
      reference: data.reference,
      network: data.network
    }
    console.log('sendquidax', send)
    const response = await this.request("post", `users/${data.user}/withdraws`, send);
    console.log('responsequidax', response.data)
    return response.data
  }

  public getWithdrawDetailsById(user = "me", id: string) {
    return this.request("get", `users/${user}/withdraws/${id}`);
  }

  public getWithdrawDetailsByReference(user = "me", reference: string) {
    return this.request("get", `users/${user}/withdraws/reference/${reference}`);
  }

  public handleWithdrawWebhooks(data: any) {
    switch (data?.event) {
      case "withdraw.successful":
        // Handle successful withdrawal
        break;
      case "withdraw.rejected":
        // Handle rejected withdrawal
        break;
    }
    return data;
  }


  public getUserWallet(user = "me") {
    return this.request("get", `users/${user}/wallets`);
  }
  public getPaymentAddress(user = "me", currency: string) {
    return this.request("get", `users/${user}/wallets/${currency}/address`);
  }

  public createPaymentAddress(user = "me", currency: string, network: string) {
    return this.request("post", `users/${user}/wallets/${currency}/addresses?network=${network}`);
  }

  public validatePaymentAddress(currency: string, address: string) {
    return this.request("get", `${currency}/${address}/validate_address`);
  }

  // Currency Swap
  public makeCryptoFiatSwap(user = "me", from_currency: string, to_currency: string, from_amount: any) {
    console.log(`makeCryptoFiatSwap`, from_currency, to_currency, from_amount)
    return this.request("post", `users/${user}/swap_quotation`, { from_currency, to_currency, from_amount });
  }

  public async confirmCryptoFiatSwap(user = "me", quotation_id: string) {
    const swap = `users/${user}/swap_quotation/${quotation_id}/confirm`
    console.log(`confirmCryptoFiatSwap`, swap)
    return await this.request("post", swap);
  }

  public refreshCryptoFiatSwap(
    user = "me",
    quotation_id: string,
    from_currency: string,
    to_currency: string,
    from_amount: any
  ) {
    return this.request("post", `users/${user}/swap_quotation/${quotation_id}/refresh`, { from_currency, to_currency, from_amount });
  }

  public fetchCryptoFiatSwapTransaction(user = "me") {
    return this.request("get", `users/${user}/swap_transactions`);
  }

  public fetchSingleCryptoFiatSwapTransaction(user = "me", swap_transaction_id: string) {
    return this.request("get", `users/${user}/swap_transactions/${swap_transaction_id}`);
  }

  // ===== ON-RAMP METHODS =====

  // Get purchase quotes for on-ramp (uses RAMP API key)
  public getPurchaseQuote(currency: string, token: string, fiat_amount: number, token_network: string = "trc20") {
    return this.request("get", `purchase_quotes/buy?currency=${currency}&token=${token}&fiat_amount=${fiat_amount}&token_network=${token_network}`, undefined, true);
  }

  // Get purchase limits for a currency (uses RAMP API key)
  public getPurchaseLimits(currency_symbol: string) {
    return this.request("get", `purchase_limits/buy?currency_symbol=${currency_symbol}`, undefined, true);
  }

  // Initiate on-ramp transaction (uses RAMP API key)
  public initiateOnRampTransaction(data: {
    merchant_reference: string;
    from_currency: string;
    to_currency: string;
    from_amount: string;
    customer: {
      email: string;
      first_name: string;
      last_name: string;
    };
    wallet_address: {
      address: string;
      network: string;
    };
  }) {
    return this.request("post", "custodial/on_ramp_transactions/initiate", {
      merchant_reference: data.merchant_reference,
      from_currency: data.from_currency,
      to_currency: data.to_currency,
      from_amount: data.from_amount,
      customer: data.customer,
      wallet_address: data.wallet_address
    }, true);
  }

  // Confirm on-ramp transaction to get bank details (uses RAMP API key)
  public confirmOnRampTransaction(merchant_reference: string) {
    return this.request("post", `custodial/on_ramp_transactions/${merchant_reference}/confirm`, undefined, true);
  }

  // Fetch on-ramp transaction status (uses RAMP API key)
  public fetchOnRampTransaction(merchant_reference: string) {
    return this.request("get", `on_ramp_transactions/${merchant_reference}`, undefined, true);
  }

  // Handle on-ramp webhooks
  public handleOnRampWebhooks(data: any) {
    switch (data?.event) {
      case "buy_transaction.successful":
        return {
          event: data.event,
          merchant_reference: data.data.merchant_reference,
          status: "completed" as const,
          mode: data.data.mode,
          network: data.data.network,
          crypto_amount: data.data.to_amount,
          crypto_currency: data.data.to_currency,
          fiat_amount: data.data.from_amount,
          fiat_currency: data.data.from_currency,
          transaction_hash: data.data.crypto_payout?.transaction_hash,
          quidax_reference: data.data.public_id,
          blockchain_fee: data.data.blockchain_fee,
          processor_fee: data.data.crypto_payout?.processor_fee,
          fiat_deposit: data.data.fiat_deposit,
          crypto_payout: data.data.crypto_payout
        };
      case "buy_transaction.processing":
        return {
          event: data.event,
          merchant_reference: data.data.merchant_reference,
          status: "processing" as const,
          mode: data.data.mode,
          network: data.data.network,
          crypto_amount: data.data.to_amount,
          crypto_currency: data.data.to_currency,
          fiat_amount: data.data.from_amount,
          fiat_currency: data.data.from_currency,
          quidax_reference: data.data.public_id,
          blockchain_fee: data.data.blockchain_fee,
          fiat_deposit: data.data.fiat_deposit,
          crypto_payout: data.data.crypto_payout // Will be null for processing
        };
      case "buy_transaction.failed":
        return {
          event: data.event,
          merchant_reference: data.data.merchant_reference,
          status: "failed" as const,
          mode: data.data.mode,
          network: data.data.network,
          crypto_amount: data.data.to_amount,
          crypto_currency: data.data.to_currency,
          fiat_amount: data.data.from_amount,
          fiat_currency: data.data.from_currency,
          quidax_reference: data.data.public_id,
          blockchain_fee: data.data.blockchain_fee,
          fiat_deposit: data.data.fiat_deposit,
          crypto_payout: data.data.crypto_payout,
          reason: data.data.status === "needs_attention" ? "Transaction needs attention" : "Transaction failed"
        };
      default:
        return {
          event: data.event || "unknown",
          merchant_reference: data.data?.merchant_reference || "",
          status: "unknown" as const,
          data: data
        };
    }
  }
}

export default new Quidax();

import axios from "axios";
import { getItem, setItem } from "./connectRedis";

class MagmaOnePay {
  private baseURL: string;
  private tokenKey: string;

  constructor() {
    this.baseURL = process.env.MAGMA_API_URL ?? "https://api.magmaonepay.com/";
    this.tokenKey = "magmaonepay_token";
  }

  private async getToken(): Promise<string | null> {
    let token = await getItem(this.tokenKey);
    if (!token) {
      token = process.env.MAGMA_PRIVATE_KEY ?? null;
      if (token) {
        await setItem(this.tokenKey, token);
      }
    }
    return token;
  }

  private async request(
    method: "get" | "post",
    endpoint: string,
    data?: any,
    auth = true
  ) {
    const headers: Record<string, string> = {
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    if (auth) {
      const token = await this.getToken();
      if (token) headers["Authorization"] = `Bearer ${token}`;
    }

    try {
      const response = await axios({
        method,
        url: `${this.baseURL}${endpoint}`,
        headers,
        data,
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error?.response?.data || error.message,
      };
    }
  }

  // 1. Get Payment Methods
  public async getPaymentMethods() {
    return this.request("get", "payment-methods");
  }

  // 2. Initialize Payment
  public async initializePayment(data: {
    amount: number;
    currency: string;
    payment_method: string;
    customer: {
      name: string;
      email: string;
      phone: string;
    };
  }) {
    return this.request("post", "payment-initialization", data);
  }

  // 3. Process Payment
  public async processPayment(data: {
    payment_id: string;
    confirmation_code: string;
  }) {
    return this.request("post", "payment-process", data);
  }

  // 4. Get Payment Status
  public async getPaymentStatus(payment_id: string) {
    return this.request("get", `payment-status/${payment_id}`);
  }

  // 5. Get Available Payout Methods
  public async getPayoutMethods() {
    return this.request("get", "payout-methods");
  }

  // 6. Get Balance
  public async getBalance() {
    return this.request("get", "balance");
  }

  // 7. Execute Transfer
  public async executeTransfer(data: {
    amount: number;
    currency: string;
    payout_method: string;
    recipient: {
      name: string;
      account_number: string;
      bank_code: string;
    };
  }) {
    return this.request("post", "execute-transfer", data);
  }

  // 8. Get Transfer Status
  public async getTransferStatus(transfer_id: string) {
    return this.request("get", `transfer-status/${transfer_id}`);
  }

  // 9. Get Transfer History
  public async getTransferHistory() {
    return this.request("get", "transfer-history");
  }

  // 10. Get Transfer History by Receiver Account
  public async getTransferHistoryByReceiver(account_number: string) {
    return this.request("get", `transfer-history/${account_number}`);
  }

  // 11. Create Collection
  public async createCollection(data: {
    amount: number;
    currency: string;
    payment_method: string;
    customer: {
      name: string;
      email: string;
      phone: string;
    };
    description?: string;
    callback_url?: string;
  }) {
    return this.request("post", "collection-initialization", data);
  }

  // 12. Get Collection Status
  public async getCollectionStatus(collection_id: string) {
    return this.request("get", `collection-status/${collection_id}`);
  }

  // 13. Get Collection History
  public async getCollectionHistory() {
    return this.request("get", "collection-history");
  }

  // 14. Get Collection History by Customer
  public async getCollectionHistoryByCustomer(customer_id: string) {
    return this.request("get", `collection-history/${customer_id}`);
  }
}

export default new MagmaOnePay();

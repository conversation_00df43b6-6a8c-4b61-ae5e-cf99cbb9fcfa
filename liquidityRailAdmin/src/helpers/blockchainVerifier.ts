import axios from 'axios';

interface TransactionVerificationResult {
    isValid: boolean;
    amount?: string;
    from?: string;
    to?: string;
    error?: string;
}

class BlockchainVerifier {
    private readonly STELLAR_API = 'https://horizon.stellar.org';
    private readonly BSC_API = 'https://api.bscscan.com/api';
    private readonly TRON_API = 'https://api.trongrid.io';
    private readonly BSC_API_KEY = process.env.BSC_API_KEY || '';
    private readonly TRON_API_KEY = process.env.TRON_API_KEY || '';

    async verifyTransaction(hash: string, chain: string, expectedAmount?: string): Promise<TransactionVerificationResult> {
        switch (chain.toUpperCase()) {
            case 'STELLAR':
            case 'XLM':
                return this.verifyStellarTransaction(hash);
            case 'BSC':
                return this.verifyBSCTransaction(hash);
            case 'TRON':
                return this.verifyTronTransaction(hash);
            default:
                return {
                    isValid: false,
                    error: `Unsupported blockchain: ${chain}`
                };
        }
    }

    private async verifyStellarTransaction(hash: string): Promise<TransactionVerificationResult> {
        try {
            const response = await axios.get(`${this.STELLAR_API}/transactions/${hash}`);
            const transaction = response.data;

            if (!transaction) {
                return {
                    isValid: false,
                    error: 'Transaction not found'
                };
            }

            // Extract payment operation details
            const operations = await axios.get(`${this.STELLAR_API}/transactions/${hash}/operations`);
            const paymentOp = operations.data._embedded.records.find((op: any) => op.type === 'payment');

            if (!paymentOp) {
                return {
                    isValid: false,
                    error: 'No payment operation found in transaction'
                };
            }

            return {
                isValid: true,
                amount: paymentOp.amount,
                from: paymentOp.from,
                to: paymentOp.to
            };
        } catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    private async verifyBSCTransaction(hash: string): Promise<TransactionVerificationResult> {
        try {
            const response = await axios.get(`${this.BSC_API}`, {
                params: {
                    module: 'transaction',
                    action: 'gettxreceiptstatus',
                    txhash: hash,
                    apikey: this.BSC_API_KEY
                }
            });

            if (response.data.status !== '1') {
                return {
                    isValid: false,
                    error: 'Transaction failed or not found'
                };
            }

            // Get transaction details
            const txResponse = await axios.get(`${this.BSC_API}`, {
                params: {
                    module: 'transaction',
                    action: 'gettxinfo',
                    txhash: hash,
                    apikey: this.BSC_API_KEY
                }
            });

            const tx = txResponse.data.result;
            return {
                isValid: true,
                amount: tx.value,
                from: tx.from,
                to: tx.to
            };
        } catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }

    private async verifyTronTransaction(hash: string): Promise<TransactionVerificationResult> {
        try {
            const response = await axios.get(`${this.TRON_API}/v1/transactions/${hash}`, {
                headers: {
                    'TRON-PRO-API-KEY': this.TRON_API_KEY
                }
            });

            if (!response.data || response.data.ret[0].contractRet !== 'SUCCESS') {
                return {
                    isValid: false,
                    error: 'Transaction failed or not found'
                };
            }

            const tx = response.data;
            return {
                isValid: true,
                amount: tx.raw_data.contract[0].parameter.value.amount,
                from: tx.raw_data.contract[0].parameter.value.owner_address,
                to: tx.raw_data.contract[0].parameter.value.to_address
            };
        } catch (error) {
            return {
                isValid: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
}

export default BlockchainVerifier; 
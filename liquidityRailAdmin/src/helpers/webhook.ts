// Possible intermediate statuses inside transaction.updated
export enum TransactionUpdateStatus {
  CONVERT_SUCCESSFUL = 'CONVERT_SUCCESSFUL',
  CONVERT_FAILED = 'CONVERT_FAILED',
  PENDING = 'PENDING',
  RETRYING = 'RETRYING',
  ONHOLD = 'ONHOLD',
}

export interface FiatDetails {
  status?: string;
  amount?: string;
  currency?: string;
  account_number?: string;
  payment_method?: string;
  reference_id?: string;
  fee?: string;
}

export interface CryptoDetails {
  from_address: string;
  to_address: string;
  amount: string;
  asset_code: string;
  contract_address?: string;
  hash: string;
  state: string;
  direction: string;
}

export interface TransactionInfo {
  client_id: string;
  quote_id: string;
  reference_id: string,
  status: 'SUCCESSFUL' | 'FAILED' | 'EXPIRED' | 'ONHOLD' | 'PENDING' | 'RETRYING' | string;
  trans_type: 'deposit' | 'withdrawal' | 'conversion';
  channel: 'crypto' | 'fiat';
  transaction_flow: 'onramp' | 'offramp';  // direction of transaction
  payin_asset: string;   // incoming asset e.g., USDT
  payout_asset: string;  // outgoing asset e.g., UGX
  payin_amount: string;  // incoming amount
  fiat_equivalent?: {
    currency: string;
    amount: string;
  };
}

// Base event interface with common fields
interface BaseEvent {
  event: string;
  timestamp: string;
  id:string,
  type: 'rail_event';
  message: string;
  transaction: TransactionInfo;
  fiatDetails?: FiatDetails;
  cryptoDetails?: CryptoDetails;
}

// Specific event types

export interface DepositReceivedEvent extends BaseEvent {
  event: 'deposit.received';
}

export interface WithdrawSentEvent extends BaseEvent {
  event: 'withdraw.sent';
}

export interface TransactionUpdatedEvent extends BaseEvent {
  event: 'transaction.updated';
  status: TransactionUpdateStatus | string;
}

export interface TransactionCompleteEvent extends BaseEvent {
  event: 'transaction.complete';
}

import RequestHelper from "./request.helper";

class Borderless {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseURL = process.env.BORDERLESS_API_URL || "https://sandbox-api.borderless.xyz/v1";
    this.headers = {
      Accept: "application/json",
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.BORDERLESS_API_KEY}`,
    };
  }

  private async request(method: "get" | "post", endpoint: string, data?: any) {
    RequestHelper.setEndpoint(`${this.baseURL}${endpoint}`);
    RequestHelper.setHeaders(this.headers);
    if (data) RequestHelper.setData(data);

    const response = await RequestHelper[`${method}Request`]();
    const errors = await RequestHelper.getErrors();
    return errors?.status === "error" ? errors : await RequestHelper.getResults();
  }

  /**
   * Create a quote for swapping assets
   */
  public createQuote(fromAccountId: string, fromAmount: string, fromAsset: string, toAsset: string) {
    return this.request("post", "/swaps/quote", {
      fromAccountId,
      fromAmount,
      fromAsset,
      toAsset,
    });
  }

  /**
   * Confirm (execute) a previously created swap quote
   */
  public executeQuote(quoteId: string) {
    return this.request("post", `/swaps/quote/${quoteId}/execute`);
  }

  /**
   * Initiate a withdrawal
   */
  public makeWithdrawal(params: {
    fiat: string;
    country: string;
    asset: string;
    amount: string;
    accountId: string;
    paymentPurpose: string;
    paymentInstructionId: string;
    passphrase: string;
  }) {
    const idempotencyKey = crypto.randomUUID();
    this.headers["idempotency-key"] = idempotencyKey;

    return this.request("post", "/withdrawals", params);
  }
}

export default new Borderless();

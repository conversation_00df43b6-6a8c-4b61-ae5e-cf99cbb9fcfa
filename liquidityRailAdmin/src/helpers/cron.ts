import * as cron from 'node-cron';
import Accounts from '../models/accounts';
import MudaPayment from './MudaPayment';

class CronService {
    constructor() {
        console.log("cron initiated==>")
        this.scheduleEveryThirtySeconds();
        this.Every45Seconds();
        this.scheduleEveryTwelveHours();
        MudaPayment.getJWT();

    }

    private scheduleEveryTwelveHours() {
        cron.schedule('0 */12 * * *', this.everyTwelveHoursTask);
    }

    private everyTwelveHoursTask() {
        console.log('Task running every 12 hours, get muda key');
        // Add your logic to get muda key here
        MudaPayment.getJWT();
    }

    private Every45Seconds() {
        cron.schedule('*/45 * * * * *', this.scheduleEvery45Seconds);
    }


    private scheduleEveryThirtySeconds() {
        cron.schedule('*/59 * * * * *', this.everyThirtySecondsTask);
    }


    private scheduleEvery45Seconds() {
        console.log('Task running every 59 seconds, check pending deposits');
        new Accounts().expirePendingQuotes()
    }

    private everyThirtySecondsTask() {
        console.log('Task running every 45 seconds, check pending deposits');
        new Accounts().getQuotestatus()
    }
}

export default CronService;

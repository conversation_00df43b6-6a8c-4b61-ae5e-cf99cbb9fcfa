{"name": "stellar-service", "version": "1.0.0", "description": "Stellar transaction monitoring microservice", "main": "src/app.ts", "scripts": {"start": "ts-node src/app.ts", "dev": "nodemon --exec ts-node src/app.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["stellar", "blockchain", "notifications", "microservice"], "author": "", "license": "ISC", "dependencies": {"stellar-sdk": "^11.3.0", "dotenv": "^16.3.1", "axios": "^1.6.3", "express": "^4.18.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.17.46", "@types/cors": "^2.8.17", "@types/body-parser": "^1.19.5", "@types/uuid": "^9.0.7", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}
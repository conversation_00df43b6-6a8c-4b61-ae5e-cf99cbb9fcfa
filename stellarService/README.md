# Stellar Service

A TypeScript microservice that monitors Stellar blockchain transactions for a specific public key and sends notifications.

## Features

- 🔍 Real-time monitoring of Stellar payments
- 📢 Webhook notifications for new transactions
- 🚀 Express.js REST API with TypeScript
- 🔄 Automatic reconnection on connection loss
- 💊 Health check endpoints
- 🛡️ Type-safe implementation

## Project Structure

```
stellarService/
├── src/
│   ├── controllers/
│   │   └── stellar.ts          # API endpoints
│   ├── services/
│   │   ├── StellarMonitor.ts   # Core monitoring logic
│   │   └── NotificationService.ts # Notification handling
│   ├── types/
│   │   └── index.ts            # Type definitions
│   └── app.ts                  # Main application
├── package.json
├── tsconfig.json
├── env.example
└── README.md
```

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp env.example .env
   ```
   
   Edit `.env` with your values:
   ```env
   STELLAR_HORIZON_URL=https://horizon.stellar.org
   STELLAR_PUBLIC_KEY=GDXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
   NOTIFICATION_WEBHOOK_URL=http://localhost:3001/api/notifications
   NOTIFICATION_API_KEY=your_api_key_here
   PORT=3005
   NODE_ENV=development
   ```

3. **Start the service:**
   ```bash
   # Development with auto-reload
   npm run dev
   
   # Production
   npm start
   
   # Build TypeScript
   npm run build
   ```

## API Endpoints

### Health & Info
- `GET /health` - Health check
- `GET /info` - Service information

### Stellar Monitoring
- `GET /stellar/status` - Get monitoring status
- `POST /stellar/start` - Start monitoring
- `POST /stellar/stop` - Stop monitoring
- `POST /stellar/test-notification` - Send test notification

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `STELLAR_HORIZON_URL` | Stellar Horizon server URL | Yes | `https://horizon.stellar.org` |
| `STELLAR_PUBLIC_KEY` | Public key to monitor | Yes | - |
| `NOTIFICATION_WEBHOOK_URL` | Webhook URL for notifications | No | - |
| `NOTIFICATION_API_KEY` | API key for webhook auth | No | - |
| `PORT` | Service port | No | `3005` |
| `NODE_ENV` | Environment | No | `development` |

## Webhook Interface

```typescript
interface WebhookPayload {
  type: 'stellar_payment';
  timestamp: string;
  data: {
    transaction_id: string;
    amount: string;
    asset_type: string;
    asset_code: string;
    asset_issuer?: string;
    coin: string;
    issuer?: string;
    from: string;
    to: string;
    memo?: string;
    memo_type?: string;
    created_at: string;
    transaction_hash: string;
    source_account: string;
  };
  message: string;
}
```

## Example Webhook Payload

### Native XLM Payment
```json
{
  "type": "stellar_payment",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "transaction_id": "payment_id",
    "amount": "100.0000000",
    "asset_type": "native",
    "asset_code": "XLM",
    "coin": "XLM",
    "from": "GFROM...",
    "to": "GTO...",
    "memo": "Payment for services",
    "memo_type": "text",
    "created_at": "2024-01-01T12:00:00.000Z",
    "transaction_hash": "hash...",
    "source_account": "GSOURCE..."
  },
  "message": "New Stellar payment: 100.0000000 XLM from GFROM... to GTO... (memo: Payment for services)"
}
```

### Custom Asset Payment
```json
{
  "type": "stellar_payment",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "transaction_id": "payment_id",
    "amount": "50.0000000",
    "asset_type": "credit_alphanum4",
    "asset_code": "USDC",
    "asset_issuer": "GISSUER...",
    "coin": "USDC",
    "issuer": "GISSUER...",
    "from": "GFROM...",
    "to": "GTO...",
    "created_at": "2024-01-01T12:00:00.000Z",
    "transaction_hash": "hash...",
    "source_account": "GSOURCE..."
  },
  "message": "New Stellar payment: 50.0000000 USDC from GFROM... to GTO..."
}
```

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build TypeScript
npm run build

# Run built version
npm start
```

## Integration with Aggregator System

This service follows the established patterns from the aggregator backend:

- **TypeScript**: Full type safety
- **Express.js**: RESTful API endpoints
- **Service Layer**: Business logic separation
- **Controller Pattern**: Route handling
- **Environment Configuration**: `.env` based config

## Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3005
CMD ["npm", "start"]
```

```bash
# Build
docker build -t stellar-service .

# Run
docker run -p 3005:3005 --env-file .env stellar-service
``` 
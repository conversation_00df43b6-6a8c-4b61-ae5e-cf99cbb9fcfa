export interface StellarPayment {
  id: string;
  amount: string;
  asset_type: string;
  asset_code?: string;
  asset_issuer?: string;
  from: string;
  to: string;
  memo?: string;
  memo_type?: string;
  created_at: string;
  transaction_hash: string;
  source_account: string;
  type_i: number;
  type: string;
}

export interface ProcessEnv {
  STELLAR_HORIZON_URL?: string;
  STELLAR_PUBLIC_KEY?: string;
  NOTIFICATION_WEBHOOK_URL?: string;
  NOTIFICATION_API_KEY?: string;
  PORT?: string;
  NODE_ENV?: string;
}

export interface WebhookPayload {
  type: 'stellar_payment';
  timestamp: string;
  data: {
    transaction_id: string;
    amount: string;
    asset_type: string;
    asset_code: string;
    asset_issuer?: string;
    coin: string;
    issuer?: string;
    from: string;
    to: string;
    memo?: string;
    memo_type?: string;
    created_at: string;
    transaction_hash: string;
    source_account: string;
  };
  message: string;
}

export interface NotificationPayload extends WebhookPayload {}

export interface ServiceStatus {
  isMonitoring: boolean;
  publicKey: string;
  horizonUrl: string;
}

export interface ServiceInfo {
  service: string;
  version: string;
  monitoring: string;
  horizon: string;
} 
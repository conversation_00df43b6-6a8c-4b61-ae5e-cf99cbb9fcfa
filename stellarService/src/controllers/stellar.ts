import express, { Request, Response } from 'express';
import StellarMonitor from '../services/StellarMonitor';
import NotificationService from '../services/NotificationService';

const router = express.Router();
const stellarMonitor = new StellarMonitor();
const notificationService = new NotificationService();

// Get monitoring status
router.get('/status', (req: Request, res: Response) => {
  try {
    const status = stellarMonitor.getStatus();
    res.json({
      status: 200,
      data: status,
      message: 'Status retrieved successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      status: 500,
      message: error.message || 'Failed to get status'
    });
  }
});

// Start monitoring
router.post('/start', async (req: Request, res: Response) => {
  try {
    await stellarMonitor.start();
    res.json({
      status: 200,
      message: 'Stellar monitoring started successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      status: 500,
      message: error.message || 'Failed to start monitoring'
    });
  }
});

// Stop monitoring
router.post('/stop', (req: Request, res: Response) => {
  try {
    stellarMonitor.stop();
    res.json({
      status: 200,
      message: 'Stellar monitoring stopped successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      status: 500,
      message: error.message || 'Failed to stop monitoring'
    });
  }
});

// Send test notification
router.post('/test-notification', async (req: Request, res: Response) => {
  try {
    await notificationService.sendTestNotification();
    res.json({
      status: 200,
      message: 'Test notification sent successfully'
    });
  } catch (error: any) {
    res.status(500).json({
      status: 500,
      message: error.message || 'Failed to send test notification'
    });
  }
});

export default router; 
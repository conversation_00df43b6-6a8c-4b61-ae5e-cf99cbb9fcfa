import express, { Request, Response } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import StellarMonitor from './services/StellarMonitor';
import stellarController from './controllers/stellar';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3005;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.set('trust proxy', true);

// Routes
app.use('/stellar', stellarController);

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.json({ 
    status: 'healthy', 
    service: 'stellar-service',
    timestamp: new Date().toISOString()
  });
});

// Service info endpoint
app.get('/info', (req: Request, res: Response) => {
  res.json({
    service: 'stellar-service',
    version: '1.0.0',
    monitoring: process.env.STELLAR_PUBLIC_KEY,
    horizon: process.env.STELLAR_HORIZON_URL
  });
});

// Initialize Stellar Monitor
const stellarMonitor = new StellarMonitor();

// Start the service
app.listen(PORT, async () => {
  console.log(`🚀 Stellar Service running on port ${PORT}`);
  console.log(`📡 Monitoring Stellar account: ${process.env.STELLAR_PUBLIC_KEY}`);
  
  try {
    await stellarMonitor.start();
    console.log('✅ Stellar monitoring started successfully');
  } catch (error: any) {
    console.error('❌ Failed to start Stellar monitoring:', error.message);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  stellarMonitor.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  stellarMonitor.stop();
  process.exit(0);
});

export default app; 
import * as StellarSdk from 'stellar-sdk';
import NotificationService from './NotificationService';
import { StellarPayment, ServiceStatus } from '../types';

class StellarMonitor {
  private server: StellarSdk.Horizon.Server;
  private publicKey: string;
  private notificationService: NotificationService;
  private isMonitoring: boolean = false;
  private closeFunction: (() => void) | null = null;

  constructor() {
    this.server = new StellarSdk.Horizon.Server(process.env.STELLAR_HORIZON_URL || 'https://horizon.stellar.org');
    this.publicKey = process.env.STELLAR_PUBLIC_KEY || '';
    this.notificationService = new NotificationService();
  }

  async start(): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚠️ Monitoring is already running');
      return;
    }

    if (!this.publicKey) {
      throw new Error('STELLAR_PUBLIC_KEY is required');
    }

    try {
      // Validate the public key
      if (!StellarSdk.StrKey.isValidEd25519PublicKey(this.publicKey)) {
        throw new Error('Invalid Stellar public key format');
      }

      console.log(`🔍 Starting to monitor account: ${this.publicKey}`);
      
      // Start streaming payments
      this.closeFunction = this.server
        .payments()
        .forAccount(this.publicKey)
        .cursor('now')
        .stream({
          onmessage: (record: any) => this.handlePayment(record),
          onerror: (error: any) => this.handleError(error)
        });

      this.isMonitoring = true;
      console.log('✅ Payment monitoring started');

    } catch (error: any) {
      console.error('❌ Error starting Stellar monitor:', error.message);
      throw error;
    }
  }

  stop(): void {
    if (this.closeFunction) {
      this.closeFunction();
      this.isMonitoring = false;
      console.log('🛑 Stellar monitoring stopped');
    }
  }

  private async handlePayment(record: any): Promise<void> {
    try {
      // Filter only payment operations
      if (record.type !== 'payment') {
        return;
      }

      // Fetch transaction details to get memo
      let memo: string | undefined;
      let memo_type: string | undefined;
      
      try {
        const transaction = await this.server.transactions().transaction(record.transaction_hash).call();
        memo = transaction.memo || undefined;
        memo_type = transaction.memo_type || undefined;
      } catch (memoError: any) {
        console.warn('⚠️ Could not fetch transaction memo:', memoError.message);
      }

      // Convert Stellar SDK record to our StellarPayment type
      const payment: StellarPayment = {
        id: record.id,
        amount: record.amount,
        asset_type: record.asset_type,
        asset_code: record.asset_code || 'XLM',
        asset_issuer: record.asset_issuer,
        from: record.from,
        to: record.to,
        memo: memo,
        memo_type: memo_type,
        created_at: record.created_at,
        transaction_hash: record.transaction_hash,
        source_account: record.source_account,
        type_i: record.type_i,
        type: record.type
      };

      console.log('💰 New payment detected:', payment);

      // Send notification
      await this.notificationService.sendPaymentNotification(payment);

    } catch (error: any) {
      console.error('❌ Error handling payment:', error.message);
    }
  }

  private handleError(error: any): void {
    console.error('❌ Stellar stream error:', error);
    
    // Attempt to reconnect after a delay
    setTimeout(() => {
      if (this.isMonitoring) {
        console.log('🔄 Attempting to reconnect...');
        this.stop();
        this.start().catch(err => {
          console.error('❌ Reconnection failed:', err.message);
        });
      }
    }, 5000);
  }

  getStatus(): ServiceStatus {
    return {
      isMonitoring: this.isMonitoring,
      publicKey: this.publicKey,
      horizonUrl: process.env.STELLAR_HORIZON_URL || 'https://horizon.stellar.org'
    };
  }
}

export default StellarMonitor; 
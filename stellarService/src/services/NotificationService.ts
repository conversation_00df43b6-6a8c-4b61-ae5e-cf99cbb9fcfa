import axios from 'axios';
import { StellarPayment, WebhookPayload } from '../types';

class NotificationService {
  private webhookUrl: string;
  private apiKey: string;

  constructor() {
    this.webhookUrl = process.env.NOTIFICATION_WEBHOOK_URL || '';
    this.apiKey = process.env.NOTIFICATION_API_KEY || '';
  }

  async sendPaymentNotification(payment: StellarPayment): Promise<void> {
    try {
      const notification: WebhookPayload = {
        type: 'stellar_payment',
        timestamp: new Date().toISOString(),
        data: {
          transaction_id: payment.id,
          amount: payment.amount,
          asset_type: payment.asset_type,
          asset_code: payment.asset_code || 'XLM',
          asset_issuer: payment.asset_issuer,
          coin: payment.asset_code || 'XLM',
          issuer: payment.asset_issuer,
          from: payment.from,
          to: payment.to,
          memo: payment.memo,
          memo_type: payment.memo_type,
          created_at: payment.created_at,
          transaction_hash: payment.transaction_hash,
          source_account: payment.source_account
        },
        message: `New Stellar payment: ${payment.amount} ${payment.asset_code || 'XLM'} from ${payment.from} to ${payment.to}${payment.memo ? ` (memo: ${payment.memo})` : ''}`
      };

      // Send to webhook if configured
      if (this.webhookUrl) {
        await this.sendWebhook(notification);
      }

      // Log notification
      console.log('📢 Notification sent:', notification.message);

    } catch (error: any) {
      console.error('❌ Error sending notification:', error.message);
    }
  }

  private async sendWebhook(notification: WebhookPayload): Promise<void> {
    try {
      const headers: any = {
        'Content-Type': 'application/json'
      };

      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      const response = await axios.post(this.webhookUrl, notification, {
        headers,
        timeout: 10000
      });

      console.log('✅ Webhook sent successfully:', response.status);

    } catch (error: any) {
      if (error.response) {
        console.error('❌ Webhook failed:', error.response.status, error.response.data);
      } else {
        console.error('❌ Webhook error:', error.message);
      }
      throw error;
    }
  }

  async sendTestNotification(): Promise<void> {
    const testPayment: StellarPayment = {
      id: 'test_payment_id',
      amount: '100.0000000',
      asset_type: 'native',
      asset_code: 'XLM',
      asset_issuer: undefined,
      from: 'GTEST...FROM',
      to: 'GTEST...TO',
      memo: 'Test payment memo',
      memo_type: 'text',
      created_at: new Date().toISOString(),
      transaction_hash: 'test_hash',
      source_account: 'GTEST...SOURCE',
      type_i: 1,
      type: 'payment'
    };

    await this.sendPaymentNotification(testPayment);
  }
}

export default NotificationService; 
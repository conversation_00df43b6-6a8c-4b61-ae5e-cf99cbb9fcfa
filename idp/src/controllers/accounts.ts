import express, { Request, Response } from "express";
import Accounts from "../models/accounts";
import { JWTMiddleware } from "../helpers/jwt.middleware";

const router = express.Router();
const accounts = new Accounts();
const requireAuth = JWTMiddleware.verifyTokenAccess;

// Public Routes
router.post("/register", register);
router.post("/login", login);
router.post("/verify-email", verifyEmail);
router.post("/request-password-reset", requestPasswordReset);
router.post("/reset-password", resetPassword);

// Protected Routes
router.get("/users", requireAuth, getAllUsers);
router.get("/users/profile/:profileId", requireAuth, getUsersByProfile);
router.post("/users/create", requireAuth, createUserLogin);
router.get("/profile", requireAuth, getProfile);
router.post("/change-password", requireAuth, changePassword);


async function register(req: Request, res: Response) {
  try {
    const result = await accounts.register(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function login(req: Request, res: Response) {
  try {
    const result = await accounts.login(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function verifyEmail(req: Request, res: Response) {
  try {
    const result = await accounts.verifyEmail(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function requestPasswordReset(req: Request, res: Response) {
  try {
    const result = await accounts.requestPasswordReset(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function resetPassword(req: Request, res: Response) {
  try {
    const result = await accounts.resetPassword(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function changePassword(req: Request, res: Response) {
  try {
    const result = await accounts.changepassword(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function getAllUsers(req: Request, res: Response) {
  try {
    const result = await accounts.getAllusers();
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function getUsersByProfile(req: Request, res: Response) {
  try {
    const result = await accounts.getusersByProfile(req.params.profileId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function createUserLogin(req: Request, res: Response) {
  try {
    const result = await accounts.createUserLogin(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

async function getProfile(req: Request, res: Response) {
  try {
    const userId = (req as any).user.userId;
    const result = await accounts.getProfile(userId);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
}

export default router;

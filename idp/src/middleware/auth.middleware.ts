import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

export interface AuthenticatedRequest extends Request {
  user?: any;
  client_id?: string;
  user_id?: string;
}

export class AuthMiddleware {
  static authenticate(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return res.status(401).json({
          status: 401,
          message: 'Authentication required'
        });
      }

      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        return res.status(500).json({
          status: 500,
          message: 'Server configuration error'
        });
      }

      const decoded = jwt.verify(token, jwtSecret) as any;
      req.user = decoded;
      req.client_id = decoded.client_id;
      req.user_id = decoded.user_id;
      
      next();
    } catch (error) {
      return res.status(401).json({
        status: 401,
        message: 'Invalid or expired token'
      });
    }
  }

  static requireAdmin(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    if (!req.user) {
      return res.status(401).json({
        status: 401,
        message: 'Authentication required'
      });
    }

    if (req.user.role !== 'admin' && req.user.user_type !== 'admin') {
      return res.status(403).json({
        status: 403,
        message: 'Admin access required'
      });
    }

    next();
  }

  static rateLimiter(windowMs: number = 15 * 60 * 1000, max: number = 100) {
    const requests = new Map();

    return (req: Request, res: Response, next: NextFunction) => {
      const ip = req.ip;
      const now = Date.now();
      const windowStart = now - windowMs;

      if (!requests.has(ip)) {
        requests.set(ip, []);
      }

      const userRequests = requests.get(ip);
      const recentRequests = userRequests.filter((time: number) => time > windowStart);
      
      if (recentRequests.length >= max) {
        return res.status(429).json({
          status: 429,
          message: 'Too many requests, please try again later'
        });
      }

      recentRequests.push(now);
      requests.set(ip, recentRequests);
      
      next();
    };
  }
} 
import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import expressFileUpload from 'express-fileupload';
import stellarListner from './helpers/stellar.listner';
import account from './controllers/accounts'; 
import admin from './controllers/admin'; 
import CronService from './helpers/cron';

import transactions from './controllers/transactions';  
import Test from './tests/index'
new Test()
 

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3005; // Default to 3000 if PORT is not in environment

app.use(cors());
app.use(expressFileUpload()); // Use express-fileupload before body-parser
app.use(bodyParser.json());
app.use(express.json({ limit: '50mb' })); // Increase JSON payload limit to 50 MB
app.use(express.urlencoded({ limit: '50mb', extended: true })); // Increase URL-encoded payload limit


new CronService()
new stellarListner()

// Using the routes
app.use('/clients', account);
app.use('/payment', transactions);
app.use('/admin', admin);

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

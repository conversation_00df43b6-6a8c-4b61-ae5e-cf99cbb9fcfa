
export interface CallbackData {
    type: "transaction_status" | "payment_confirmation" | "wallet_update"; // Callback Type
    timestamp: string; // ISO Timestamp
    reference_id: string; // Unique reference ID for tracking
    status: "PENDING" | "SUCCESS" | "FAILED"; // Transaction or payment status
    amount?: string; // Optional: Transaction amount
    currency?: string; // Optional: Currency or token code (e.g., "UGX")
    sender_account?: string; // Optional: Public Key or Account ID of sender
    receiver_account?: string; // Optional: Public Key or Account ID of receiver
    transaction_id?: string; // Optional: Stellar or Internal Transaction ID
     meta?: string; // Optional: Any additional notes
     client_id:string
}

/**
 * Simple logger utility for consistent logging across the application
 */

const logger = {
  /**
   * Log informational message
   * @param message - The message to log
   * @param data - Optional data to include with the log
   */
  info: (message: string, data: any = {}) => {
    console.log(`ℹ️ INFO: ${message}`, data);
  },

  /**
   * Log warning message
   * @param message - The message to log
   * @param data - Optional data to include with the log
   */
  warn: (message: string, data: any = {}) => {
    console.warn(`⚠️ WARNING: ${message}`, data);
  },

  /**
   * Log error message
   * @param message - The message to log
   * @param data - Optional data to include with the log
   */
  error: (message: string, data: any = {}) => {
    console.error(`❌ ERROR: ${message}`, data);
  },

  /**
   * Log debug message (only in development)
   * @param message - The message to log
   * @param data - Optional data to include with the log
   */
  debug: (message: string, data: any = {}) => {
    if (process.env.NODE_ENV !== 'production') {
      console.debug(`🔍 DEBUG: ${message}`, data);
    }
  }
};

export default logger; 
import { Account } from "stellar-sdk";
import Accounts from "../models/accounts";
import PegPay from '../intergrations/PegPay'

export default class Test {
    constructor() {
        const username = 'iranks20'
        this.main()
    }
    public  async main(): Promise<void> {
     //   const payment =  await new PegPay().validatePhoneNumber("+************")
       // console.log('Hello World',payment)
     //   new Accounts().addCurrencies();
    }
 
}



{"name": "ema", "version": "1.0.0", "description": "", "main": "app.ts", "scripts": {"start": "ts-node src/app.ts", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --exec ts-node src/app.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.1536.0", "axios": "^1.6.3", "bcryptjs": "^3.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-ws": "^5.0.2", "firebase-admin": "^12.5.0", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mysql2": "^2.2.5", "node-cron": "^3.0.3", "node-rsa": "^1.1.1", "nodemailer": "^6.9.15", "request": "^2.88.2", "sequelize": "^6.35.2", "stellar-sdk": "^7.0.0", "uuid": "^9.0.1", "xml2json": "^0.12.0", "zod": "^3.24.2"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.4", "@types/express-ws": "^3.0.5", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-rsa": "^1.1.4", "@types/nodemailer": "^6.4.14", "@types/request": "^2.48.12", "@types/sequelize": "^4.28.19", "@types/uuid": "^9.0.7", "@types/xml2json": "^0.11.6", "i": "^0.3.7", "nodemon": "^3.0.2", "npm": "^10.4.0", "redis": "^4.6.11", "ts-node": "^10.9.2", "typescript": "^5.3.3", "@types/node-cron": "^3.0.11"}}
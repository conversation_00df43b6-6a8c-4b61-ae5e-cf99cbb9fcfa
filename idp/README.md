# Muda Payments API

This repository contains the Muda Payments API, a robust payment processing system that allows businesses to process mobile money transactions, manage accounts, and handle various payment operations.

## API Organization

The API is organized into two main categories:

1. **Public Endpoints**: These endpoints are safe to share with third-party integrators and are documented in `API_DOCUMENTATION.md`.
2. **Admin Endpoints**: These endpoints are further divided into:
   - **Public Admin Endpoints**: Can be shared with trusted third parties who need limited administrative capabilities.
   - **Private Admin Endpoints**: For internal use only, should never be shared with third parties.
   
Admin endpoints are documented in `ADMIN_API_DOCUMENTATION.md`.

## Documentation Files

- **[API_DOCUMENTATION.md](./API_DOCUMENTATION.md)**: Contains documentation for all public-facing endpoints that can be shared with third-party integrators.
- **[ADMIN_API_DOCUMENTATION.md](./ADMIN_API_DOCUMENTATION.md)**: Contains documentation for admin endpoints, clearly indicating which are public (can be shared with trusted third parties) and which are private (for internal use only).

## Project Structure

```
muda/aggregator/paments-api/
├── src/
│   ├── controllers/
│   │   ├── admin.ts           # Admin controller with public and private endpoints
│   │   ├── transactions.ts    # Transactions controller with public endpoints
│   │   └── ...
│   ├── helpers/
│   │   ├── cron.ts            # Cron service for scheduled tasks
│   │   └── ...
│   ├── models/
│   │   └── ...
│   ├── routes/
│   │   └── ...
│   └── index.ts
├── .env
├── package.json
├── API_DOCUMENTATION.md
├── ADMIN_API_DOCUMENTATION.md
└── README.md
```

## Controller Organization

### Transactions Controller (`src/controllers/transactions.ts`)

The transactions controller is organized with clear comments indicating which endpoints are public and which are private:

```typescript
// =============================================
// PUBLIC ENDPOINTS - Safe to share with third-party integrators
// =============================================

// Endpoint implementations...

// =============================================
// PRIVATE ENDPOINTS - For admin use only, not to be shared
// =============================================

// Endpoint implementations...
```

### Admin Controller (`src/controllers/admin.ts`)

The admin controller is similarly organized:

```typescript
// =============================================
// PUBLIC ADMIN ENDPOINTS - Can be shared with trusted third parties
// =============================================

// Endpoint implementations...

// =============================================
// PRIVATE ADMIN ENDPOINTS - For internal use only, never share
// =============================================

// Endpoint implementations...
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables (copy `.env.example` to `.env` and fill in the values)
4. Start the development server:
   ```
   npm run dev
   ```

## Development Guidelines

When adding new endpoints, please follow these guidelines:

1. Clearly categorize your endpoint as either public or private in the controller file using the comment structure shown above.
2. Update the appropriate documentation file based on the endpoint's category.
3. Include comprehensive JSDoc comments for all controller methods.
4. Ensure proper input validation and error handling.
5. Write tests for all new endpoints.

## Security Considerations

- All endpoints (except login) require JWT authentication
- Public endpoints have limited access to sensitive data
- Admin endpoints require additional permissions
- Rate limiting is implemented to prevent abuse
- Input validation is performed on all endpoints
- All API requests are logged for audit purposes

## Scheduled Tasks

The application includes a cron service (`src/helpers/cron.ts`) that handles scheduled tasks such as:

- Processing offline transactions
- Checking pending transactions
- Daily summary tasks
- Cache refreshing

## Support

For API support, please contact:
- Email: <EMAIL>
- Phone: +256 XXX XXX XXX 
{"name": "aggregator-backend-monorepo", "version": "1.0.0", "description": "Aggregator backend monorepo with shared tooling", "private": true, "workspaces": ["wallet", "admin", "gateway", "liquidityRailAdmin", "stellarService", "idp"], "scripts": {"prepare": "husky install", "test": "npm run test:all-services", "test:all-services": "npm run test:wallet && npm run test:admin && npm run test:gateway", "test:wallet": "cd wallet && npm test", "test:admin": "cd admin && npm test || echo 'No tests configured for admin'", "test:gateway": "cd gateway && npm test || echo 'No tests configured for gateway'", "typecheck": "npm run typecheck:all-services", "typecheck:all-services": "npm run typecheck:wallet && npm run typecheck:admin && npm run typecheck:gateway && npm run typecheck:liquidityRailAdmin && npm run typecheck:stellarService && npm run typecheck:idp", "typecheck:wallet": "cd wallet && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for wallet'", "typecheck:admin": "cd admin && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for admin'", "typecheck:gateway": "cd gateway && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for gateway'", "typecheck:liquidityRailAdmin": "cd liquidityRailAdmin && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for liquidityRailAdmin'", "typecheck:stellarService": "cd stellarService && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for stellarService'", "typecheck:idp": "cd idp && npx tsc --noEmit --skipLibCheck || echo 'TypeScript check failed for idp'", "lint": "echo '<PERSON><PERSON> not configured yet'", "install:all": "npm install && npm run install:wallet && npm run install:admin && npm run install:gateway && npm run install:liquidityRailAdmin && npm run install:stellarService && npm run install:idp", "install:wallet": "cd wallet && npm install", "install:admin": "cd admin && npm install", "install:gateway": "cd gateway && npm install", "install:liquidityRailAdmin": "cd liquidityRailAdmin && npm install", "install:stellarService": "cd stellarService && npm install", "install:idp": "cd idp && npm install"}, "devDependencies": {"husky": "^8.0.3", "lint-staged": "^15.2.0"}, "lint-staged": {"*.{ts,tsx}": ["echo 'Checking staged TypeScript files...'"]}}
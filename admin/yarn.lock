# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz"
  integrity sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@fastify/busboy@^3.0.0":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@fastify/busboy/-/busboy-3.1.1.tgz"
  integrity sha512-5DGmA8FTdB2XbDeEwc/5ZXBl6UbBAyBOOLlPuBnZ/N1SwdH9Ii+cOX3tBROlDgcTXxjOYnLMVoKk9+FXAw0CJw==

"@firebase/app-check-interop-types@0.3.2":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@firebase/app-check-interop-types/-/app-check-interop-types-0.3.2.tgz"
  integrity sha512-LMs47Vinv2HBMZi49C09dJxp0QT5LwDzFaVGf/+ITHe3BlIhUiLNttkATSXplc89A2lAaeTqjgqVkiRfUGyQiQ==

"@firebase/app-types@0.9.2":
  version "0.9.2"
  resolved "https://registry.npmjs.org/@firebase/app-types/-/app-types-0.9.2.tgz"
  integrity sha512-oMEZ1TDlBz479lmABwWsWjzHwheQKiAgnuKxE0pz0IXCVx7/rtlkx1fQ6GfgK24WCrxDKMplZrT50Kh04iMbXQ==

"@firebase/auth-interop-types@0.2.3":
  version "0.2.3"
  resolved "https://registry.npmjs.org/@firebase/auth-interop-types/-/auth-interop-types-0.2.3.tgz"
  integrity sha512-Fc9wuJGgxoxQeavybiuwgyi+0rssr76b+nHpj+eGhXFYAdudMWyfBHvFL/I5fEHniUM/UQdFzi9VXJK2iZF7FQ==

"@firebase/component@0.6.9":
  version "0.6.9"
  resolved "https://registry.npmjs.org/@firebase/component/-/component-0.6.9.tgz"
  integrity sha512-gm8EUEJE/fEac86AvHn8Z/QW8BvR56TBw3hMW0O838J/1mThYQXAIQBgUv75EqlCZfdawpWLrKt1uXvp9ciK3Q==
  dependencies:
    "@firebase/util" "1.10.0"
    tslib "^2.1.0"

"@firebase/database-compat@1.0.8":
  version "1.0.8"
  resolved "https://registry.npmjs.org/@firebase/database-compat/-/database-compat-1.0.8.tgz"
  integrity sha512-OpeWZoPE3sGIRPBKYnW9wLad25RaWbGyk7fFQe4xnJQKRzlynWeFBSRRAoLE2Old01WXwskUiucNqUUVlFsceg==
  dependencies:
    "@firebase/component" "0.6.9"
    "@firebase/database" "1.0.8"
    "@firebase/database-types" "1.0.5"
    "@firebase/logger" "0.4.2"
    "@firebase/util" "1.10.0"
    tslib "^2.1.0"

"@firebase/database-types@1.0.5":
  version "1.0.5"
  resolved "https://registry.npmjs.org/@firebase/database-types/-/database-types-1.0.5.tgz"
  integrity sha512-fTlqCNwFYyq/C6W7AJ5OCuq5CeZuBEsEwptnVxlNPkWCo5cTTyukzAHRSO/jaQcItz33FfYrrFk1SJofcu2AaQ==
  dependencies:
    "@firebase/app-types" "0.9.2"
    "@firebase/util" "1.10.0"

"@firebase/database@1.0.8":
  version "1.0.8"
  resolved "https://registry.npmjs.org/@firebase/database/-/database-1.0.8.tgz"
  integrity sha512-dzXALZeBI1U5TXt6619cv0+tgEhJiwlUtQ55WNZY7vGAjv7Q1QioV969iYwt1AQQ0ovHnEW0YW9TiBfefLvErg==
  dependencies:
    "@firebase/app-check-interop-types" "0.3.2"
    "@firebase/auth-interop-types" "0.2.3"
    "@firebase/component" "0.6.9"
    "@firebase/logger" "0.4.2"
    "@firebase/util" "1.10.0"
    faye-websocket "0.11.4"
    tslib "^2.1.0"

"@firebase/logger@0.4.2":
  version "0.4.2"
  resolved "https://registry.npmjs.org/@firebase/logger/-/logger-0.4.2.tgz"
  integrity sha512-Q1VuA5M1Gjqrwom6I6NUU4lQXdo9IAQieXlujeHZWvRt1b7qQ0KwBaNAjgxG27jgF9/mUwsNmO8ptBCGVYhB0A==
  dependencies:
    tslib "^2.1.0"

"@firebase/util@1.10.0":
  version "1.10.0"
  resolved "https://registry.npmjs.org/@firebase/util/-/util-1.10.0.tgz"
  integrity sha512-xKtx4A668icQqoANRxyDLBLz51TAbDP9KRfpbKGxiCAW346d0BeJe5vN6/hKxxmWwnZ0mautyv39JxviwwQMOQ==
  dependencies:
    tslib "^2.1.0"

"@google-cloud/firestore@^7.7.0":
  version "7.11.0"
  resolved "https://registry.npmjs.org/@google-cloud/firestore/-/firestore-7.11.0.tgz"
  integrity sha512-88uZ+jLsp1aVMj7gh3EKYH1aulTAMFAp8sH/v5a9w8q8iqSG27RiWLoxSAFr/XocZ9hGiWH1kEnBw+zl3xAgNA==
  dependencies:
    "@opentelemetry/api" "^1.3.0"
    fast-deep-equal "^3.1.1"
    functional-red-black-tree "^1.0.1"
    google-gax "^4.3.3"
    protobufjs "^7.2.6"

"@google-cloud/paginator@^5.0.0":
  version "5.0.2"
  resolved "https://registry.npmjs.org/@google-cloud/paginator/-/paginator-5.0.2.tgz"
  integrity sha512-DJS3s0OVH4zFDB1PzjxAsHqJT6sKVbRwwML0ZBP9PbU7Yebtu/7SWMRzvO2J3nUi9pRNITCfu4LJeooM2w4pjg==
  dependencies:
    arrify "^2.0.0"
    extend "^3.0.2"

"@google-cloud/projectify@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@google-cloud/projectify/-/projectify-4.0.0.tgz"
  integrity sha512-MmaX6HeSvyPbWGwFq7mXdo0uQZLGBYCwziiLIGq5JVX+/bdI3SAq6bP98trV5eTWfLuvsMcIC1YJOF2vfteLFA==

"@google-cloud/promisify@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@google-cloud/promisify/-/promisify-4.0.0.tgz"
  integrity sha512-Orxzlfb9c67A15cq2JQEyVc7wEsmFBmHjZWZYQMUyJ1qivXyMwdyNOs9odi79hze+2zqdTtu1E19IM/FtqZ10g==

"@google-cloud/storage@^7.7.0":
  version "7.15.0"
  resolved "https://registry.npmjs.org/@google-cloud/storage/-/storage-7.15.0.tgz"
  integrity sha512-/j/+8DFuEOo33fbdX0V5wjooOoFahEaMEdImHBmM2tH9MPHJYNtmXOf2sGUmZmiufSukmBEvdlzYgDkkgeBiVQ==
  dependencies:
    "@google-cloud/paginator" "^5.0.0"
    "@google-cloud/projectify" "^4.0.0"
    "@google-cloud/promisify" "^4.0.0"
    abort-controller "^3.0.0"
    async-retry "^1.3.3"
    duplexify "^4.1.3"
    fast-xml-parser "^4.4.1"
    gaxios "^6.0.2"
    google-auth-library "^9.6.3"
    html-entities "^2.5.2"
    mime "^3.0.0"
    p-limit "^3.0.1"
    retry-request "^7.0.0"
    teeny-request "^9.0.0"
    uuid "^8.0.0"

"@grpc/grpc-js@^1.10.9":
  version "1.12.5"
  resolved "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.12.5.tgz"
  integrity sha512-d3iiHxdpg5+ZcJ6jnDSOT8Z0O0VMVGy34jAnYLUX8yd36b1qn8f1TwOA/Lc7TsOh03IkPJ38eGI5qD2EjNkoEA==
  dependencies:
    "@grpc/proto-loader" "^0.7.13"
    "@js-sdsl/ordered-map" "^4.4.2"

"@grpc/proto-loader@^0.7.13":
  version "0.7.13"
  resolved "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.13.tgz"
  integrity sha512-AiXO/bfe9bmxBjxxtYxFAXGZvMaN5s8kO+jBHAJCON8rJoB5YS/D6X7ZNc6XQkuHNmyl4CYaMI1fJ/Gn27RGGw==
  dependencies:
    lodash.camelcase "^4.3.0"
    long "^5.0.0"
    protobufjs "^7.2.5"
    yargs "^17.7.2"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@isaacs/string-locale-compare@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@isaacs/string-locale-compare/-/string-locale-compare-1.1.0.tgz"
  integrity sha512-SQ7Kzhh9+D+ZW9MA0zkYv3VXhIDNx+LzM6EJ+/65I3QY+enU6Itte7E5XX7EWrqLW2FN4n06GWzBnPoC3th2aQ==

"@jridgewell/resolve-uri@^3.0.3":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz"
  integrity sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@js-sdsl/ordered-map@^4.4.2":
  version "4.4.2"
  resolved "https://registry.npmjs.org/@js-sdsl/ordered-map/-/ordered-map-4.4.2.tgz"
  integrity sha512-iUKgm52T8HOE/makSxjqoWhe95ZJA1/G1sYsGev2JDKUSS14KAgg1LHb+Ba+IPow0xflbnSkOsZcO08C7w1gYw==

"@npmcli/agent@^2.0.0":
  version "2.2.0"
  dependencies:
    agent-base "^7.1.0"
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.1"
    lru-cache "^10.0.1"
    socks-proxy-agent "^8.0.1"

"@npmcli/arborist@^7.2.1":
  version "7.3.1"
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/fs" "^3.1.0"
    "@npmcli/installed-package-contents" "^2.0.2"
    "@npmcli/map-workspaces" "^3.0.2"
    "@npmcli/metavuln-calculator" "^7.0.0"
    "@npmcli/name-from-folder" "^2.0.0"
    "@npmcli/node-gyp" "^3.0.0"
    "@npmcli/package-json" "^5.0.0"
    "@npmcli/query" "^3.0.1"
    "@npmcli/run-script" "^7.0.2"
    bin-links "^4.0.1"
    cacache "^18.0.0"
    common-ancestor-path "^1.0.1"
    hosted-git-info "^7.0.1"
    json-parse-even-better-errors "^3.0.0"
    json-stringify-nice "^1.1.4"
    minimatch "^9.0.0"
    nopt "^7.0.0"
    npm-install-checks "^6.2.0"
    npm-package-arg "^11.0.1"
    npm-pick-manifest "^9.0.0"
    npm-registry-fetch "^16.0.0"
    npmlog "^7.0.1"
    pacote "^17.0.4"
    parse-conflict-json "^3.0.0"
    proc-log "^3.0.0"
    promise-all-reject-late "^1.0.0"
    promise-call-limit "^3.0.1"
    read-package-json-fast "^3.0.2"
    semver "^7.3.7"
    ssri "^10.0.5"
    treeverse "^3.0.0"
    walk-up-path "^3.0.1"

"@npmcli/config@^8.0.2":
  version "8.1.0"
  dependencies:
    "@npmcli/map-workspaces" "^3.0.2"
    ci-info "^4.0.0"
    ini "^4.1.0"
    nopt "^7.0.0"
    proc-log "^3.0.0"
    read-package-json-fast "^3.0.2"
    semver "^7.3.5"
    walk-up-path "^3.0.1"

"@npmcli/disparity-colors@^3.0.0":
  version "3.0.0"
  dependencies:
    ansi-styles "^4.3.0"

"@npmcli/fs@^3.1.0":
  version "3.1.0"
  dependencies:
    semver "^7.3.5"

"@npmcli/git@^5.0.0", "@npmcli/git@^5.0.3":
  version "5.0.4"
  dependencies:
    "@npmcli/promise-spawn" "^7.0.0"
    lru-cache "^10.0.1"
    npm-pick-manifest "^9.0.0"
    proc-log "^3.0.0"
    promise-inflight "^1.0.1"
    promise-retry "^2.0.1"
    semver "^7.3.5"
    which "^4.0.0"

"@npmcli/installed-package-contents@^2.0.1", "@npmcli/installed-package-contents@^2.0.2":
  version "2.0.2"
  dependencies:
    npm-bundled "^3.0.0"
    npm-normalize-package-bin "^3.0.0"

"@npmcli/map-workspaces@^3.0.2", "@npmcli/map-workspaces@^3.0.4":
  version "3.0.4"
  dependencies:
    "@npmcli/name-from-folder" "^2.0.0"
    glob "^10.2.2"
    minimatch "^9.0.0"
    read-package-json-fast "^3.0.0"

"@npmcli/metavuln-calculator@^7.0.0":
  version "7.0.0"
  dependencies:
    cacache "^18.0.0"
    json-parse-even-better-errors "^3.0.0"
    pacote "^17.0.0"
    semver "^7.3.5"

"@npmcli/name-from-folder@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@npmcli/name-from-folder/-/name-from-folder-2.0.0.tgz"
  integrity sha512-pwK+BfEBZJbKdNYpHHRTNBwBoqrN/iIMO0AiGvYsp3Hoaq0WbgGSWQR6SCldZovoDpY3yje5lkFUe6gsDgJ2vg==

"@npmcli/node-gyp@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@npmcli/node-gyp/-/node-gyp-3.0.0.tgz"
  integrity sha512-gp8pRXC2oOxu0DUE1/M3bYtb1b3/DbJ5aM113+XJBgfXdussRAsX0YOrOhdd8WvnAR6auDBvJomGAkLKA5ydxA==

"@npmcli/package-json@^5.0.0":
  version "5.0.0"
  dependencies:
    "@npmcli/git" "^5.0.0"
    glob "^10.2.2"
    hosted-git-info "^7.0.0"
    json-parse-even-better-errors "^3.0.0"
    normalize-package-data "^6.0.0"
    proc-log "^3.0.0"
    semver "^7.5.3"

"@npmcli/promise-spawn@^7.0.0", "@npmcli/promise-spawn@^7.0.1":
  version "7.0.1"
  dependencies:
    which "^4.0.0"

"@npmcli/query@^3.0.1":
  version "3.0.1"
  dependencies:
    postcss-selector-parser "^6.0.10"

"@npmcli/run-script@^7.0.0", "@npmcli/run-script@^7.0.2", "@npmcli/run-script@^7.0.4":
  version "7.0.4"
  resolved "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.4.tgz"
  integrity sha512-9ApYM/3+rBt9V80aYg6tZfzj3UWdiYyCt7gJUD1VJKvWF5nwKDSICXbYIQbspFTq6TOpbsEtIC0LArB8d9PFmg==
  dependencies:
    "@npmcli/node-gyp" "^3.0.0"
    "@npmcli/package-json" "^5.0.0"
    "@npmcli/promise-spawn" "^7.0.0"
    node-gyp "^10.0.0"
    which "^4.0.0"

"@opentelemetry/api@^1.3.0":
  version "1.9.0"
  resolved "https://registry.npmjs.org/@opentelemetry/api/-/api-1.9.0.tgz"
  integrity sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz"
  integrity sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz"
  integrity sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz"
  integrity sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz"
  integrity sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz"
  integrity sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz"
  integrity sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz"
  integrity sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz"
  integrity sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz"
  integrity sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz"
  integrity sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==

"@redis/bloom@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@redis/bloom/-/bloom-1.2.0.tgz"
  integrity sha512-HG2DFjYKbpNmVXsa0keLHp/3leGJz1mjh09f2RLGGLQZzSHpkmZWuwJbAvo3QcRY8p80m5+ZdXZdYOSBLlp7Cg==

"@redis/client@^1.0.0", "@redis/client@1.6.0":
  version "1.6.0"
  resolved "https://registry.npmjs.org/@redis/client/-/client-1.6.0.tgz"
  integrity sha512-aR0uffYI700OEEH4gYnitAnv3vzVGXCFvYfdpu/CJKvk4pHfLPEy/JSZyrpQ+15WhXe1yJRXLtfQ84s4mEXnPg==
  dependencies:
    cluster-key-slot "1.1.2"
    generic-pool "3.9.0"
    yallist "4.0.0"

"@redis/graph@1.1.1":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@redis/graph/-/graph-1.1.1.tgz"
  integrity sha512-FEMTcTHZozZciLRl6GiiIB4zGm5z5F3F6a6FZCyrfxdKOhFlGkiAqlexWMBzCi4DcRoyiOsuLfW+cjlGWyExOw==

"@redis/json@1.0.7":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@redis/json/-/json-1.0.7.tgz"
  integrity sha512-6UyXfjVaTBTJtKNG4/9Z8PSpKE6XgSyEb8iwaqDcy+uKrd/DGYHTWkUdnQDyzm727V7p21WUMhsqz5oy65kPcQ==

"@redis/search@1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@redis/search/-/search-1.2.0.tgz"
  integrity sha512-tYoDBbtqOVigEDMAcTGsRlMycIIjwMCgD8eR2t0NANeQmgK/lvxNAvYyb6bZDD4frHRhIHkJu2TBRvB0ERkOmw==

"@redis/time-series@1.1.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@redis/time-series/-/time-series-1.1.0.tgz"
  integrity sha512-c1Q99M5ljsIuc4YdaCwfUEXsofakb9c8+Zse2qxTadu8TalLXuAESzLvFAvNVbkmSlvlzIQOLpBCmWI9wTOt+g==

"@sigstore/bundle@^2.1.1":
  version "2.1.1"
  dependencies:
    "@sigstore/protobuf-specs" "^0.2.1"

"@sigstore/core@^0.2.0":
  version "0.2.0"

"@sigstore/protobuf-specs@^0.2.1":
  version "0.2.1"

"@sigstore/sign@^2.2.1":
  version "2.2.1"
  dependencies:
    "@sigstore/bundle" "^2.1.1"
    "@sigstore/core" "^0.2.0"
    "@sigstore/protobuf-specs" "^0.2.1"
    make-fetch-happen "^13.0.0"

"@sigstore/tuf@^2.3.0":
  version "2.3.0"
  dependencies:
    "@sigstore/protobuf-specs" "^0.2.1"
    tuf-js "^2.2.0"

"@sigstore/verify@^0.1.0":
  version "0.1.0"
  dependencies:
    "@sigstore/bundle" "^2.1.1"
    "@sigstore/core" "^0.2.0"
    "@sigstore/protobuf-specs" "^0.2.1"

"@tootallnate/once@2":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz"
  integrity sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.9.tgz"
  integrity sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==

"@tufjs/canonical-json@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@tufjs/canonical-json/-/canonical-json-2.0.0.tgz"
  integrity sha512-yVtV8zsdo8qFHe+/3kw81dSLyF7D576A5cCFCi4X7B39tWT7SekaEFUnvnWJHz+9qO7qJTah1JbrDjWKqFtdWA==

"@tufjs/models@2.0.0":
  version "2.0.0"
  dependencies:
    "@tufjs/canonical-json" "2.0.0"
    minimatch "^9.0.3"

"@types/bluebird@*":
  version "3.5.42"
  resolved "https://registry.npmjs.org/@types/bluebird/-/bluebird-3.5.42.tgz"
  integrity sha512-Jhy+MWRlro6UjVi578V/4ZGNfeCOcNCp0YaFNIUGFKlImowqwb1O/22wDVk3FDGMLqxdpOV3qQHD5fPEH4hK6A==

"@types/body-parser@*", "@types/body-parser@^1.19.5":
  version "1.19.5"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/busboy@*":
  version "1.5.3"
  resolved "https://registry.npmjs.org/@types/busboy/-/busboy-1.5.3.tgz"
  integrity sha512-YMBLFN/xBD8bnqywIlGyYqsNFXu6bsiY7h3Ae0kO17qEuTjsqeyYMRPSUDacIKIquws2Y6KjmxAyNx8xB3xQbw==
  dependencies:
    "@types/node" "*"

"@types/caseless@*":
  version "0.12.5"
  resolved "https://registry.npmjs.org/@types/caseless/-/caseless-0.12.5.tgz"
  integrity sha512-hWtVTC2q7hc7xZ/RLbxapMvDMgUnDvKvMOpKal4DrMyfGBUfB1oKaZlIRr6mJL+If3bAP6sV/QneGzF6tJjZDg==

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/continuation-local-storage@*":
  version "3.2.7"
  resolved "https://registry.npmjs.org/@types/continuation-local-storage/-/continuation-local-storage-3.2.7.tgz"
  integrity sha512-Q7dPOymVpRG5Zpz90/o26+OAqOG2Sw+FED7uQmTrJNCF/JAPTylclZofMxZKd6W7g1BDPmT9/C/jX0ZcSNTQwQ==
  dependencies:
    "@types/node" "*"

"@types/cors@^2.8.17":
  version "2.8.17"
  resolved "https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz"
  integrity sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==
  dependencies:
    "@types/node" "*"

"@types/crypto-js@^4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@types/crypto-js/-/crypto-js-4.2.2.tgz"
  integrity sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==

"@types/debug@^4.1.8":
  version "4.1.12"
  resolved "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz"
  integrity sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==
  dependencies:
    "@types/ms" "*"

"@types/eventsource@^1.1.2":
  version "1.1.15"
  resolved "https://registry.npmjs.org/@types/eventsource/-/eventsource-1.1.15.tgz"
  integrity sha512-XQmGcbnxUNa06HR3VBVkc9+A2Vpi9ZyLJcdS5dwaQQ/4ZMWFO+5c90FnMUpbtMZwB/FChoYHwuVg8TvkECacTA==

"@types/express-fileupload@^1.4.4":
  version "1.4.4"
  resolved "https://registry.npmjs.org/@types/express-fileupload/-/express-fileupload-1.4.4.tgz"
  integrity sha512-kxCs5oJ40JPhvh3LpxCeGfuSZIl8/6bk85u1YqNcIbfQCmUm3u+Ao1oOiSt/VdbEPs+V3JQg8giqxAyqXlpbWg==
  dependencies:
    "@types/busboy" "*"
    "@types/express" "*"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  version "4.17.41"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.41.tgz"
  integrity sha512-OaJ7XLaelTgrvlZD8/aa0vvvxZdUmlCn6MtWeB7TkiKW70BQLc9XEPpDLPdbo52ZhXUCrznlWdCHWxJWtdyajA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-ws@^3.0.5":
  version "3.0.5"
  resolved "https://registry.npmjs.org/@types/express-ws/-/express-ws-3.0.5.tgz"
  integrity sha512-lbWMjoHrm/v85j81UCmb/GNZFO3genxRYBW1Ob7rjRI+zxUBR+4tcFuOpKKsYQ1LYTYiy3356epLeYi/5zxUwA==
  dependencies:
    "@types/express" "*"
    "@types/express-serve-static-core" "*"
    "@types/ws" "*"

"@types/express@*", "@types/express@^4.17.17", "@types/express@^4.17.21":
  version "4.17.21"
  resolved "https://registry.npmjs.org/@types/express/-/express-4.17.21.tgz"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/jsonwebtoken@^9.0.2", "@types/jsonwebtoken@^9.0.9":
  version "9.0.9"
  resolved "https://registry.npmjs.org/@types/jsonwebtoken/-/jsonwebtoken-9.0.9.tgz"
  integrity sha512-uoe+GxEuHbvy12OUQct2X9JenKM3qAscquYymuQN4fMWG9DBQtykrQEFcAbVACF7qaLw9BePSodUL0kquqBJpQ==
  dependencies:
    "@types/ms" "*"
    "@types/node" "*"

"@types/lodash@*":
  version "4.14.202"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.14.202.tgz"
  integrity sha512-OvlIYQK9tNneDlS0VN54LLd5uiPCBOp7gS5Z0f1mjoJYBrtStzgmJBxONW3U6OZqdtNzZPmn9BS/7WI7BFFcFQ==

"@types/long@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz"
  integrity sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==

"@types/mime@*", "@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/ms@*":
  version "0.7.34"
  resolved "https://registry.npmjs.org/@types/ms/-/ms-0.7.34.tgz"
  integrity sha512-nG96G3Wp6acyAgJqGasjODb+acrI7KltPiRxzHPXnP3NgI28bpQDRv53olbqGXbfcgF5aiiHmO3xpwEpS5Ld9g==

"@types/node-cron@^3.0.11":
  version "3.0.11"
  resolved "https://registry.npmjs.org/@types/node-cron/-/node-cron-3.0.11.tgz"
  integrity sha512-0ikrnug3/IyneSHqCBeslAhlK2aBfYek1fGo4bP4QnZPmiqSGRK+Oy7ZMisLWkesffJvQ1cqAcBnJC+8+nxIAg==

"@types/node-rsa@^1.1.4":
  version "1.1.4"
  resolved "https://registry.npmjs.org/@types/node-rsa/-/node-rsa-1.1.4.tgz"
  integrity sha512-dB0ECel6JpMnq5ULvpUTunx3yNm8e/dIkv8Zu9p2c8me70xIRUUG3q+qXRwcSf9rN3oqamv4116iHy90dJGRpA==
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@^20.19.9", "@types/node@>= 8", "@types/node@>=13.7.0":
  version "20.19.9"
  resolved "https://registry.npmjs.org/@types/node/-/node-20.19.9.tgz"
  integrity sha512-cuVNgarYWZqxRJDQHEB58GEONhOK79QVR/qYx4S7kcUObQvUwvFnYxJuuHUKm2aieN9X3yZB4LZsuYNU1Qphsw==
  dependencies:
    undici-types "~6.21.0"

"@types/node@^22.0.1":
  version "22.10.10"
  resolved "https://registry.npmjs.org/@types/node/-/node-22.10.10.tgz"
  integrity sha512-X47y/mPNzxviAGY5TcYPtYL8JsY3kAq2n8fMmKoRCxq/c4v4pyGNCzM2R6+M5/umG4ZfHuT+sgqDYqWc9rJ6ww==
  dependencies:
    undici-types "~6.20.0"

"@types/nodemailer@^6.4.14":
  version "6.4.14"
  resolved "https://registry.npmjs.org/@types/nodemailer/-/nodemailer-6.4.14.tgz"
  integrity sha512-fUWthHO9k9DSdPCSPRqcu6TWhYyxTBg382vlNIttSe9M7XfsT06y0f24KHXtbnijPGGRIcVvdKHTNikOI6qiHA==
  dependencies:
    "@types/node" "*"

"@types/qrcode@^1.5.5":
  version "1.5.5"
  resolved "https://registry.npmjs.org/@types/qrcode/-/qrcode-1.5.5.tgz"
  integrity sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg==
  dependencies:
    "@types/node" "*"

"@types/qs@*":
  version "6.9.11"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.11.tgz"
  integrity sha512-oGk0gmhnEJK4Yyk+oI7EfXsLayXatCWPHary1MtcmbAifkobT9cM9yutG/hZKIseOU0MqbIwQ/u2nn/Gb+ltuQ==

"@types/randombytes@^2.0.0":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@types/randombytes/-/randombytes-2.0.3.tgz"
  integrity sha512-+NRgihTfuURllWCiIAhm1wsJqzsocnqXM77V/CalsdJIYSRGEHMnritxh+6EsBklshC+clo1KgnN14qgSGeQdw==
  dependencies:
    "@types/node" "*"

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/request@^2.48.12", "@types/request@^2.48.8":
  version "2.48.12"
  resolved "https://registry.npmjs.org/@types/request/-/request-2.48.12.tgz"
  integrity sha512-G3sY+NpsA9jnwm0ixhAFQSJ3Q9JkpLZpJbI3GMv0mIAT0y3mRabYeINzal5WOChIiaTEGQYlHOKgkaM9EisWHw==
  dependencies:
    "@types/caseless" "*"
    "@types/node" "*"
    "@types/tough-cookie" "*"
    form-data "^2.5.0"

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/sequelize@^4.28.19":
  version "4.28.19"
  resolved "https://registry.npmjs.org/@types/sequelize/-/sequelize-4.28.19.tgz"
  integrity sha512-GTPL/0gRWyx44+w6HeJ1WHMWik3Bx+8njr7i2LVnRC24R+ocb4EOiJaVtrLEwu55llFRZ5hoYjyRUjrhvESrng==
  dependencies:
    "@types/bluebird" "*"
    "@types/continuation-local-storage" "*"
    "@types/lodash" "*"
    "@types/validator" "*"

"@types/serve-static@*":
  version "1.15.5"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.5.tgz"
  integrity sha512-PDRk21MnK70hja/YF8AHfC7yIsiQHn1rcXx7ijCFBX/k+XQJhQT/gw3xekXKJvx+5SXaMMS8oqQy09Mzvz2TuQ==
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/speakeasy@^2.0.10":
  version "2.0.10"
  resolved "https://registry.npmjs.org/@types/speakeasy/-/speakeasy-2.0.10.tgz"
  integrity sha512-QVRlDW5r4yl7p7xkNIbAIC/JtyOcClDIIdKfuG7PWdDT1MmyhtXSANsildohy0K+Lmvf/9RUtLbNLMacvrVwxA==
  dependencies:
    "@types/node" "*"

"@types/tough-cookie@*":
  version "4.0.5"
  resolved "https://registry.npmjs.org/@types/tough-cookie/-/tough-cookie-4.0.5.tgz"
  integrity sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==

"@types/urijs@^1.19.6":
  version "1.19.25"
  resolved "https://registry.npmjs.org/@types/urijs/-/urijs-1.19.25.tgz"
  integrity sha512-XOfUup9r3Y06nFAZh3WvO0rBU4OtlfPB/vgxpjg+NRdGU6CN6djdc6OEiH+PcqHCY6eFLo9Ista73uarf4gnBg==

"@types/uuid@^9.0.7":
  version "9.0.7"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-9.0.7.tgz"
  integrity sha512-WUtIVRUZ9i5dYXefDEAI7sh9/O7jGvHg7Df/5O/gtH3Yabe5odI3UWopVR1qbPXQtvOxWu3mM4XxlYeZtMWF4g==

"@types/validator@*", "@types/validator@^13.7.17":
  version "13.11.7"
  resolved "https://registry.npmjs.org/@types/validator/-/validator-13.11.7.tgz"
  integrity sha512-q0JomTsJ2I5Mv7dhHhQLGjMvX0JJm5dyZ1DXQySIUzU1UlwzB8bt+R6+LODUbz0UDIOvEzGc28tk27gBJw2N8Q==

"@types/ws@*":
  version "8.5.14"
  resolved "https://registry.npmjs.org/@types/ws/-/ws-8.5.14.tgz"
  integrity sha512-bd/YFLW+URhBzMXurx7lWByOu+xzU9+kb3RboOteXYDfW+tr+JZa99OyNmPINEGB/ahzKrEuc8rcv4gnpJmxTw==
  dependencies:
    "@types/node" "*"

"@types/xml2json@^0.11.6":
  version "0.11.6"
  resolved "https://registry.npmjs.org/@types/xml2json/-/xml2json-0.11.6.tgz"
  integrity sha512-OYZ5cB4zctk4JdK1MGdLmeBMdmg+/AClCUspQ6KtQ2vZLJkSpm0XWE9H0vCfCGlXvbqn9CBqIda2dXX2KAE/5w==
  dependencies:
    "@types/node" "*"

abbrev@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz"
  integrity sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-walk@^8.1.1:
  version "8.3.1"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.1.tgz"
  integrity sha512-TgUZgYvqZprrl7YldZNoa9OciCAyZR+Ejm9eXzKCmjsF5IKp/wgQ7Z/ZpjpGTIUPwrHQIcYeI8qDh4PsEwxMbw==

acorn@^8.4.1:
  version "8.11.2"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz"
  integrity sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==

agent-base@^7.0.2, agent-base@^7.1.0:
  version "7.1.0"
  dependencies:
    debug "^4.3.4"

agent-base@^7.1.2:
  version "7.1.3"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz"
  integrity sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.0.1"

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^4.3.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

"aproba@^1.0.3 || ^2.0.0", aproba@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

archy@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/archy/-/archy-1.0.0.tgz"
  integrity sha512-Xg+9RwCg/0p32teKdGMPTPnVXKD0w3DfHnFTficozsAgsvq2XenPJq/MYpzzQ/v8zrOyJn6Ds39VA4JIDwFfqw==

are-we-there-yet@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.2.tgz"
  integrity sha512-ncSWAawFhKMJDTdoAeOV+jyW1VCMj5QIAwULIBV0SSR7B/RLPPEQiknKcg/RIIZlUQrxELpsxMiTUoAQ4sIUyg==

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

arrify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/arrify/-/arrify-2.0.1.tgz"
  integrity sha512-3duEwti880xqi4eAMN8AyR4a0ByT90zoYdLlevfrvU43vb0YZwZVfxOgxWrLXXXpyugL0hNZc9G6BiB5B3nUug==

asn1@^0.2.4:
  version "0.2.6"
  resolved "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz"
  integrity sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==
  dependencies:
    safer-buffer "~2.1.0"

async-retry@^1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/async-retry/-/async-retry-1.3.3.tgz"
  integrity sha512-wfr/jstw9xNi/0teMHrRW7dsz3Lt5ARhYNZ2ewpadnhaIp5mbALhOAP+EAdsC7t4Z6wqsDVv9+W6gm1Dk9mEyw==
  dependencies:
    retry "0.13.1"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
  integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==

aws-sdk@^2.1536.0:
  version "2.1536.0"
  resolved "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.1536.0.tgz"
  integrity sha512-Kwl5xKti6qURwKkasZPA9d4q72tcNt0e3ZZ2ikjZvWbfWcIuuLN0GpUTarU8UpV4EiEw3W5z5G3jyHy8JLNyLw==
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.5.0"

axios@^0.19.0:
  version "0.19.2"
  resolved "https://registry.npmjs.org/axios/-/axios-0.19.2.tgz"
  integrity sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==
  dependencies:
    follow-redirects "1.5.10"

axios@^1.11.0:
  version "1.11.0"
  resolved "https://registry.npmjs.org/axios/-/axios-1.11.0.tgz"
  integrity sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.4"
    proxy-from-env "^1.1.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base32.js@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/base32.js/-/base32.js-0.1.0.tgz"
  integrity sha512-n3TkB02ixgBOhTvANakDb4xaMXnYUVkNoRFJjQflcqMQhyEKxEHdj3E6N8t8sUQ0mjH/3/JxzlXuz3ul/J90pQ==

base32.js@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/base32.js/-/base32.js-0.0.1.tgz"
  integrity sha512-EGHIRiegFa62/SsA1J+Xs2tIzludPdzM064N9wjbiEgHnGnJ1V0WEpA4pEwCYT5nDvZk3ubf0shqaCS7k6xeUQ==

base64-js@^1.0.2, base64-js@^1.3.0, base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

bcryptjs@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.0.tgz"
  integrity sha512-Q2vVGpGC7B7m9wggpcA5lq4OYR5OS1nrXoUpnH9MmogXU8HpxzKg63uxtCrLebY5v/y3o0r7JcGCpR/vTGGn7A==

bignumber.js@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-4.1.0.tgz"
  integrity sha512-eJzYkFYy9L4JzXsbymsFn3p54D+llV27oTQ+ziJG7WFRheJcNZilgVXMG0LoZtlQSKBsJdWtLFqOD0u+U0jZKA==

bignumber.js@^9.0.0:
  version "9.1.2"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.1.2.tgz"
  integrity sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug==

bin-links@^4.0.1:
  version "4.0.3"
  dependencies:
    cmd-shim "^6.0.0"
    npm-normalize-package-bin "^3.0.0"
    read-cmd-shim "^4.0.0"
    write-file-atomic "^5.0.0"

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

binary-extensions@^2.2.0:
  version "2.2.0"

bindings@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz"
  integrity sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==
  dependencies:
    file-uri-to-path "1.0.0"

body-parser@^1.20.2:
  version "1.20.2"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz"
  integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@1.20.1:
  version "1.20.1"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz"
  integrity sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==

buffer@^5.1.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@4.9.2:
  version "4.9.2"
  resolved "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz"
  integrity sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtins@^5.0.0:
  version "5.0.1"
  dependencies:
    semver "^7.0.0"

busboy@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cacache@^18.0.0, cacache@^18.0.2:
  version "18.0.2"
  dependencies:
    "@npmcli/fs" "^3.1.0"
    fs-minipass "^3.0.0"
    glob "^10.2.2"
    lru-cache "^10.0.1"
    minipass "^7.0.3"
    minipass-collect "^2.0.1"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    p-map "^4.0.0"
    ssri "^10.0.0"
    tar "^6.1.11"
    unique-filename "^3.0.0"

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  integrity sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.5.tgz"
  integrity sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ==
  dependencies:
    function-bind "^1.1.2"
    get-intrinsic "^1.2.1"
    set-function-length "^1.1.1"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

chalk@^5.3.0:
  version "5.3.0"

chokidar@^3.5.2:
  version "3.5.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

ci-info@^4.0.0:
  version "4.0.0"

cidr-regex@4.0.3:
  version "4.0.3"
  dependencies:
    ip-regex "^5.0.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-columns@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cli-columns/-/cli-columns-4.0.0.tgz"
  integrity sha512-XW2Vg+w+L9on9wtwKpyzluIPCWXjaBahI7mTcYjx+BVIYD9c3yqcv/yKC7CmdCZat4rq2yiE1UMSJC5ivKfMtQ==
  dependencies:
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

cli-table3@^0.6.3:
  version "0.6.3"
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz"
  integrity sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

cluster-key-slot@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/cluster-key-slot/-/cluster-key-slot-1.1.2.tgz"
  integrity sha512-RMr0FhtfXemyinomL4hrWcYJxmX6deFdCxpJzhDttxgO1+bcCnkk+9drydLVDmAMG7NE6aN/fl4F7ucU/90gAA==

cmd-shim@^6.0.0:
  version "6.0.2"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-support@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

colorette@2.0.19:
  version "2.0.19"
  resolved "https://registry.npmjs.org/colorette/-/colorette-2.0.19.tgz"
  integrity sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==

columnify@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/columnify/-/columnify-1.6.0.tgz"
  integrity sha512-lomjuFZKfM6MSAnV9aCZC9sc0qGbmZdfygNv+nCpqVkSKdCxCklLtd16O0EILGkImHw9ZpHkAnHaB+8Zxq5W6Q==
  dependencies:
    strip-ansi "^6.0.1"
    wcwidth "^1.0.0"

combined-stream@^1.0.6, combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^10.0.0:
  version "10.0.1"
  resolved "https://registry.npmjs.org/commander/-/commander-10.0.1.tgz"
  integrity sha512-y4Mg2tXshplEbSGzx7amzPwKKOCGuoSRP/CjEdwwk0FOGlUbq6lKuoyDZTNZkmxHdJtp54hdfY/JUrdL7Xfdug==

common-ancestor-path@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/common-ancestor-path/-/common-ancestor-path-1.0.1.tgz"
  integrity sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

cors@^2.8.5:
  version "2.8.5"
  resolved "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

crc@^3.5.0:
  version "3.8.0"
  resolved "https://registry.npmjs.org/crc/-/crc-3.8.0.tgz"
  integrity sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==
  dependencies:
    buffer "^5.1.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

cross-spawn@^7.0.0:
  version "7.0.3"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/crypto-js/-/crypto-js-4.2.0.tgz"
  integrity sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/*****************************/Vg==

debug@^4:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@^4.3.4:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

debug@=3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz"
  integrity sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==
  dependencies:
    ms "2.0.0"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4:
  version "4.4.0"
  resolved "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz"
  integrity sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==
  dependencies:
    ms "^2.1.3"

debug@4.3.4:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"
  integrity sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-data-property@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.1.tgz"
  integrity sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ==
  dependencies:
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

denque@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmjs.org/denque/-/denque-2.1.0.tgz"
  integrity sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

diff@^5.1.0:
  version "5.1.0"

dijkstrajs@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz"
  integrity sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==

dotenv@^16.6.1:
  version "16.6.1"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz"
  integrity sha512-uBq4egWHTcTt33a72vpSG0z3HnPuIl6NqYcTrKEg2azoEyl2hpW0zqlxysq2pK9HlDIHyHyakeYaYnSAwd8bow==

dottie@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/dottie/-/dottie-2.0.6.tgz"
  integrity sha512-iGCHkfUc5kFekGiqhe8B/mdaurD+lakO9txNnTvKtA6PISrw86LgqHvRzWYPyoE2Ph5aMIrCw9/uko6XHTKCwA==

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  integrity sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexify@^4.0.0, duplexify@^4.1.3:
  version "4.1.3"
  resolved "https://registry.npmjs.org/duplexify/-/duplexify-4.1.3.tgz"
  integrity sha512-M3BmBhwJRZsSx38lZyhE53Csddgzl5R7xGJNk7CVddZD6CcmwMCH8J+7AprIrQKH7TonKxaCjcv27Qmf+sQ+oA==
  dependencies:
    end-of-stream "^1.4.1"
    inherits "^2.0.3"
    readable-stream "^3.1.1"
    stream-shift "^1.0.2"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

ecdsa-sig-formatter@^1.0.11, ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

encoding@^0.1.13:
  version "0.1.13"
  resolved "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz"
  integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
  dependencies:
    iconv-lite "^0.6.2"

end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

env-paths@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz"
  integrity sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==

err-code@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz"
  integrity sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  integrity sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  integrity sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  integrity sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es6-promise@^4.2.4:
  version "4.2.8"
  resolved "https://registry.npmjs.org/es6-promise/-/es6-promise-4.2.8.tgz"
  integrity sha512-HJDGx5daxeIvxdBxvG2cb9g4tEvwIk3i8+nhX0yGrYmZUzbkdg8QbDevheDB8gd0//uPj4c1EQua8Q+MViT0/w==

escalade@^3.1.1:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

esm@^3.2.25:
  version "3.2.25"
  resolved "https://registry.npmjs.org/esm/-/esm-3.2.25.tgz"
  integrity sha512-U1suiZ2oDVWv4zPO56S0NcR5QriEahGtdN2OR6FiOG4WJvcjBVFB0qI4+eKoWFH483PKGuLuu6V8Z4T5g63UVA==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

events@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/events/-/events-1.1.1.tgz"
  integrity sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==

eventsource@^1.0.7:
  version "1.1.2"
  resolved "https://registry.npmjs.org/eventsource/-/eventsource-1.1.2.tgz"
  integrity sha512-xAH3zWhgO2/3KIniEKYPr8plNSzlGINOUqYj0m0u7AB81iRw8b/3E73W6AuU+6klLbaSFmZnaETQ2lXPfAydrA==

exponential-backoff@^3.1.1:
  version "3.1.1"

express-fileupload@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/express-fileupload/-/express-fileupload-1.4.3.tgz"
  integrity sha512-vRzZo2YELm68DfR/CX8RMXgeK9BTAANxigrKACPjCXFGEzkCt/QWbqaIXP3W61uaX/hLj0CAo3/EVelpSQXkqA==
  dependencies:
    busboy "^1.6.0"

express-ws@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/express-ws/-/express-ws-5.0.2.tgz"
  integrity sha512-0uvmuk61O9HXgLhGl3QhNSEtRsQevtmbL94/eILaliEADZBHZOQUAiHFrGPrgsjikohyrmSG5g+sCfASTt0lkQ==
  dependencies:
    ws "^7.4.6"

"express@^4.0.0 || ^5.0.0-alpha.1", express@^4.18.2:
  version "4.18.2"
  resolved "https://registry.npmjs.org/express/-/express-4.18.2.tgz"
  integrity sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

farmhash-modern@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/farmhash-modern/-/farmhash-modern-1.1.0.tgz"
  integrity sha512-6ypT4XfgqJk/F3Yuv4SX26I3doUjt0GTG4a+JgWxXQpxXzTBq8fPUeGHfcYMMDPHJHm3yPOSjaeBwBGAHWXCdA==

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-xml-parser@^4.4.1:
  version "4.5.1"
  resolved "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.5.1.tgz"
  integrity sha512-y655CeyUQ+jj7KBbYMc4FG01V8ZQqjN+gDYGJ50RtfsUB8iG9AmwmwoAgeKLJdmueKKMrH1RJ7yXHTSoczdv5w==
  dependencies:
    strnum "^1.0.5"

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"
  integrity sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==

faye-websocket@0.11.4:
  version "0.11.4"
  resolved "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz"
  integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
  dependencies:
    websocket-driver ">=0.5.1"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz"
  integrity sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

firebase-admin@^12.5.0:
  version "12.7.0"
  resolved "https://registry.npmjs.org/firebase-admin/-/firebase-admin-12.7.0.tgz"
  integrity sha512-raFIrOyTqREbyXsNkSHyciQLfv8AUZazehPaQS1lZBSCDYW74FYXU0nQZa3qHI4K+hawohlDbywZ4+qce9YNxA==
  dependencies:
    "@fastify/busboy" "^3.0.0"
    "@firebase/database-compat" "1.0.8"
    "@firebase/database-types" "1.0.5"
    "@types/node" "^22.0.1"
    farmhash-modern "^1.1.0"
    jsonwebtoken "^9.0.0"
    jwks-rsa "^3.1.0"
    node-forge "^1.3.1"
    uuid "^10.0.0"
  optionalDependencies:
    "@google-cloud/firestore" "^7.7.0"
    "@google-cloud/storage" "^7.7.0"

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.5.10.tgz"
  integrity sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==
  dependencies:
    debug "=3.1.0"

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.1.1"
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^2.5.0:
  version "2.5.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz"
  integrity sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz"
  integrity sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-minipass@^2.0.0:
  version "2.1.0"
  dependencies:
    minipass "^3.0.0"

fs-minipass@^3.0.0, fs-minipass@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.3.tgz"
  integrity sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==
  dependencies:
    minipass "^7.0.3"

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==

gauge@^5.0.0:
  version "5.0.1"
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.3"
    console-control-strings "^1.1.0"
    has-unicode "^2.0.1"
    signal-exit "^4.0.1"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.5"

gaxios@^6.0.0, gaxios@^6.0.2, gaxios@^6.0.3, gaxios@^6.1.1:
  version "6.7.1"
  resolved "https://registry.npmjs.org/gaxios/-/gaxios-6.7.1.tgz"
  integrity sha512-LDODD4TMYx7XXdpwxAVRAIAuB0bzv0s+ywFonY46k126qzQHT9ygyoa9tncmOiQmmDrik65UYsEkv3lbfqQ3yQ==
  dependencies:
    extend "^3.0.2"
    https-proxy-agent "^7.0.1"
    is-stream "^2.0.0"
    node-fetch "^2.6.9"
    uuid "^9.0.1"

gcp-metadata@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-6.1.0.tgz"
  integrity sha512-Jh/AIwwgaxan+7ZUUmRLCjtchyDiqh4KjBJ5tW3plBZb5iL/BPcso8A5DlzeD9qlw0duCamnNdpFjxwaT0KyKg==
  dependencies:
    gaxios "^6.0.0"
    json-bigint "^1.0.0"

generate-function@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz"
  integrity sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==
  dependencies:
    is-property "^1.0.2"

generic-pool@3.9.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/generic-pool/-/generic-pool-3.9.0.tgz"
  integrity sha512-hymDOu5B53XvN4QT9dBmZxPX4CWhBPPLguTZ9MMFeFa/Kg0xWVfylOVNlJji/E7yTZWFd/q9GO5TxDLq156D7g==

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.0.2, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2, get-intrinsic@^1.2.6:
  version "1.3.0"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  integrity sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==

get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  integrity sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

getopts@2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/getopts/-/getopts-2.3.0.tgz"
  integrity sha512-5eDf9fuSXwxBL6q5HX+dhDj+dslFGWzU5thZ9kNKUkcPtaPdatmUFKwHFrLb/uf/WpA4BHET+AX3Scl56cAjpA==

glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob@^10.2.2, glob@^10.3.10:
  version "10.3.10"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^2.3.5"
    minimatch "^9.0.1"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry "^1.10.1"

google-auth-library@^9.0.0, google-auth-library@^9.3.0, google-auth-library@^9.6.3, google-auth-library@^9.7.0:
  version "9.15.1"
  resolved "https://registry.npmjs.org/google-auth-library/-/google-auth-library-9.15.1.tgz"
  integrity sha512-Jb6Z0+nvECVz+2lzSMt9u98UsoakXxA2HGHMCxh+so3n90XgYWkq5dur19JAJV7ONiJY22yBTyJB1TSkvPq9Ng==
  dependencies:
    base64-js "^1.3.0"
    ecdsa-sig-formatter "^1.0.11"
    gaxios "^6.1.1"
    gcp-metadata "^6.1.0"
    gtoken "^7.0.0"
    jws "^4.0.0"

google-gax@^4.3.3:
  version "4.4.1"
  resolved "https://registry.npmjs.org/google-gax/-/google-gax-4.4.1.tgz"
  integrity sha512-Phyp9fMfA00J3sZbJxbbB4jC55b7DBjE3F6poyL3wKMEBVKA79q6BGuHcTiM28yOzVql0NDbRL8MLLh8Iwk9Dg==
  dependencies:
    "@grpc/grpc-js" "^1.10.9"
    "@grpc/proto-loader" "^0.7.13"
    "@types/long" "^4.0.0"
    abort-controller "^3.0.0"
    duplexify "^4.0.0"
    google-auth-library "^9.3.0"
    node-fetch "^2.7.0"
    object-hash "^3.0.0"
    proto3-json-serializer "^2.0.2"
    protobufjs "^7.3.2"
    retry-request "^7.0.0"
    uuid "^9.0.1"

googleapis-common@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/googleapis-common/-/googleapis-common-7.2.0.tgz"
  integrity sha512-/fhDZEJZvOV3X5jmD+fKxMqma5q2Q9nZNSF3kn1F18tpxmA86BcTxAGBQdM0N89Z3bEaIs+HVznSmFJEAmMTjA==
  dependencies:
    extend "^3.0.2"
    gaxios "^6.0.3"
    google-auth-library "^9.7.0"
    qs "^6.7.0"
    url-template "^2.0.8"
    uuid "^9.0.0"

googleapis@^144.0.0:
  version "144.0.0"
  resolved "https://registry.npmjs.org/googleapis/-/googleapis-144.0.0.tgz"
  integrity sha512-ELcWOXtJxjPX4vsKMh+7V+jZvgPwYMlEhQFiu2sa9Qmt5veX8nwXPksOWGGN6Zk4xCiLygUyaz7xGtcMO+Onxw==
  dependencies:
    google-auth-library "^9.0.0"
    googleapis-common "^7.0.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  integrity sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==

graceful-fs@^4.2.11, graceful-fs@^4.2.6:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

gtoken@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/gtoken/-/gtoken-7.1.0.tgz"
  integrity sha512-pCcEwRi+TKpMlxAQObHDQ56KawURgyAf6jtIY046fJ5tIv3zDe/LEIubckAO8fj6JnAxLdmWkUfNyulQ2iKdEw==
  dependencies:
    gaxios "^6.0.0"
    jws "^4.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-property-descriptors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.1.tgz"
  integrity sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg==
  dependencies:
    get-intrinsic "^1.2.2"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  integrity sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  integrity sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==
  dependencies:
    has-symbols "^1.0.3"

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

hasown@^2.0.0:
  version "2.0.0"
  dependencies:
    function-bind "^1.1.2"

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hoek@^4.2.1:
  version "4.3.1"
  resolved "https://registry.npmjs.org/hoek/-/hoek-4.3.1.tgz"
  integrity sha512-v7E+yIjcHECn973i0xHm4kJkEpv3C8sbYS4344WXbzYqRyiDD7rjnnKo4hsJkejQBAFdRMUGNHySeSPKSH9Rqw==

hoek@5.x.x:
  version "5.0.4"
  resolved "https://registry.npmjs.org/hoek/-/hoek-5.0.4.tgz"
  integrity sha512-Alr4ZQgoMlnere5FZJsIyfIjORBqZll5POhDsF4q64dPuJR6rNxXdDxtHSQq8OXRurhmx+PWYEE8bXRROY8h0w==

hoek@6.x.x:
  version "6.1.3"
  resolved "https://registry.npmjs.org/hoek/-/hoek-6.1.3.tgz"
  integrity sha512-YXXAAhmF9zpQbC7LEcREFtXfGq5K1fmd+4PHkBq8NUqmzW3G+Dq10bI/i0KucLRwss3YYFQ0fSfoxBZYiGUqtQ==

hosted-git-info@^7.0.0, hosted-git-info@^7.0.1:
  version "7.0.1"
  dependencies:
    lru-cache "^10.0.1"

html-entities@^2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/html-entities/-/html-entities-2.5.2.tgz"
  integrity sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==

http-cache-semantics@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz"
  integrity sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-parser-js@>=0.5.1:
  version "0.5.9"
  resolved "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.9.tgz"
  integrity sha512-n1XsPy3rXVxlqxVioEWdC+0+M+SQw0DpJynwtOPo1X+ZlvdzTLtDBIJJlDQTnwZIFJrZSzSGmIOUdP8tu+SgLw==

http-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz"
  integrity sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==
  dependencies:
    "@tootallnate/once" "2"
    agent-base "6"
    debug "4"

http-proxy-agent@^7.0.0:
  version "7.0.0"
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

https-proxy-agent@^7.0.1:
  version "7.0.6"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz"
  integrity sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==
  dependencies:
    agent-base "^7.1.2"
    debug "4"

i@^0.3.7:
  version "0.3.7"
  resolved "https://registry.npmjs.org/i/-/i-0.3.7.tgz"
  integrity sha512-FYz4wlXgkQwIPqhzC5TdNMLSE5+GS1IIDJZY/1ZiEPCT2S3COUVZeT5OW4BmW4r5LHLQuOosSwsvnroG9GR59Q==

iconv-lite@^0.6.2:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13, ieee754@^1.1.4, ieee754@1.1.13:
  version "1.1.13"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz"
  integrity sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
  integrity sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==

ignore-walk@^6.0.4:
  version "6.0.4"
  dependencies:
    minimatch "^9.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflection@^1.13.4:
  version "1.13.4"
  resolved "https://registry.npmjs.org/inflection/-/inflection-1.13.4.tgz"
  integrity sha512-6I/HUDeYFfuNCVS3td055BaXBwKYuzw7K3ExVMStBowKo9oOAMJIXIHvdyR3iboTCp1b+1i5DSkIZTcwIktuDw==

inherits@^2.0.1, inherits@^2.0.3, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ini@^4.1.0, ini@^4.1.1:
  version "4.1.1"

init-package-json@^6.0.0:
  version "6.0.0"
  dependencies:
    npm-package-arg "^11.0.0"
    promzard "^1.0.0"
    read "^2.0.0"
    read-package-json "^7.0.0"
    semver "^7.3.5"
    validate-npm-package-license "^3.0.4"
    validate-npm-package-name "^5.0.0"

interpret@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/interpret/-/interpret-2.2.0.tgz"
  integrity sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==

ip-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/ip-regex/-/ip-regex-5.0.0.tgz"
  integrity sha512-fOCG6lhoKKakwv+C6KdsOnGvgXnmgfmp0myi3bcNwj3qfwPAxRKWEuFhvEFF7ceYIz6+1jRZ+yguLFAmUNPEfw==

ip@^2.0.0:
  version "2.0.0"

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-callable@^1.1.3:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-cidr@^5.0.3:
  version "5.0.3"
  dependencies:
    cidr-regex "4.0.3"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz"
  integrity sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==
  dependencies:
    hasown "^2.0.2"

is-core-module@^2.8.1:
  version "2.13.1"
  dependencies:
    hasown "^2.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-lambda@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/is-lambda/-/is-lambda-1.0.1.tgz"
  integrity sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-property@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz"
  integrity sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-typed-array@^1.1.3:
  version "1.1.12"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz"
  integrity sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==
  dependencies:
    which-typed-array "^1.1.11"

isarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isemail@3.x.x:
  version "3.2.0"
  resolved "https://registry.npmjs.org/isemail/-/isemail-3.2.0.tgz"
  integrity sha512-zKqkK+O+dGqevc93KNsbZ/TqTUFd46MwWjYOoMrjIMZ51eU7DtQG3Wmd9SQQT7i7RVnuTPEiYEWHU3MSbxC1Tg==
  dependencies:
    punycode "2.x.x"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isexe@^3.1.1:
  version "3.1.1"

jackspeak@^2.3.5:
  version "2.3.6"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://registry.npmjs.org/jmespath/-/jmespath-0.16.0.tgz"
  integrity sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==

joi@^13.1.2:
  version "13.7.0"
  resolved "https://registry.npmjs.org/joi/-/joi-13.7.0.tgz"
  integrity sha512-xuY5VkHfeOYK3Hdi91ulocfuFopwgbSORmIwzcwHKESQhC7w1kD5jaVSPnqDxS2I8t3RZ9omCKAxNwXN5zG1/Q==
  dependencies:
    hoek "5.x.x"
    isemail "3.x.x"
    topo "3.x.x"

jose@^4.14.6:
  version "4.15.9"
  resolved "https://registry.npmjs.org/jose/-/jose-4.15.9.tgz"
  integrity sha512-1vUQX+IdDMVPj4k8kOxgUqlcK518yluMuGZwqlr44FS1ppZB/5GWh4rZG89erpOBOJjU/OBsnCVFfapsRz6nEA==

js-xdr@^1.1.3:
  version "1.3.0"
  resolved "https://registry.npmjs.org/js-xdr/-/js-xdr-1.3.0.tgz"
  integrity sha512-fjLTm2uBtFvWsE3l2J14VjTuuB8vJfeTtYuNS7LiLHDWIX2kt0l1pqq9334F8kODUkKPMuULjEcbGbkFFwhx5g==
  dependencies:
    lodash "^4.17.5"
    long "^2.2.3"

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
  integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
  dependencies:
    bignumber.js "^9.0.0"

json-parse-even-better-errors@^3.0.0, json-parse-even-better-errors@^3.0.1:
  version "3.0.1"

json-stringify-nice@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/json-stringify-nice/-/json-stringify-nice-1.1.4.tgz"
  integrity sha512-5Z5RFW63yxReJ7vANgW6eZFGWaQvnPE3WNmZoOJrSkGju2etKA2L5rrOa1sm877TVTFt57A80BH1bArcmlLfPw==

jsonparse@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/jsonparse/-/jsonparse-1.3.1.tgz"
  integrity sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==

jsonwebtoken@^9.0.0, jsonwebtoken@^9.0.2:
  version "9.0.2"
  resolved "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  integrity sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

just-diff-apply@^5.2.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/just-diff-apply/-/just-diff-apply-5.5.0.tgz"
  integrity sha512-OYTthRfSh55WOItVqwpefPtNt2VdKsq5AnAK6apdtR6yCH8pr0CmSr710J0Mf+WdQy7K/OzMy7K2MgAfdQURDw==

just-diff@^6.0.0:
  version "6.0.2"
  resolved "https://registry.npmjs.org/just-diff/-/just-diff-6.0.2.tgz"
  integrity sha512-S59eriX5u3/QhMNq3v/gm8Kd0w8OS6Tz2FS1NG4blv+z0MuQcBRJyFWjdovM0Rad4/P4aUPFtnkNjMjyMlMSYA==

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz"
  integrity sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jwa@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/jwa/-/jwa-2.0.0.tgz"
  integrity sha512-jrZ2Qx916EA+fq9cEAeCROWPTfCwi1IVHqT2tapuqLEVVDKFDENFw1oL+MwrTvH6msKxsd1YTDVw6uKEcsrLEA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jwks-rsa@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/jwks-rsa/-/jwks-rsa-3.1.0.tgz"
  integrity sha512-v7nqlfezb9YfHHzYII3ef2a2j1XnGeSE/bK3WfumaYCqONAIstJbrEGapz4kadScZzEt7zYCN7bucj8C0Mv/Rg==
  dependencies:
    "@types/express" "^4.17.17"
    "@types/jsonwebtoken" "^9.0.2"
    debug "^4.3.4"
    jose "^4.14.6"
    limiter "^1.1.5"
    lru-memoizer "^2.2.0"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  integrity sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

jws@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/jws/-/jws-4.0.0.tgz"
  integrity sha512-KDncfTmOZoOMTFG4mBlG0qUIOlc03fmzH+ru6RgYVZhPkyiy/92Owlt/8UEN+a4TXR1FQetfIpJE8ApdvdVxTg==
  dependencies:
    jwa "^2.0.0"
    safe-buffer "^5.0.1"

knex@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/knex/-/knex-3.1.0.tgz"
  integrity sha512-GLoII6hR0c4ti243gMs5/1Rb3B+AjwMOfjYm97pu0FOQa7JH56hgBxYf5WK2525ceSbBY1cjeZ9yk99GPMB6Kw==
  dependencies:
    colorette "2.0.19"
    commander "^10.0.0"
    debug "4.3.4"
    escalade "^3.1.1"
    esm "^3.2.25"
    get-package-type "^0.1.0"
    getopts "2.3.0"
    interpret "^2.2.0"
    lodash "^4.17.21"
    pg-connection-string "2.6.2"
    rechoir "^0.8.0"
    resolve-from "^5.0.0"
    tarn "^3.0.2"
    tildify "2.0.0"

libnpmaccess@^8.0.1:
  version "8.0.2"
  dependencies:
    npm-package-arg "^11.0.1"
    npm-registry-fetch "^16.0.0"

libnpmdiff@^6.0.3:
  version "6.0.6"
  dependencies:
    "@npmcli/arborist" "^7.2.1"
    "@npmcli/disparity-colors" "^3.0.0"
    "@npmcli/installed-package-contents" "^2.0.2"
    binary-extensions "^2.2.0"
    diff "^5.1.0"
    minimatch "^9.0.0"
    npm-package-arg "^11.0.1"
    pacote "^17.0.4"
    tar "^6.2.0"

libnpmexec@^7.0.4:
  version "7.0.7"
  dependencies:
    "@npmcli/arborist" "^7.2.1"
    "@npmcli/run-script" "^7.0.2"
    ci-info "^4.0.0"
    npm-package-arg "^11.0.1"
    npmlog "^7.0.1"
    pacote "^17.0.4"
    proc-log "^3.0.0"
    read "^2.0.0"
    read-package-json-fast "^3.0.2"
    semver "^7.3.7"
    walk-up-path "^3.0.1"

libnpmfund@^5.0.1:
  version "5.0.4"
  dependencies:
    "@npmcli/arborist" "^7.2.1"

libnpmhook@^10.0.0:
  version "10.0.1"
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^16.0.0"

libnpmorg@^6.0.1:
  version "6.0.2"
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^16.0.0"

libnpmpack@^6.0.3:
  version "6.0.6"
  dependencies:
    "@npmcli/arborist" "^7.2.1"
    "@npmcli/run-script" "^7.0.2"
    npm-package-arg "^11.0.1"
    pacote "^17.0.4"

libnpmpublish@^9.0.2:
  version "9.0.4"
  dependencies:
    ci-info "^4.0.0"
    normalize-package-data "^6.0.0"
    npm-package-arg "^11.0.1"
    npm-registry-fetch "^16.0.0"
    proc-log "^3.0.0"
    semver "^7.3.7"
    sigstore "^2.2.0"
    ssri "^10.0.5"

libnpmsearch@^7.0.0:
  version "7.0.1"
  dependencies:
    npm-registry-fetch "^16.0.0"

libnpmteam@^6.0.0:
  version "6.0.1"
  dependencies:
    aproba "^2.0.0"
    npm-registry-fetch "^16.0.0"

libnpmversion@^5.0.1:
  version "5.0.2"
  resolved "https://registry.npmjs.org/libnpmversion/-/libnpmversion-5.0.2.tgz"
  integrity sha512-6JBnLhd6SYgKRekJ4cotxpURLGbEtKxzw+a8p5o+wNwrveJPMH8yW/HKjeewyHzWmxzzwn9EQ3TkF2onkrwstA==
  dependencies:
    "@npmcli/git" "^5.0.3"
    "@npmcli/run-script" "^7.0.2"
    json-parse-even-better-errors "^3.0.0"
    proc-log "^3.0.0"
    semver "^7.3.7"

limiter@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/limiter/-/limiter-1.1.5.tgz"
  integrity sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz"
  integrity sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  integrity sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==

lodash@^4.17.11, lodash@^4.17.21, lodash@^4.17.5:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

long@^2.2.3:
  version "2.4.0"
  resolved "https://registry.npmjs.org/long/-/long-2.4.0.tgz"
  integrity sha512-ijUtjmO/n2A5PaosNG9ZGDsQ3vxJg7ZW8vsY8Kp0f2yIZWhSJvjmegV7t+9RPQKxKrvj8yKGehhS+po14hPLGQ==

long@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/long/-/long-4.0.0.tgz"
  integrity sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==

long@^5.0.0:
  version "5.2.4"
  resolved "https://registry.npmjs.org/long/-/long-5.2.4.tgz"
  integrity sha512-qtzLbJE8hq7VabR3mISmVGtoXP8KGc2Z/AT8OuqlYD7JTR3oqrgwdjnk07wpj1twXxYmgDXgoKVWUG/fReSzHg==

lru-cache@^10.0.1, "lru-cache@^9.1.1 || ^10.0.0":
  version "10.1.0"

lru-cache@^6.0.0, lru-cache@6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.14.1:
  version "7.18.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  integrity sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==

lru-memoizer@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/lru-memoizer/-/lru-memoizer-2.3.0.tgz"
  integrity sha512-GXn7gyHAMhO13WSKrIiNfztwxodVsP8IoZ3XfrJV4yH2x0/OeTO/FIaAHTY5YekdGgW94njfuKmyyt1E0mR6Ug==
  dependencies:
    lodash.clonedeep "^4.5.0"
    lru-cache "6.0.0"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

make-fetch-happen@^13.0.0:
  version "13.0.0"
  dependencies:
    "@npmcli/agent" "^2.0.0"
    cacache "^18.0.0"
    http-cache-semantics "^4.1.1"
    is-lambda "^1.0.1"
    minipass "^7.0.2"
    minipass-fetch "^3.0.0"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    negotiator "^0.6.3"
    promise-retry "^2.0.1"
    ssri "^10.0.0"

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  integrity sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/mime/-/mime-3.0.0.tgz"
  integrity sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.0, minimatch@^9.0.1, minimatch@^9.0.3:
  version "9.0.3"
  dependencies:
    brace-expansion "^2.0.1"

minipass-collect@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/minipass-collect/-/minipass-collect-2.0.1.tgz"
  integrity sha512-D7V8PO9oaz7PWGLbCACuI1qEOsq7UKfLotx/C0Aet43fCUB/wfQ7DYeq2oR/svFJGYDHPr38SHATeaj/ZoKHKw==
  dependencies:
    minipass "^7.0.3"

minipass-fetch@^3.0.0:
  version "3.0.4"
  dependencies:
    minipass "^7.0.3"
    minipass-sized "^1.0.3"
    minizlib "^2.1.2"
  optionalDependencies:
    encoding "^0.1.13"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/minipass-flush/-/minipass-flush-1.0.5.tgz"
  integrity sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==
  dependencies:
    minipass "^3.0.0"

minipass-json-stream@^1.0.1:
  version "1.0.1"
  dependencies:
    jsonparse "^1.3.1"
    minipass "^3.0.0"

minipass-pipeline@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmjs.org/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
  integrity sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==
  dependencies:
    minipass "^3.0.0"

minipass-sized@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/minipass-sized/-/minipass-sized-1.0.3.tgz"
  integrity sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0:
  version "3.3.6"
  dependencies:
    yallist "^4.0.0"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.2, minipass@^7.0.3, minipass@^7.0.4:
  version "7.0.4"

minipass@^5.0.0:
  version "5.0.0"

minizlib@^2.1.1, minizlib@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

moment-timezone@^0.5.43:
  version "0.5.43"
  resolved "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.43.tgz"
  integrity sha512-72j3aNyuIsDxdF1i7CEgV2FfxM1r6aaqJyLB2vwb33mXYyoyLly+F1zbWqhA3/bVIoJ4szlUoMbUnVdid32NUQ==
  dependencies:
    moment "^2.29.4"

moment@^2.29.4:
  version "2.30.1"
  resolved "https://registry.npmjs.org/moment/-/moment-2.30.1.tgz"
  integrity sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

ms@^2.1.2:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

mute-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-1.0.0.tgz"
  integrity sha512-avsJQhyd+680gKXyG/sQc0nXaC6rBkPOfyHYcFb9+hdkqQkR9bdnkJ0AMZhke0oesPqIO+mFFJ+IdBc7mst4IA==

mysql2@^2.2.5:
  version "2.3.3"
  resolved "https://registry.npmjs.org/mysql2/-/mysql2-2.3.3.tgz"
  integrity sha512-wxJUev6LgMSgACDkb/InIFxDprRa6T95+VEoR+xPvtngtccNH2dGjEB/fVZ8yg1gWv1510c9CvXuJHi5zUm0ZA==
  dependencies:
    denque "^2.0.1"
    generate-function "^2.3.1"
    iconv-lite "^0.6.3"
    long "^4.0.0"
    lru-cache "^6.0.0"
    named-placeholders "^1.1.2"
    seq-queue "^0.0.5"
    sqlstring "^2.3.2"

named-placeholders@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.3.tgz"
  integrity sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==
  dependencies:
    lru-cache "^7.14.1"

nan@^2.19.0:
  version "2.22.0"
  resolved "https://registry.npmjs.org/nan/-/nan-2.22.0.tgz"
  integrity sha512-nbajikzWTMwsW+eSsNm3QwlOs7het9gGJU5dDZzRTQGk03vyBOauxgI4VakDzE0PtsGTmXPsXTbbjVhRwR5mpw==

negotiator@^0.6.3:
  version "0.6.3"

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

node-cron@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/node-cron/-/node-cron-3.0.3.tgz"
  integrity sha512-dOal67//nohNgYWb+nWmg5dkFdIwDm8EpeGYMekPMrngV3637lqnX0lbUcCtgibHTz6SEz7DAIjKvKDFYCnO1A==
  dependencies:
    uuid "8.3.2"

node-expat@^2.3.18:
  version "2.4.1"
  resolved "https://registry.npmjs.org/node-expat/-/node-expat-2.4.1.tgz"
  integrity sha512-uWgvQLgo883NKIL+66oJsK9ysKK3ej0YjVCPBZzO/7wMAuH68/Yb7+JwPWNaVq0yPaxrb48AoEXfYEc8gsmFbg==
  dependencies:
    bindings "^1.5.0"
    nan "^2.19.0"

node-fetch@^2.6.9, node-fetch@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz"
  integrity sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==

node-gyp@^10.0.0, node-gyp@^10.0.1:
  version "10.0.1"
  dependencies:
    env-paths "^2.2.0"
    exponential-backoff "^3.1.1"
    glob "^10.3.10"
    graceful-fs "^4.2.6"
    make-fetch-happen "^13.0.0"
    nopt "^7.0.0"
    proc-log "^3.0.0"
    semver "^7.3.5"
    tar "^6.1.2"
    which "^4.0.0"

node-rsa@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/node-rsa/-/node-rsa-1.1.1.tgz"
  integrity sha512-Jd4cvbJMryN21r5HgxQOpMEqv+ooke/korixNNK3mGqfGJmy0M77WDDzo/05969+OkMy3XW1UuZsSmW9KQm7Fw==
  dependencies:
    asn1 "^0.2.4"

nodemailer@^6.9.15:
  version "6.9.15"
  resolved "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.15.tgz"
  integrity sha512-AHf04ySLC6CIfuRtRiEYtGEXgRfa6INgWGluDhnxTZhHSKvrBu7lc1VVchQ0d8nPc4cFaZoPq8vkyNoZr0TpGQ==

nodemon@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/nodemon/-/nodemon-3.0.2.tgz"
  integrity sha512-9qIN2LNTrEzpOPBaWHTm4Asy1LxXLSickZStAQ4IZe7zsoIpD/A7LWxhZV3t4Zu352uBcqVnRsDXSMR2Sc3lTA==
  dependencies:
    chokidar "^3.5.2"
    debug "^4"
    ignore-by-default "^1.0.1"
    minimatch "^3.1.2"
    pstree.remy "^1.1.8"
    semver "^7.5.3"
    simple-update-notifier "^2.0.0"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.5"

nopt@^7.0.0, nopt@^7.2.0:
  version "7.2.0"
  dependencies:
    abbrev "^2.0.0"

nopt@~1.0.10:
  version "1.0.10"
  resolved "https://registry.npmjs.org/nopt/-/nopt-1.0.10.tgz"
  integrity sha512-NWmpvLSqUrgrAC9HCuxEvb+PSloHpqVu+FqcO4eeF2h5qYRhA7ev6KvelyQAKtegUbC6RypJnlEOhd8vloNKYg==
  dependencies:
    abbrev "1"

normalize-package-data@^6.0.0:
  version "6.0.0"
  dependencies:
    hosted-git-info "^7.0.0"
    is-core-module "^2.8.1"
    semver "^7.3.5"
    validate-npm-package-license "^3.0.4"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

npm-audit-report@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/npm-audit-report/-/npm-audit-report-5.0.0.tgz"
  integrity sha512-EkXrzat7zERmUhHaoren1YhTxFwsOu5jypE84k6632SXTHcQE1z8V51GC6GVZt8LxkC+tbBcKMUBZAgk8SUSbw==

npm-bundled@^3.0.0:
  version "3.0.0"
  dependencies:
    npm-normalize-package-bin "^3.0.0"

npm-install-checks@^6.0.0, npm-install-checks@^6.2.0, npm-install-checks@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/npm-install-checks/-/npm-install-checks-6.3.0.tgz"
  integrity sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==
  dependencies:
    semver "^7.1.1"

npm-normalize-package-bin@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.1.tgz"
  integrity sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==

npm-package-arg@^11.0.0, npm-package-arg@^11.0.1:
  version "11.0.1"
  dependencies:
    hosted-git-info "^7.0.0"
    proc-log "^3.0.0"
    semver "^7.3.5"
    validate-npm-package-name "^5.0.0"

npm-packlist@^8.0.0:
  version "8.0.2"
  resolved "https://registry.npmjs.org/npm-packlist/-/npm-packlist-8.0.2.tgz"
  integrity sha512-shYrPFIS/JLP4oQmAwDyk5HcyysKW8/JLTEA32S0Z5TzvpaeeX2yMFfoK1fjEBnCBvVyIB/Jj/GBFdm0wsgzbA==
  dependencies:
    ignore-walk "^6.0.4"

npm-pick-manifest@^9.0.0:
  version "9.0.0"
  dependencies:
    npm-install-checks "^6.0.0"
    npm-normalize-package-bin "^3.0.0"
    npm-package-arg "^11.0.0"
    semver "^7.3.5"

npm-profile@^9.0.0:
  version "9.0.0"
  dependencies:
    npm-registry-fetch "^16.0.0"
    proc-log "^3.0.0"

npm-registry-fetch@^16.0.0, npm-registry-fetch@^16.1.0:
  version "16.1.0"
  dependencies:
    make-fetch-happen "^13.0.0"
    minipass "^7.0.2"
    minipass-fetch "^3.0.0"
    minipass-json-stream "^1.0.1"
    minizlib "^2.1.2"
    npm-package-arg "^11.0.0"
    proc-log "^3.0.0"

npm-user-validate@^2.0.0:
  version "2.0.0"

npm@^10.4.0:
  version "10.4.0"
  resolved "https://registry.npmjs.org/npm/-/npm-10.4.0.tgz"
  integrity sha512-RS7Mx0OVfXlOcQLRePuDIYdFCVBPCNapWHplDK+mh7GDdP/Tvor4ocuybRRPSvfcRb2vjRJt1fHCqw3cr8qACQ==
  dependencies:
    "@isaacs/string-locale-compare" "^1.1.0"
    "@npmcli/arborist" "^7.2.1"
    "@npmcli/config" "^8.0.2"
    "@npmcli/fs" "^3.1.0"
    "@npmcli/map-workspaces" "^3.0.4"
    "@npmcli/package-json" "^5.0.0"
    "@npmcli/promise-spawn" "^7.0.1"
    "@npmcli/run-script" "^7.0.4"
    "@sigstore/tuf" "^2.3.0"
    abbrev "^2.0.0"
    archy "~1.0.0"
    cacache "^18.0.2"
    chalk "^5.3.0"
    ci-info "^4.0.0"
    cli-columns "^4.0.0"
    cli-table3 "^0.6.3"
    columnify "^1.6.0"
    fastest-levenshtein "^1.0.16"
    fs-minipass "^3.0.3"
    glob "^10.3.10"
    graceful-fs "^4.2.11"
    hosted-git-info "^7.0.1"
    ini "^4.1.1"
    init-package-json "^6.0.0"
    is-cidr "^5.0.3"
    json-parse-even-better-errors "^3.0.1"
    libnpmaccess "^8.0.1"
    libnpmdiff "^6.0.3"
    libnpmexec "^7.0.4"
    libnpmfund "^5.0.1"
    libnpmhook "^10.0.0"
    libnpmorg "^6.0.1"
    libnpmpack "^6.0.3"
    libnpmpublish "^9.0.2"
    libnpmsearch "^7.0.0"
    libnpmteam "^6.0.0"
    libnpmversion "^5.0.1"
    make-fetch-happen "^13.0.0"
    minimatch "^9.0.3"
    minipass "^7.0.4"
    minipass-pipeline "^1.2.4"
    ms "^2.1.2"
    node-gyp "^10.0.1"
    nopt "^7.2.0"
    normalize-package-data "^6.0.0"
    npm-audit-report "^5.0.0"
    npm-install-checks "^6.3.0"
    npm-package-arg "^11.0.1"
    npm-pick-manifest "^9.0.0"
    npm-profile "^9.0.0"
    npm-registry-fetch "^16.1.0"
    npm-user-validate "^2.0.0"
    npmlog "^7.0.1"
    p-map "^4.0.0"
    pacote "^17.0.6"
    parse-conflict-json "^3.0.1"
    proc-log "^3.0.0"
    qrcode-terminal "^0.12.0"
    read "^2.1.0"
    semver "^7.5.4"
    spdx-expression-parse "^3.0.1"
    ssri "^10.0.5"
    supports-color "^9.4.0"
    tar "^6.2.0"
    text-table "~0.2.0"
    tiny-relative-date "^1.3.0"
    treeverse "^3.0.0"
    validate-npm-package-name "^5.0.0"
    which "^4.0.0"
    write-file-atomic "^5.0.1"

npmlog@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-7.0.1.tgz"
  integrity sha512-uJ0YFk/mCQpLBt+bxN88AKd+gyqZvZDbtiNxk6Waqcj2aPRyfVx8ITawkyQynxUagInjdYT1+qj4NfA5KJJUxg==
  dependencies:
    are-we-there-yet "^4.0.0"
    console-control-strings "^1.1.0"
    gauge "^5.0.0"
    set-blocking "^2.0.0"

object-assign@^4:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.9.0:
  version "1.13.1"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz"
  integrity sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.1:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

pacote@^17.0.0, pacote@^17.0.4, pacote@^17.0.6:
  version "17.0.6"
  dependencies:
    "@npmcli/git" "^5.0.0"
    "@npmcli/installed-package-contents" "^2.0.1"
    "@npmcli/promise-spawn" "^7.0.0"
    "@npmcli/run-script" "^7.0.0"
    cacache "^18.0.0"
    fs-minipass "^3.0.0"
    minipass "^7.0.2"
    npm-package-arg "^11.0.0"
    npm-packlist "^8.0.0"
    npm-pick-manifest "^9.0.0"
    npm-registry-fetch "^16.0.0"
    proc-log "^3.0.0"
    promise-retry "^2.0.1"
    read-package-json "^7.0.0"
    read-package-json-fast "^3.0.0"
    sigstore "^2.2.0"
    ssri "^10.0.0"
    tar "^6.1.11"

parse-conflict-json@^3.0.0, parse-conflict-json@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/parse-conflict-json/-/parse-conflict-json-3.0.1.tgz"
  integrity sha512-01TvEktc68vwbJOtWZluyWeVGWjP+bZwXtPDMQVbBKzbJ/vZBif0L69KH1+cHv1SZ6e0FKLvjyHe8mqsIqYOmw==
  dependencies:
    json-parse-even-better-errors "^3.0.0"
    just-diff "^6.0.0"
    just-diff-apply "^5.2.0"

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.10.1:
  version "1.10.1"
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==

pg-connection-string@^2.6.1, pg-connection-string@2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/pg-connection-string/-/pg-connection-string-2.6.2.tgz"
  integrity sha512-ch6OwaeaPYcova4kKZ15sbJ2hKb/VP48ZD2gE7i1J+L4MspCtBMAx8nMgz7bksc7IojCIIWuEhHibSMFH8m8oA==

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz"
  integrity sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==

postcss-selector-parser@^6.0.10:
  version "6.0.15"
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

proc-log@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/proc-log/-/proc-log-3.0.0.tgz"
  integrity sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==

promise-all-reject-late@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-all-reject-late/-/promise-all-reject-late-1.0.1.tgz"
  integrity sha512-vuf0Lf0lOxyQREH7GDIOUMLS7kz+gs8i6B+Yi8dC68a2sychGrHTJYghMBD6k7eUcH0H5P73EckCA48xijWqXw==

promise-call-limit@^3.0.1:
  version "3.0.1"

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/promise-inflight/-/promise-inflight-1.0.1.tgz"
  integrity sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==

promise-retry@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz"
  integrity sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==
  dependencies:
    err-code "^2.0.2"
    retry "^0.12.0"

promzard@^1.0.0:
  version "1.0.0"
  dependencies:
    read "^2.0.0"

proto3-json-serializer@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/proto3-json-serializer/-/proto3-json-serializer-2.0.2.tgz"
  integrity sha512-SAzp/O4Yh02jGdRc+uIrGoe87dkN/XtwxfZ4ZyafJHymd79ozp5VG5nyZ7ygqPM5+cpLDjjGnYFUkngonyDPOQ==
  dependencies:
    protobufjs "^7.2.5"

protobufjs@^7.2.5, protobufjs@^7.2.6, protobufjs@^7.3.2:
  version "7.4.0"
  resolved "https://registry.npmjs.org/protobufjs/-/protobufjs-7.4.0.tgz"
  integrity sha512-mRUWCc3KUU4w1jU8sGxICXH/gNS94DvI1gxqDvBzhj1JpcsimQkYiOJfwsPUykUI5ZaspFbSgmBLER8IrQ3tqw==
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/node" ">=13.7.0"
    long "^5.0.0"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

pstree.remy@^1.1.8:
  version "1.1.8"
  resolved "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz"
  integrity sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz"
  integrity sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==

punycode@2.x.x:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

qrcode-terminal@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.12.0.tgz"
  integrity sha512-EXtzRZmC+YGmGlDFbXKxQiMZNwCLEO6BANKXG4iCtSIM0yqc/pappSx3RIKr4r0uh5JsBckOXeKrB3Iz7mdQpQ==

qrcode@^1.5.4:
  version "1.5.4"
  resolved "https://registry.npmjs.org/qrcode/-/qrcode-1.5.4.tgz"
  integrity sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==
  dependencies:
    dijkstrajs "^1.0.1"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@^6.7.0, qs@6.11.0:
  version "6.11.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz"
  integrity sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-cmd-shim@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-4.0.0.tgz"
  integrity sha512-yILWifhaSEEytfXI76kB9xEEiG1AiozaCJZ83A87ytjRiN+jVibXjedjCRNjoZviinhG+4UkalO3mWTd8u5O0Q==

read-package-json-fast@^3.0.0, read-package-json-fast@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-3.0.2.tgz"
  integrity sha512-0J+Msgym3vrLOUB3hzQCuZHII0xkNGCtz/HJH9xZshwv9DbDwkw1KaE3gx/e2J5rpEY5rtOy6cyhKOPrkP7FZw==
  dependencies:
    json-parse-even-better-errors "^3.0.0"
    npm-normalize-package-bin "^3.0.0"

read-package-json@^7.0.0:
  version "7.0.0"
  dependencies:
    glob "^10.2.2"
    json-parse-even-better-errors "^3.0.0"
    normalize-package-data "^6.0.0"
    npm-normalize-package-bin "^3.0.0"

read@^2.0.0, read@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/read/-/read-2.1.0.tgz"
  integrity sha512-bvxi1QLJHcaywCAEsAk4DG3nVoqiY2Csps3qzWalhj5hFqRn1d/OixkFXtLO1PrgHUcAP0FNaSY/5GYNfENFFQ==
  dependencies:
    mute-stream "~1.0.0"

readable-stream@^3.1.1:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.8.0.tgz"
  integrity sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==
  dependencies:
    resolve "^1.20.0"

redis@^4.6.11:
  version "4.7.0"
  resolved "https://registry.npmjs.org/redis/-/redis-4.7.0.tgz"
  integrity sha512-zvmkHEAdGMn+hMRXuMBtu4Vo5P6rHQjLoHftu+lBqq8ZTA3RCVC/WzD790bkKKiNFp7d5/9PcSD19fJyyRvOdQ==
  dependencies:
    "@redis/bloom" "1.2.0"
    "@redis/client" "1.6.0"
    "@redis/graph" "1.1.1"
    "@redis/json" "1.0.7"
    "@redis/search" "1.2.0"
    "@redis/time-series" "1.1.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz"
  integrity sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve@^1.20.0:
  version "1.22.10"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz"
  integrity sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

retry-as-promised@^7.0.4:
  version "7.0.4"
  resolved "https://registry.npmjs.org/retry-as-promised/-/retry-as-promised-7.0.4.tgz"
  integrity sha512-XgmCoxKWkDofwH8WddD0w85ZfqYz+ZHlr5yo+3YUCfycWawU56T5ckWXsScsj5B8tqUcIG67DxXByo3VUgiAdA==

retry-request@^7.0.0:
  version "7.0.2"
  resolved "https://registry.npmjs.org/retry-request/-/retry-request-7.0.2.tgz"
  integrity sha512-dUOvLMJ0/JJYEn8NrpOaGNE7X3vpI5XlZS/u0ANjqtcZVKnIxP7IgCFwrKTxENw29emmwug53awKtaMm4i9g5w==
  dependencies:
    "@types/request" "^2.48.8"
    extend "^3.0.2"
    teeny-request "^9.0.0"

retry@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz"
  integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==

retry@0.13.1:
  version "0.13.1"
  resolved "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@>=5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sax@>=0.6.0, sax@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz"
  integrity sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA==

semver@^7.0.0, semver@^7.1.1, semver@^7.3.5, semver@^7.3.7, semver@^7.5.3, semver@^7.5.4:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

seq-queue@^0.0.5:
  version "0.0.5"
  resolved "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.5.tgz"
  integrity sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q==

sequelize-pool@^7.1.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/sequelize-pool/-/sequelize-pool-7.1.0.tgz"
  integrity sha512-G9c0qlIWQSK29pR/5U2JF5dDQeqqHRragoyahj/Nx4KOOQ3CPPfzxnfqFPCSB7x5UgjOgnZ61nSxz+fjDpRlJg==

sequelize@^6.35.2:
  version "6.35.2"
  resolved "https://registry.npmjs.org/sequelize/-/sequelize-6.35.2.tgz"
  integrity sha512-EdzLaw2kK4/aOnWQ7ed/qh3B6/g+1DvmeXr66RwbcqSm/+QRS9X0LDI5INBibsy4eNJHWIRPo3+QK0zL+IPBHg==
  dependencies:
    "@types/debug" "^4.1.8"
    "@types/validator" "^13.7.17"
    debug "^4.3.4"
    dottie "^2.0.6"
    inflection "^1.13.4"
    lodash "^4.17.21"
    moment "^2.29.4"
    moment-timezone "^0.5.43"
    pg-connection-string "^2.6.1"
    retry-as-promised "^7.0.4"
    semver "^7.5.4"
    sequelize-pool "^7.1.0"
    toposort-class "^1.0.1"
    uuid "^8.3.2"
    validator "^13.9.0"
    wkx "^0.5.0"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

set-function-length@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.1.1.tgz"
  integrity sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ==
  dependencies:
    define-data-property "^1.1.1"
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

sha.js@^2.3.6:
  version "2.4.11"
  resolved "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz"
  integrity sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

sigstore@^2.2.0:
  version "2.2.0"
  dependencies:
    "@sigstore/bundle" "^2.1.1"
    "@sigstore/core" "^0.2.0"
    "@sigstore/protobuf-specs" "^0.2.1"
    "@sigstore/sign" "^2.2.1"
    "@sigstore/tuf" "^2.3.0"
    "@sigstore/verify" "^0.1.0"

simple-update-notifier@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz"
  integrity sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==
  dependencies:
    semver "^7.5.3"

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks-proxy-agent@^8.0.1:
  version "8.0.2"
  dependencies:
    agent-base "^7.0.2"
    debug "^4.3.4"
    socks "^2.7.1"

socks@^2.7.1:
  version "2.7.1"
  dependencies:
    ip "^2.0.0"
    smart-buffer "^4.2.0"

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz"
  integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"

spdx-expression-parse@^3.0.0, spdx-expression-parse@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.16"

speakeasy@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/speakeasy/-/speakeasy-2.0.0.tgz"
  integrity sha512-lW2A2s5LKi8rwu77ewisuUOtlCydF/hmQSOJjpTqTj1gZLkNgTaYnyvfxy2WBr4T/h+9c4g8HIITfj83OkFQFw==
  dependencies:
    base32.js "0.0.1"

sqlstring@^2.3.2:
  version "2.3.3"
  resolved "https://registry.npmjs.org/sqlstring/-/sqlstring-2.3.3.tgz"
  integrity sha512-qC9iz2FlN7DQl3+wjwn3802RTyjCx7sDvfQEXchwa6CWOx07/WVfh91gBmQ9fahw8snwGEWU3xGzOt4tFyHLxg==

ssri@^10.0.0, ssri@^10.0.5:
  version "10.0.5"
  dependencies:
    minipass "^7.0.3"

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

stellar-base@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/stellar-base/-/stellar-base-4.0.3.tgz"
  integrity sha512-Tf1Ko86j4CgUtaiTC6H6qPZ97EziXN6SVokH4FGv5+XvBhFg4V+u9ySMlgPjFbHQumplkHiw8JQx1IZRHnrT3A==
  dependencies:
    base32.js "^0.1.0"
    bignumber.js "^4.0.0"
    crc "^3.5.0"
    js-xdr "^1.1.3"
    lodash "^4.17.11"
    sha.js "^2.3.6"
    tweetnacl "^1.0.0"
  optionalDependencies:
    sodium-native "^2.3.0"

stellar-sdk@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/stellar-sdk/-/stellar-sdk-7.0.0.tgz"
  integrity sha512-90hGtqs4W79CFs2SoRtlNC03LysbSZa0UI/msEckQstFnuDzfTxh0GXaN/9EU9OmCE9F13hCIb+E/LBLlPNeHg==
  dependencies:
    "@types/eventsource" "^1.1.2"
    "@types/node" ">= 8"
    "@types/randombytes" "^2.0.0"
    "@types/urijs" "^1.19.6"
    axios "^0.19.0"
    bignumber.js "^4.0.0"
    detect-node "^2.0.4"
    es6-promise "^4.2.4"
    eventsource "^1.0.7"
    lodash "^4.17.11"
    randombytes "^2.1.0"
    stellar-base "^4.0.0"
    toml "^2.3.0"
    tslib "^1.10.0"
    urijs "^1.19.1"
    utility-types "^3.7.0"

stream-events@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/stream-events/-/stream-events-1.0.5.tgz"
  integrity sha512-E1GUzBSgvct8Jsb3v2X15pjzN1tYebtbLaMg+eBOUOAxgbLoSbT2NS91ckc5lJD1KfLjId+jXJRgo0qnV5Nerg==
  dependencies:
    stubs "^3.0.0"

stream-shift@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/stream-shift/-/stream-shift-1.0.3.tgz"
  integrity sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

stubs@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/stubs/-/stubs-3.0.0.tgz"
  integrity sha512-PdHt7hHUJKxvTCgbKX9C1V/ftOcjJQgz8BZwNfV5c4B6dcGqlpelTbJ999jBGZ2jYiPAwcX5dP6oBwVlBlUbxw==

supports-color@^5.5.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^9.4.0:
  version "9.4.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-9.4.0.tgz"
  integrity sha512-VL+lNrEoIXww1coLPOmiEmK/0sGigko5COxI09KzHc2VJXJsQ37UaQ+8quuxjDeA7+KnLGTWRyOXSLLR2Wb4jw==

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

tar@^6.1.11, tar@^6.1.2, tar@^6.2.0:
  version "6.2.0"
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

tarn@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/tarn/-/tarn-3.0.2.tgz"
  integrity sha512-51LAVKUSZSVfI05vjPESNc5vwqqZpbXCsU+/+wxlOrUjk2SnFTt97v9ZgQrD4YmxYW1Px6w2KjaDitCfkvgxMQ==

teeny-request@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/teeny-request/-/teeny-request-9.0.0.tgz"
  integrity sha512-resvxdc6Mgb7YEThw6G6bExlXKkv6+YbuzGg9xuXxSgxJF7Ozs+o8Y9+2R3sArdWdW8nOokoQb1yrpFB0pQK2g==
  dependencies:
    http-proxy-agent "^5.0.0"
    https-proxy-agent "^5.0.0"
    node-fetch "^2.6.9"
    stream-events "^1.0.5"
    uuid "^9.0.0"

text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

tildify@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/tildify/-/tildify-2.0.0.tgz"
  integrity sha512-Cc+OraorugtXNfs50hU9KS369rFXCfgGLpfCfvlc+Ud5u6VWmUQsOAa9HbTvheQdYnrdJqqv1e5oIqXppMYnSw==

tiny-relative-date@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/tiny-relative-date/-/tiny-relative-date-1.3.0.tgz"
  integrity sha512-MOQHpzllWxDCHHaDno30hhLfbouoYlOI8YlMNtvKe1zXbjEVhbcEovQxvZrPvtiYW630GQDoMMarCnjfyfHA+A==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

toml@^2.3.0:
  version "2.3.6"
  resolved "https://registry.npmjs.org/toml/-/toml-2.3.6.tgz"
  integrity sha512-gVweAectJU3ebq//Ferr2JUY4WKSDe5N+z0FvjDncLGyHmIDoxgY/2Ie4qfEIDm4IS7OA6Rmdm7pdEEdMcV/xQ==

topo@3.x.x:
  version "3.0.3"
  resolved "https://registry.npmjs.org/topo/-/topo-3.0.3.tgz"
  integrity sha512-IgpPtvD4kjrJ7CRA3ov2FhWQADwv+Tdqbsf1ZnPUSAtCJ9e1Z44MmoSGDXGk4IppoZA7jd/QRkNddlLJWlUZsQ==
  dependencies:
    hoek "6.x.x"

toposort-class@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toposort-class/-/toposort-class-1.0.1.tgz"
  integrity sha512-OsLcGGbYF3rMjPUf8oKktyvCiUxSbqMMS39m33MAjLTC1DVIH6x3WSt63/M77ihI09+Sdfk1AXvfhCEeUmC7mg==

touch@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/touch/-/touch-3.1.0.tgz"
  integrity sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==
  dependencies:
    nopt "~1.0.10"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

treeverse@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/treeverse/-/treeverse-3.0.0.tgz"
  integrity sha512-gcANaAnd2QDZFmHFEOF4k7uc1J/6a6z3DJMd/QwEyxLoKGiptJRwid582r7QIsFlFMIZ3SnxfS52S4hm2DHkuQ==

ts-node@^10.9.2:
  version "10.9.2"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tslib@^1.10.0:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.1.0:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tuf-js@^2.2.0:
  version "2.2.0"
  dependencies:
    "@tufjs/models" "2.0.0"
    debug "^4.3.4"
    make-fetch-happen "^13.0.0"

tweetnacl@^1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tweetnacl/-/tweetnacl-1.0.3.tgz"
  integrity sha512-6rt+RN7aOi1nGMyC4Xa5DdYiukl2UWCbcJft7YhxReBGQD7OAM8Pbxw6YMo4r2diNEA8FEmu32YOn9rhaiE5yw==

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typescript@^5.3.3, typescript@>=2.7:
  version "5.3.3"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.3.3.tgz"
  integrity sha512-pXWcraxM0uxAS+tN0AG/BF2TyqmHO014Z070UsJ+pFvYuRSq8KH8DmWpnbXe0pEPDHXZV3FcAbJkijJ5oNEnWw==

undefsafe@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz"
  integrity sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==

undici-types@~6.20.0:
  version "6.20.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz"
  integrity sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==

undici-types@~6.21.0:
  version "6.21.0"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  integrity sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==

unique-filename@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/unique-filename/-/unique-filename-3.0.0.tgz"
  integrity sha512-afXhuC55wkAmZ0P18QsVE6kp8JaxrEokN2HGIoIVv2ijHQd419H0+6EigAFcIzXeMIkcIkNBpB3L/DXB3cTS/g==
  dependencies:
    unique-slug "^4.0.0"

unique-slug@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/unique-slug/-/unique-slug-4.0.0.tgz"
  integrity sha512-WrcA6AyEfqDX5bWige/4NQfPZMtASNVxdmWR76WESYQVAACSgWcR6e9i0mofqqBxYFtL4oAxPIptY73/0YE1DQ==
  dependencies:
    imurmurhash "^0.1.4"

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

urijs@^1.19.1:
  version "1.19.11"
  resolved "https://registry.npmjs.org/urijs/-/urijs-1.19.11.tgz"
  integrity sha512-HXgFDgDommxn5/bIv0cnQZsPhHDA90NPHD6+c/v21U5+Sx5hoP8+dP9IZXBU1gIfvdRfhG8cel9QNPeionfcCQ==

url-template@^2.0.8:
  version "2.0.8"
  resolved "https://registry.npmjs.org/url-template/-/url-template-2.0.8.tgz"
  integrity sha512-XdVKMF4SJ0nP/O7XIPB0JwAEuT9lDIYnNsK8yGVe43y0AWoKeJNdv3ZNWh7ksJ6KqQFjOO6ox/VEitLnaVNufw==

url@0.10.3:
  version "0.10.3"
  resolved "https://registry.npmjs.org/url/-/url-0.10.3.tgz"
  integrity sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

util-deprecate@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

util@^0.12.4:
  version "0.12.5"
  resolved "https://registry.npmjs.org/util/-/util-0.12.5.tgz"
  integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

utility-types@^3.7.0:
  version "3.11.0"
  resolved "https://registry.npmjs.org/utility-types/-/utility-types-3.11.0.tgz"
  integrity sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==

uuid@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==

uuid@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz"
  integrity sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==

uuid@^8.0.0:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

uuid@^9.0.0:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

uuid@^9.0.1:
  version "9.0.1"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  integrity sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==

uuid@8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz"
  integrity sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==

uuid@8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

validate-npm-package-license@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^5.0.0:
  version "5.0.0"
  dependencies:
    builtins "^5.0.0"

validator@^13.9.0:
  version "13.11.0"
  resolved "https://registry.npmjs.org/validator/-/validator-13.11.0.tgz"
  integrity sha512-Ii+sehpSfZy+At5nPdnyMhx78fEoPDkR2XW/zimHEL3MyGJQOCQ7WeP20jPYRz7ZCpcKLB21NxuXHF3bxjStBQ==

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

walk-up-path@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/walk-up-path/-/walk-up-path-3.0.1.tgz"
  integrity sha512-9YlCL/ynK3CTlrSRrDxZvUauLzAswPCrsaCgilqFevUYpeEW0/3ScEjaa3kbW/T0ghhkEr7mv+fpjqn1Y1YuTA==

wcwidth@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-module@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz"
  integrity sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==

which-typed-array@^1.1.11, which-typed-array@^1.1.2:
  version "1.1.13"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.13.tgz"
  integrity sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.4"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

which@^2.0.1:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

which@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/which/-/which-4.0.0.tgz"
  integrity sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==
  dependencies:
    isexe "^3.1.1"

wide-align@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

wkx@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/wkx/-/wkx-0.5.0.tgz"
  integrity sha512-Xng/d4Ichh8uN4l0FToV/258EjMGU9MGcA0HV2d9B/ZpZB3lqQm7nkOdZdm5GhKtLLhAE7PiVQwN4eN+2YJJUg==
  dependencies:
    "@types/node" "*"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^5.0.0, write-file-atomic@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz"
  integrity sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

ws@^7.4.6:
  version "7.5.10"
  resolved "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz"
  integrity sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==

xml2js@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/xml2js/-/xml2js-0.5.0.tgz"
  integrity sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xml2json@^0.12.0:
  version "0.12.0"
  resolved "https://registry.npmjs.org/xml2json/-/xml2json-0.12.0.tgz"
  integrity sha512-EPJHRWJnJUYbJlzR4pBhZODwWdi2IaYGtDdteJi0JpZ4OD31IplWALuit8r73dJuM4iHZdDVKY1tLqY2UICejg==
  dependencies:
    hoek "^4.2.1"
    joi "^13.1.2"
    node-expat "^2.3.18"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmjs.org/y18n/-/y18n-4.0.3.tgz"
  integrity sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^4.0.0, yallist@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz"
  integrity sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npmjs.org/yargs/-/yargs-15.4.1.tgz"
  integrity sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^17.7.2:
  version "17.7.2"
  resolved "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==

zod@^3.24.2:
  version "3.24.2"
  resolved "https://registry.npmjs.org/zod/-/zod-3.24.2.tgz"
  integrity sha512-lY7CDW43ECgW9u1TcT3IoXHflywfVqDYze4waEz812jR/bZ8FHDsl7pFQoSZTz5N+2NqRXs8GBwnAwo3ZNxqhQ==

import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('third_party_accounts');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('third_party_accounts', (table) => {
      table.bigIncrements('id').unsigned().primary(); // bigint UNSIGNED NOT NULL AUTO_INCREMENT
      table.string('service_name', 100).notNullable().unique();
      table.string('account_name', 100).nullable();
      table.string('primary_email', 150).nullable();
      table.string('secondary_email', 150).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['account_name']);
      table.index(['primary_email']);
      table.index(['secondary_email']);
      table.index(['created_at']);
      table.index(['updated_at']);
    });
    
    console.log('✅ Created third_party_accounts table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table third_party_accounts exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM third_party_accounts');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.bigIncrements('id').unsigned().primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('service_name')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.string('service_name', 100).notNullable().unique();
    });
    console.log('✅ Added service_name field');
  }
  
  if (!existingColumns.includes('account_name')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.string('account_name', 100).nullable();
    });
    console.log('✅ Added account_name field');
  }
  
  if (!existingColumns.includes('primary_email')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.string('primary_email', 150).nullable();
    });
    console.log('✅ Added primary_email field');
  }
  
  if (!existingColumns.includes('secondary_email')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.string('secondary_email', 150).nullable();
    });
    console.log('✅ Added secondary_email field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM third_party_accounts');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('third_party_accounts_account_name_index')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['account_name']);
    });
    console.log('✅ Added account_name index');
  }
  
  if (!existingIndexes.includes('third_party_accounts_primary_email_index')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['primary_email']);
    });
    console.log('✅ Added primary_email index');
  }
  
  if (!existingIndexes.includes('third_party_accounts_secondary_email_index')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['secondary_email']);
    });
    console.log('✅ Added secondary_email index');
  }
  
  if (!existingIndexes.includes('third_party_accounts_created_at_index')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('third_party_accounts_updated_at_index')) {
    await knex.schema.alterTable('third_party_accounts', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  // Check for unique constraints
  if (!existingIndexes.includes('service_name')) {
    try {
      await knex.raw('ALTER TABLE third_party_accounts ADD UNIQUE KEY service_name (service_name)');
      console.log('✅ Added unique constraint on service_name');
    } catch (error) {
      console.log('⚠️  Unique constraint on service_name already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for third_party_accounts table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('stellar_swaps');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('stellar_swaps', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('swap_id', 255).notNullable().unique();
      table.string('client_id', 255).notNullable();
      table.string('source_asset', 20).notNullable();
      table.string('source_issuer', 255).nullable();
      table.string('destination_asset', 20).notNullable();
      table.string('destination_issuer', 255).nullable();
      table.decimal('source_amount', 20, 7).notNullable();
      table.decimal('destination_amount', 20, 7).notNullable();
      table.string('stellar_hash', 255).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['client_id'], 'idx_client_swaps');
      table.index(['created_at'], 'idx_created_at');
      table.index(['source_asset']);
      table.index(['destination_asset']);
      table.index(['source_issuer']);
      table.index(['destination_issuer']);
      table.index(['stellar_hash']);
    });
    
    console.log('✅ Created stellar_swaps table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table stellar_swaps exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM stellar_swaps');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('swap_id')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('swap_id', 255).notNullable().unique();
    });
    console.log('✅ Added swap_id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('client_id', 255).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('source_asset')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('source_asset', 20).notNullable();
    });
    console.log('✅ Added source_asset field');
  }
  
  if (!existingColumns.includes('source_issuer')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('source_issuer', 255).nullable();
    });
    console.log('✅ Added source_issuer field');
  }
  
  if (!existingColumns.includes('destination_asset')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('destination_asset', 20).notNullable();
    });
    console.log('✅ Added destination_asset field');
  }
  
  if (!existingColumns.includes('destination_issuer')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('destination_issuer', 255).nullable();
    });
    console.log('✅ Added destination_issuer field');
  }
  
  if (!existingColumns.includes('source_amount')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.decimal('source_amount', 20, 7).notNullable();
    });
    console.log('✅ Added source_amount field');
  }
  
  if (!existingColumns.includes('destination_amount')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.decimal('destination_amount', 20, 7).notNullable();
    });
    console.log('✅ Added destination_amount field');
  }
  
  if (!existingColumns.includes('stellar_hash')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.string('stellar_hash', 255).nullable();
    });
    console.log('✅ Added stellar_hash field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM stellar_swaps');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('idx_client_swaps')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['client_id'], 'idx_client_swaps');
    });
    console.log('✅ Added idx_client_swaps index');
  }
  
  if (!existingIndexes.includes('idx_created_at')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['created_at'], 'idx_created_at');
    });
    console.log('✅ Added idx_created_at index');
  }
  
  if (!existingIndexes.includes('stellar_swaps_source_asset_index')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['source_asset']);
    });
    console.log('✅ Added source_asset index');
  }
  
  if (!existingIndexes.includes('stellar_swaps_destination_asset_index')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['destination_asset']);
    });
    console.log('✅ Added destination_asset index');
  }
  
  if (!existingIndexes.includes('stellar_swaps_source_issuer_index')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['source_issuer']);
    });
    console.log('✅ Added source_issuer index');
  }
  
  if (!existingIndexes.includes('stellar_swaps_destination_issuer_index')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['destination_issuer']);
    });
    console.log('✅ Added destination_issuer index');
  }
  
  if (!existingIndexes.includes('stellar_swaps_stellar_hash_index')) {
    await knex.schema.alterTable('stellar_swaps', (table) => {
      table.index(['stellar_hash']);
    });
    console.log('✅ Added stellar_hash index');
  }
  
  // Check for unique constraint on swap_id
  if (!existingIndexes.includes('swap_id')) {
    try {
      await knex.raw('ALTER TABLE stellar_swaps ADD UNIQUE KEY swap_id (swap_id)');
      console.log('✅ Added unique constraint on swap_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on swap_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for stellar_swaps table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
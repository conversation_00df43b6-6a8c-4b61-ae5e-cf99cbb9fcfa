import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('stellar_assets');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('stellar_assets', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('asset_code', 20).notNullable();
      table.string('asset_issuer', 255).nullable();
      table.enum('asset_type', ['native', 'credit_alphanum4', 'credit_alphanum12']).defaultTo('credit_alphanum4');
      table.boolean('is_active').defaultTo(true); // tinyint(1) DEFAULT '1'
      table.string('display_name', 100).nullable();
      table.text('description').nullable();
      table.string('home_domain', 255).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['is_active'], 'idx_active_assets');
      table.index(['asset_code']);
      table.index(['asset_issuer']);
      table.index(['asset_type']);
      table.index(['display_name']);
      table.index(['home_domain']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created stellar_assets table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table stellar_assets exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM stellar_assets');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.string('asset_code', 20).notNullable();
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('asset_issuer')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.string('asset_issuer', 255).nullable();
    });
    console.log('✅ Added asset_issuer field');
  }
  
  if (!existingColumns.includes('asset_type')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.enum('asset_type', ['native', 'credit_alphanum4', 'credit_alphanum12']).defaultTo('credit_alphanum4');
    });
    console.log('✅ Added asset_type field');
  }
  
  if (!existingColumns.includes('is_active')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.boolean('is_active').defaultTo(true);
    });
    console.log('✅ Added is_active field');
  }
  
  if (!existingColumns.includes('display_name')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.string('display_name', 100).nullable();
    });
    console.log('✅ Added display_name field');
  }
  
  if (!existingColumns.includes('description')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.text('description').nullable();
    });
    console.log('✅ Added description field');
  }
  
  if (!existingColumns.includes('home_domain')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.string('home_domain', 255).nullable();
    });
    console.log('✅ Added home_domain field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM stellar_assets');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('idx_active_assets')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['is_active'], 'idx_active_assets');
    });
    console.log('✅ Added idx_active_assets index');
  }
  
  if (!existingIndexes.includes('stellar_assets_asset_code_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('stellar_assets_asset_issuer_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['asset_issuer']);
    });
    console.log('✅ Added asset_issuer index');
  }
  
  if (!existingIndexes.includes('stellar_assets_asset_type_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['asset_type']);
    });
    console.log('✅ Added asset_type index');
  }
  
  if (!existingIndexes.includes('stellar_assets_display_name_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['display_name']);
    });
    console.log('✅ Added display_name index');
  }
  
  if (!existingIndexes.includes('stellar_assets_home_domain_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['home_domain']);
    });
    console.log('✅ Added home_domain index');
  }
  
  if (!existingIndexes.includes('stellar_assets_created_at_index')) {
    await knex.schema.alterTable('stellar_assets', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraint on (asset_code, asset_issuer)
  if (!existingIndexes.includes('unique_asset')) {
    try {
      await knex.raw('ALTER TABLE stellar_assets ADD UNIQUE KEY unique_asset (asset_code, asset_issuer)');
      console.log('✅ Added unique constraint on (asset_code, asset_issuer)');
    } catch (error) {
      console.log('⚠️  Unique constraint on (asset_code, asset_issuer) already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for stellar_assets table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
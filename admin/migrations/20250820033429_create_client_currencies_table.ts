import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('client_currencies');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('client_currencies', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('client_id', 50).notNullable();
      table.string('currency', 3).notNullable();
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('client_currencies', (table) => {
      table.index(['client_id']);
      table.index(['currency']);
      table.index(['status']);
      table.unique(['client_id', 'currency']); // Composite unique constraint
    });
    
    console.log('✅ Created client_currencies table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table client_currencies exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM client_currencies');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.string('currency', 3).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    console.log('✅ Added status field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM client_currencies');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('client_currencies_client_id_index')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('client_currencies_currency_index')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('client_currencies_status_index')) {
    await knex.schema.alterTable('client_currencies', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  // Check for composite unique constraint on client_id + currency
  if (!existingIndexes.includes('client_currencies_client_id_currency_unique')) {
    try {
      await knex.raw('ALTER TABLE client_currencies ADD UNIQUE KEY client_currencies_client_id_currency_unique (client_id, currency)');
      console.log('✅ Added composite unique constraint on client_id + currency');
    } catch (error) {
      console.log('⚠️  Composite unique constraint already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for client_currencies table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
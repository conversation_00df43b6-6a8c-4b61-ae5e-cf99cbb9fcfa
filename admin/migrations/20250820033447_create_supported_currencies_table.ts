import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('supported_currencies');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('supported_currencies', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('asset_name', 40).nullable();
      table.string('asset_code', 10).notNullable();
      table.string('asset_issuer', 90).notNullable();
      table.string('currency', 3).notNullable();
      table.enum('asset_type', ['fiat', 'stableCoin', 'collections']).notNullable().defaultTo('fiat');
      table.text('icon').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['asset_code']);
      table.index(['asset_issuer']);
      table.index(['currency']);
      table.index(['asset_type']);
      table.index(['asset_name']);
    });
    
    console.log('✅ Created supported_currencies table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table supported_currencies exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM supported_currencies');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('asset_name')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.string('asset_name', 40).nullable();
    });
    console.log('✅ Added asset_name field');
  }
  
  if (!existingColumns.includes('asset_code')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.string('asset_code', 10).notNullable();
    });
    console.log('✅ Added asset_code field');
  }
  
  if (!existingColumns.includes('asset_issuer')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.string('asset_issuer', 90).notNullable();
    });
    console.log('✅ Added asset_issuer field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.string('currency', 3).notNullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('asset_type')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.enum('asset_type', ['fiat', 'stableCoin', 'collections']).notNullable().defaultTo('fiat');
    });
    console.log('✅ Added asset_type field');
  }
  
  if (!existingColumns.includes('icon')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.text('icon').nullable();
    });
    console.log('✅ Added icon field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM supported_currencies');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('supported_currencies_asset_code_index')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['asset_code']);
    });
    console.log('✅ Added asset_code index');
  }
  
  if (!existingIndexes.includes('supported_currencies_asset_issuer_index')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['asset_issuer']);
    });
    console.log('✅ Added asset_issuer index');
  }
  
  if (!existingIndexes.includes('supported_currencies_currency_index')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('supported_currencies_asset_type_index')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['asset_type']);
    });
    console.log('✅ Added asset_type index');
  }
  
  if (!existingIndexes.includes('supported_currencies_asset_name_index')) {
    await knex.schema.alterTable('supported_currencies', (table) => {
      table.index(['asset_name']);
    });
    console.log('✅ Added asset_name index');
  }
  
  console.log('✅ Field check complete for supported_currencies table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
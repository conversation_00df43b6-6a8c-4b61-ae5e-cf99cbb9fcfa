import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('api_logs');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('api_logs', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('client_id', 50).notNullable();
      table.string('user_id', 100).nullable();
      table.text('body').notNullable();
      table.string('ip', 30).nullable();
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('api_logs', (table) => {
      table.index(['client_id']);
      table.index(['user_id']);
      table.index(['ip']);
      table.index(['created_on']);
    });
    
    console.log('✅ Created api_logs table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table api_logs exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM api_logs');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('user_id')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.string('user_id', 100).nullable();
    });
    console.log('✅ Added user_id field');
  }
  
  if (!existingColumns.includes('body')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.text('body').notNullable();
    });
    console.log('✅ Added body field');
  }
  
  if (!existingColumns.includes('ip')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.string('ip', 30).nullable();
    });
    console.log('✅ Added ip field');
  }
  
  if (!existingColumns.includes('created_on')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.timestamp('created_on').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_on field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM api_logs');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('api_logs_client_id_index')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('api_logs_user_id_index')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.index(['user_id']);
    });
    console.log('✅ Added user_id index');
  }
  
  if (!existingIndexes.includes('api_logs_ip_index')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.index(['ip']);
    });
    console.log('✅ Added ip index');
  }
  
  if (!existingIndexes.includes('api_logs_created_on_index')) {
    await knex.schema.alterTable('api_logs', (table) => {
      table.index(['created_on']);
    });
    console.log('✅ Added created_on index');
  }
  
  console.log('✅ Field check complete for api_logs table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
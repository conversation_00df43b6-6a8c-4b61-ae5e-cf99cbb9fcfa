import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('whitelisted_addresses');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('whitelisted_addresses', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 100).notNullable();
      table.string('address', 255).notNullable();
      table.string('chain', 50).notNullable();
      table.string('tag', 255).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['client_id']);
      table.index(['address']);
      table.index(['chain']);
      table.index(['tag']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created whitelisted_addresses table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table whitelisted_addresses exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM whitelisted_addresses');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.string('client_id', 100).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('address')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.string('address', 255).notNullable();
    });
    console.log('✅ Added address field');
  }
  
  if (!existingColumns.includes('chain')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.string('chain', 50).notNullable();
    });
    console.log('✅ Added chain field');
  }
  
  if (!existingColumns.includes('tag')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.string('tag', 255).nullable();
    });
    console.log('✅ Added tag field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM whitelisted_addresses');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('whitelisted_addresses_client_id_index')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('whitelisted_addresses_address_index')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['address']);
    });
    console.log('✅ Added address index');
  }
  
  if (!existingIndexes.includes('whitelisted_addresses_chain_index')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['chain']);
    });
    console.log('✅ Added chain index');
  }
  
  if (!existingIndexes.includes('whitelisted_addresses_tag_index')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['tag']);
    });
    console.log('✅ Added tag index');
  }
  
  if (!existingIndexes.includes('whitelisted_addresses_created_at_index')) {
    await knex.schema.alterTable('whitelisted_addresses', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for whitelisted_addresses table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('webhook_logs');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('webhook_logs', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('cl_id', 50).nullable();
      table.string('webhook_url', 255).nullable();
      table.string('client_id', 50).nullable();
      table.string('trans_id', 50).nullable();
      table.text('webhook_data').nullable();
      table.string('event', 40).nullable();
      table.string('status_code', 40).nullable();
      table.string('response_code', 40).nullable();
      table.text('response_data').nullable();
      table.text('response').nullable();
      table.date('updated_at').nullable();
      table.string('timestamp', 40).notNullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['cl_id']);
      table.index(['webhook_url']);
      table.index(['client_id']);
      table.index(['trans_id']);
      table.index(['event']);
      table.index(['status_code']);
      table.index(['response_code']);
      table.index(['updated_at']);
      table.index(['timestamp']);
    });
    
    console.log('✅ Created webhook_logs table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table webhook_logs exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM webhook_logs');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('cl_id')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('cl_id', 50).nullable();
    });
    console.log('✅ Added cl_id field');
  }
  
  if (!existingColumns.includes('webhook_url')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('webhook_url', 255).nullable();
    });
    console.log('✅ Added webhook_url field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('client_id', 50).nullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('trans_id')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('trans_id', 50).nullable();
    });
    console.log('✅ Added trans_id field');
  }
  
  if (!existingColumns.includes('webhook_data')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.text('webhook_data').nullable();
    });
    console.log('✅ Added webhook_data field');
  }
  
  if (!existingColumns.includes('event')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('event', 40).nullable();
    });
    console.log('✅ Added event field');
  }
  
  if (!existingColumns.includes('status_code')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('status_code', 40).nullable();
    });
    console.log('✅ Added status_code field');
  }
  
  if (!existingColumns.includes('response_code')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('response_code', 40).nullable();
    });
    console.log('✅ Added response_code field');
  }
  
  if (!existingColumns.includes('response_data')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.text('response_data').nullable();
    });
    console.log('✅ Added response_data field');
  }
  
  if (!existingColumns.includes('response')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.text('response').nullable();
    });
    console.log('✅ Added response field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.date('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('timestamp')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.string('timestamp', 40).notNullable();
    });
    console.log('✅ Added timestamp field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM webhook_logs');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('webhook_logs_cl_id_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['cl_id']);
    });
    console.log('✅ Added cl_id index');
  }
  
  if (!existingIndexes.includes('webhook_logs_webhook_url_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['webhook_url']);
    });
    console.log('✅ Added webhook_url index');
  }
  
  if (!existingIndexes.includes('webhook_logs_client_id_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('webhook_logs_trans_id_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['trans_id']);
    });
    console.log('✅ Added trans_id index');
  }
  
  if (!existingIndexes.includes('webhook_logs_event_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['event']);
    });
    console.log('✅ Added event index');
  }
  
  if (!existingIndexes.includes('webhook_logs_status_code_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['status_code']);
    });
    console.log('✅ Added status_code index');
  }
  
  if (!existingIndexes.includes('webhook_logs_response_code_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['response_code']);
    });
    console.log('✅ Added response_code index');
  }
  
  if (!existingIndexes.includes('webhook_logs_updated_at_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  if (!existingIndexes.includes('webhook_logs_timestamp_index')) {
    await knex.schema.alterTable('webhook_logs', (table) => {
      table.index(['timestamp']);
    });
    console.log('✅ Added timestamp index');
  }
  
  console.log('✅ Field check complete for webhook_logs table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
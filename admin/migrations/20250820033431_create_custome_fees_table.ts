import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('custome_fees');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('custome_fees', (table) => {
      table.string('id', 36).primary(); // char(36) for UUID
      table.string('client_id', 100).nullable();
      table.integer('product_id').notNullable().unique();
      table.enum('fee_type', ['PERCENTAGE', 'FLAT', 'TIER']).notNullable();
      table.decimal('amount', 150, 2).nullable(); // double(150,2) equivalent
      table.decimal('max_amount', 150, 2).nullable();
      table.boolean('active_status').defaultTo(true); // tinyint(1) equivalent
      table.timestamp('deleted_at').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['client_id']);
      table.index(['fee_type']);
      table.index(['active_status']);
      table.index(['created_at']);
      table.index(['updated_at']);
    });
    
    console.log('✅ Created custome_fees table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table custome_fees exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM custome_fees');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.string('client_id', 100).nullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('product_id')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.integer('product_id').notNullable().unique();
    });
    console.log('✅ Added product_id field');
  }
  
  if (!existingColumns.includes('fee_type')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.enum('fee_type', ['percentage', 'flat']).notNullable();
    });
    console.log('✅ Added fee_type field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.decimal('amount', 150, 2).nullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('active_status')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.boolean('active_status').defaultTo(true);
    });
    console.log('✅ Added active_status field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM custome_fees');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('custome_fees_client_id_index')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('custome_fees_fee_type_index')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['fee_type']);
    });
    console.log('✅ Added fee_type index');
  }
  
  if (!existingIndexes.includes('custome_fees_active_status_index')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['active_status']);
    });
    console.log('✅ Added active_status index');
  }
  
  if (!existingIndexes.includes('custome_fees_created_at_index')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('custome_fees_updated_at_index')) {
    await knex.schema.alterTable('custome_fees', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  // Check for unique constraint on product_id
  if (!existingIndexes.includes('product_id')) {
    try {
      await knex.raw('ALTER TABLE custome_fees ADD UNIQUE KEY product_id (product_id)');
      console.log('✅ Added unique constraint on product_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on product_id already exists or could not be added');
    }
  }
  
  // Check for foreign key constraint on product_id
  try {
    const foreignKeys = await knex.raw(`
      SELECT CONSTRAINT_NAME 
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'custome_fees' 
      AND REFERENCED_TABLE_NAME = 'products'
      AND REFERENCED_COLUMN_NAME = 'product_id'
    `);
    
    if (foreignKeys[0].length === 0) {
      await knex.raw(`
        ALTER TABLE custome_fees 
        ADD CONSTRAINT PRODUCT 
        FOREIGN KEY (product_id) 
        REFERENCES products(product_id) 
        ON DELETE RESTRICT 
        ON UPDATE RESTRICT
      `);
      console.log('✅ Added foreign key constraint on product_id');
    } else {
      console.log('⚠️  Foreign key constraint already exists');
    }
  } catch (error) {
    console.log('⚠️  Foreign key constraint could not be added or already exists');
  }
  
  console.log('✅ Field check complete for custome_fees table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
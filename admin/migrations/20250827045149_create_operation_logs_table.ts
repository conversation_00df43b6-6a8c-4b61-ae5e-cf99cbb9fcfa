import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('operation_logs');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('operation_logs', (table) => {
      table.string('id', 36).primary().defaultTo(knex.raw('UUID()')); // CHAR(36) PRIMARY KEY DEFAULT (UUID())
      table.enum('operation_type', ['INSERT', 'UPDATE', 'DELETE', 'SELECT', 'LOGIN', 'LOGOUT', 'OTHER']).notNullable(); // ENUM NOT NULL
      table.string('table_name', 100).notNullable(); // VARCHAR(100) NOT NULL
      table.string('record_id', 100).nullable(); // VARCHAR(100) DEFAULT NULL
      table.string('executed_by', 100).nullable(); // VARCHAR(100) DEFAULT NULL
      table.text('operation_query').nullable(); // TEXT
      table.json('old_data').nullable(); // JSON DEFAULT NULL
      table.json('new_data').nullable(); // JSON DEFAULT NULL
      table.string('ip_address', 45).nullable(); // VARCHAR(45) DEFAULT NULL
      table.timestamp('created_at').defaultTo(knex.fn.now()); // TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['operation_type']);
      table.index(['table_name']);
      table.index(['record_id']);
      table.index(['executed_by']);
      table.index(['ip_address']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created operation_logs table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table operation_logs exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM operation_logs');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.string('id', 36).primary().defaultTo(knex.raw('UUID()'));
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('operation_type')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.enum('operation_type', ['INSERT', 'UPDATE', 'DELETE', 'SELECT', 'LOGIN', 'LOGOUT', 'OTHER']).notNullable();
    });
    console.log('✅ Added operation_type field');
  }
  
  if (!existingColumns.includes('table_name')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.string('table_name', 100).notNullable();
    });
    console.log('✅ Added table_name field');
  }
  
  if (!existingColumns.includes('record_id')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.string('record_id', 100).nullable();
    });
    console.log('✅ Added record_id field');
  }
  
  if (!existingColumns.includes('executed_by')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.string('executed_by', 100).nullable();
    });
    console.log('✅ Added executed_by field');
  }
  
  if (!existingColumns.includes('operation_query')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.text('operation_query').nullable();
    });
    console.log('✅ Added operation_query field');
  }
  
  if (!existingColumns.includes('old_data')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.json('old_data').nullable();
    });
    console.log('✅ Added old_data field');
  }
  
  if (!existingColumns.includes('new_data')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.json('new_data').nullable();
    });
    console.log('✅ Added new_data field');
  }
  
  if (!existingColumns.includes('ip_address')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.string('ip_address', 45).nullable();
    });
    console.log('✅ Added ip_address field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM operation_logs');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('operation_logs_operation_type_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['operation_type']);
    });
    console.log('✅ Added operation_type index');
  }
  
  if (!existingIndexes.includes('operation_logs_table_name_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['table_name']);
    });
    console.log('✅ Added table_name index');
  }
  
  if (!existingIndexes.includes('operation_logs_record_id_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['record_id']);
    });
    console.log('✅ Added record_id index');
  }
  
  if (!existingIndexes.includes('operation_logs_executed_by_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['executed_by']);
    });
    console.log('✅ Added executed_by index');
  }
  
  if (!existingIndexes.includes('operation_logs_ip_address_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['ip_address']);
    });
    console.log('✅ Added ip_address index');
  }
  
  if (!existingIndexes.includes('operation_logs_created_at_index')) {
    await knex.schema.alterTable('operation_logs', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for operation_logs table');
}

export async function down(knex: Knex): Promise<void> {
  // Drop the table if it exists
  const tableExists = await knex.schema.hasTable('operation_logs');
  
  if (tableExists) {
    await knex.schema.dropTable('operation_logs');
    console.log('✅ Dropped operation_logs table');
  } else {
    console.log('⚠️  operation_logs table does not exist, nothing to drop');
  }
}

import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('utilia_networks');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('utilia_networks', (table) => {
      table.bigIncrements('id').unsigned().primary(); // bigint UNSIGNED NOT NULL AUTO_INCREMENT
      table.string('name', 255).notNullable();
      table.string('displayName', 255).notNullable();
      table.text('caipDetails').notNullable();
      table.boolean('testnet').notNullable().defaultTo(false); // tinyint(1) NOT NULL DEFAULT '0'
      table.string('nativeAsset', 255).nullable();
      table.boolean('custom').notNullable().defaultTo(false); // tinyint(1) NOT NULL DEFAULT '0'
      table.integer('is_muda_supported').notNullable().defaultTo(0);
      table.timestamp('createdAt').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['name']);
      table.index(['displayName']);
      table.index(['testnet']);
      table.index(['nativeAsset']);
      table.index(['custom']);
      table.index(['is_muda_supported']);
      table.index(['createdAt']);
    });
    
    console.log('✅ Created utilia_networks table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table utilia_networks exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM utilia_networks');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.bigIncrements('id').unsigned().primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('name')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.string('name', 255).notNullable();
    });
    console.log('✅ Added name field');
  }
  
  if (!existingColumns.includes('displayName')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.string('displayName', 255).notNullable();
    });
    console.log('✅ Added displayName field');
  }
  
  if (!existingColumns.includes('caipDetails')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.text('caipDetails').notNullable();
    });
    console.log('✅ Added caipDetails field');
  }
  
  if (!existingColumns.includes('testnet')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.boolean('testnet').notNullable().defaultTo(false);
    });
    console.log('✅ Added testnet field');
  }
  
  if (!existingColumns.includes('nativeAsset')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.string('nativeAsset', 255).nullable();
    });
    console.log('✅ Added nativeAsset field');
  }
  
  if (!existingColumns.includes('custom')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.boolean('custom').notNullable().defaultTo(false);
    });
    console.log('✅ Added custom field');
  }
  
  if (!existingColumns.includes('is_muda_supported')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.integer('is_muda_supported').notNullable().defaultTo(0);
    });
    console.log('✅ Added is_muda_supported field');
  }
  
  if (!existingColumns.includes('createdAt')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.timestamp('createdAt').defaultTo(knex.fn.now());
    });
    console.log('✅ Added createdAt field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM utilia_networks');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('utilia_networks_name_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['name']);
    });
    console.log('✅ Added name index');
  }
  
  if (!existingIndexes.includes('utilia_networks_displayName_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['displayName']);
    });
    console.log('✅ Added displayName index');
  }
  
  if (!existingIndexes.includes('utilia_networks_testnet_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['testnet']);
    });
    console.log('✅ Added testnet index');
  }
  
  if (!existingIndexes.includes('utilia_networks_nativeAsset_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['nativeAsset']);
    });
    console.log('✅ Added nativeAsset index');
  }
  
  if (!existingIndexes.includes('utilia_networks_custom_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['custom']);
    });
    console.log('✅ Added custom index');
  }
  
  if (!existingIndexes.includes('utilia_networks_is_muda_supported_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['is_muda_supported']);
    });
    console.log('✅ Added is_muda_supported index');
  }
  
  if (!existingIndexes.includes('utilia_networks_createdAt_index')) {
    await knex.schema.alterTable('utilia_networks', (table) => {
      table.index(['createdAt']);
    });
    console.log('✅ Added createdAt index');
  }
  
  console.log('✅ Field check complete for utilia_networks table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
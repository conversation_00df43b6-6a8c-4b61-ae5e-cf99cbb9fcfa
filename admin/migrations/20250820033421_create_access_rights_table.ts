import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('access_rights');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('access_rights', (table) => {
      table.string('id', 36).primary(); // char(36) for UUID
      table.text('name').notNullable();
      table.enum('status', ['active', 'inactive']).defaultTo('active');
      table.enum('type', ['client', 'admin']).nullable();
      table.enum('role_group', [
        'transactions',
        'users_&_roles',
        'dashboard',
        'deposits',
        'reports',
        'settings',
        'wallet'
      ]).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').nullable();
      table.timestamp('deleted_at').nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('access_rights', (table) => {
      table.index(['status']);
      table.index(['type']);
      table.index(['role_group']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created access_rights table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table access_rights exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM access_rights');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.string('id', 36).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('name')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.text('name').notNullable();
    });
    console.log('✅ Added name field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.enum('status', ['active', 'inactive']).defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('type')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.enum('type', ['client', 'admin']).nullable();
    });
    console.log('✅ Added type field');
  }
  
  if (!existingColumns.includes('role_group')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.enum('role_group', [
        'transactions',
        'users_&_roles',
        'dashboard',
        'deposits',
        'reports',
        'settings',
        'wallet'
      ]).nullable();
    });
    console.log('✅ Added role_group field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.timestamp('updated_at').nullable();
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM access_rights');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('access_rights_status_index')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('access_rights_type_index')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.index(['type']);
    });
    console.log('✅ Added type index');
  }
  
  if (!existingIndexes.includes('access_rights_role_group_index')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.index(['role_group']);
    });
    console.log('✅ Added role_group index');
  }
  
  if (!existingIndexes.includes('access_rights_created_at_index')) {
    await knex.schema.alterTable('access_rights', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for access_rights table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('notification_templates');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('notification_templates', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('operation', 40).notNullable();
      table.string('title', 100).notNullable();
      table.text('body').notNullable();
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('notification_templates', (table) => {
      table.index(['operation']);
      table.index(['status']);
    });
    
    console.log('✅ Created notification_templates table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table notification_templates exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM notification_templates');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('operation')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.string('operation', 40).notNullable();
    });
    console.log('✅ Added operation field');
  }
  
  if (!existingColumns.includes('title')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.string('title', 100).notNullable();
    });
    console.log('✅ Added title field');
  }
  
  if (!existingColumns.includes('body')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.text('body').notNullable();
    });
    console.log('✅ Added body field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    console.log('✅ Added status field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM notification_templates');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('notification_templates_operation_index')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.index(['operation']);
    });
    console.log('✅ Added operation index');
  }
  
  if (!existingIndexes.includes('notification_templates_status_index')) {
    await knex.schema.alterTable('notification_templates', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  console.log('✅ Field check complete for notification_templates table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}
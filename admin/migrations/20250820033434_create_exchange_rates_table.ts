import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('exchange_rates');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('exchange_rates', (table) => {
      table.string('id', 150).primary(); // varchar(150) NOT NULL
      table.enum('enabled', ['active', 'inactive']).notNullable().defaultTo('active');
      table.boolean('hasCrypto').notNullable().defaultTo(false); // tinyint(1) equivalent
      table.string('referencePrice', 20).nullable();
      table.string('base_currency', 20).nullable();
      table.string('quote_currency', 20).nullable();
      table.string('pair', 10).nullable();
      table.decimal('markup', 10, 2).defaultTo('0.00');
      table.decimal('markdown', 10, 2).defaultTo('0.00');
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      table.timestamp('updated_at').nullable().defaultTo(knex.fn.now());
      table.string('updatedBy', 50).nullable();
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['enabled']);
      table.index(['hasCrypto']);
      table.index(['base_currency']);
      table.index(['quote_currency']);
      table.index(['pair']);
      table.index(['created_at']);
      table.index(['updated_at']);
    });
    
    console.log('✅ Created exchange_rates table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table exchange_rates exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM exchange_rates');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('id', 150).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('enabled')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.enum('enabled', ['active', 'inactive']).notNullable().defaultTo('active');
    });
    console.log('✅ Added enabled field');
  }
  
  if (!existingColumns.includes('hasCrypto')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.boolean('hasCrypto').notNullable().defaultTo(false);
    });
    console.log('✅ Added hasCrypto field');
  }
  
  if (!existingColumns.includes('referencePrice')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('referencePrice', 20).nullable();
    });
    console.log('✅ Added referencePrice field');
  }
  
  if (!existingColumns.includes('base_currency')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('base_currency', 20).nullable();
    });
    console.log('✅ Added base_currency field');
  }
  
  if (!existingColumns.includes('quote_currency')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('quote_currency', 20).nullable();
    });
    console.log('✅ Added quote_currency field');
  }
  
  if (!existingColumns.includes('pair')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('pair', 10).nullable();
    });
    console.log('✅ Added pair field');
  }
  
  if (!existingColumns.includes('markup')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.decimal('markup', 10, 2).defaultTo('0.00');
    });
    console.log('✅ Added markup field');
  }
  
  if (!existingColumns.includes('markdown')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.decimal('markdown', 10, 2).defaultTo('0.00');
    });
    console.log('✅ Added markdown field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.timestamp('updated_at').nullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('updatedBy')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.string('updatedBy', 50).nullable();
    });
    console.log('✅ Added updatedBy field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM exchange_rates');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('exchange_rates_enabled_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['enabled']);
    });
    console.log('✅ Added enabled index');
  }
  
  if (!existingIndexes.includes('exchange_rates_hasCrypto_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['hasCrypto']);
    });
    console.log('✅ Added hasCrypto index');
  }
  
  if (!existingIndexes.includes('exchange_rates_base_currency_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['base_currency']);
    });
    console.log('✅ Added base_currency index');
  }
  
  if (!existingIndexes.includes('exchange_rates_quote_currency_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['quote_currency']);
    });
    console.log('✅ Added quote_currency index');
  }
  
  if (!existingIndexes.includes('exchange_rates_pair_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['pair']);
    });
    console.log('✅ Added pair index');
  }
  
  if (!existingIndexes.includes('exchange_rates_created_at_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  if (!existingIndexes.includes('exchange_rates_updated_at_index')) {
    await knex.schema.alterTable('exchange_rates', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  console.log('✅ Field check complete for exchange_rates table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
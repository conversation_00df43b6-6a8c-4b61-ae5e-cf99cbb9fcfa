import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('client_logins');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('client_logins', (table) => {
      table.increments('id').primary();
      table.string('client_id', 40).notNullable();
      table.string('first_name', 40).nullable();
      table.string('last_name', 50).nullable();
      table.string('email', 255).notNullable().unique();
      table.string('password', 255).notNullable();
      table.string('role', 160).defaultTo('admin');
      table.enum('default_password', ['true', 'false']).notNullable().defaultTo('false');
      table.timestamp('default_password_expiry').nullable();
      table.enum('status', ['pending', 'active', 'inactive']).defaultTo(null);
      table.timestamp('deleted_at').nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['client_id']);
      table.index(['email']);
      table.index(['role']);
      table.index(['status']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created client_logins table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table client_logins exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM client_logins');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('client_id', 40).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('first_name')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('first_name', 40).nullable();
    });
    console.log('✅ Added first_name field');
  }
  
  if (!existingColumns.includes('last_name')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('last_name', 50).nullable();
    });
    console.log('✅ Added last_name field');
  }
  
  if (!existingColumns.includes('email')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('email', 255).notNullable().unique();
    });
    console.log('✅ Added email field');
  }
  
  if (!existingColumns.includes('password')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('password', 255).notNullable();
    });
    console.log('✅ Added password field');
  }
  
  if (!existingColumns.includes('role')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.string('role', 160).defaultTo('admin');
    });
    console.log('✅ Added role field');
  }
  
  if (!existingColumns.includes('default_password')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.enum('default_password', ['true', 'false']).notNullable().defaultTo('false');
    });
    console.log('✅ Added default_password field');
  }
  
  if (!existingColumns.includes('default_password_expiry')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.timestamp('default_password_expiry').nullable();
    });
    console.log('✅ Added default_password_expiry field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.enum('status', ['pending', 'active', 'inactive']).defaultTo(null);
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM client_logins');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('client_logins_client_id_index')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('client_logins_email_index')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['email']);
    });
    console.log('✅ Added email index');
  }
  
  if (!existingIndexes.includes('client_logins_role_index')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['role']);
    });
    console.log('✅ Added role index');
  }
  
  if (!existingIndexes.includes('client_logins_status_index')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('client_logins_created_at_index')) {
    await knex.schema.alterTable('client_logins', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for client_logins table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
}


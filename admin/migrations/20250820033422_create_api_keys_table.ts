import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('api_keys');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('api_keys', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('key_name', 50).nullable();
      table.integer('client_id').notNullable();
      table.string('api_key', 255).notNullable().unique();
      table.string('secret_key', 250).notNullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('api_keys', (table) => {
      table.index(['client_id']);
      table.index(['key_name']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created api_keys table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table api_keys exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM api_keys');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('key_name')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.string('key_name', 50).nullable();
    });
    console.log('✅ Added key_name field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.integer('client_id').notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('api_key')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.string('api_key', 255).notNullable().unique();
    });
    console.log('✅ Added api_key field');
  }
  
  if (!existingColumns.includes('secret_key')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.string('secret_key', 250).notNullable();
    });
    console.log('✅ Added secret_key field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM api_keys');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('api_keys_client_id_index')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('api_keys_key_name_index')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.index(['key_name']);
    });
    console.log('✅ Added key_name index');
  }
  
  if (!existingIndexes.includes('api_keys_created_at_index')) {
    await knex.schema.alterTable('api_keys', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for unique constraint on api_key
  if (!existingIndexes.includes('api_key')) {
    try {
      await knex.raw('ALTER TABLE api_keys ADD UNIQUE KEY api_key (api_key)');
      console.log('✅ Added unique constraint on api_key');
    } catch (error) {
      console.log('⚠️  Unique constraint on api_key already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for api_keys table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('balances_log');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('balances_log', (table) => {
      table.increments('id').primary(); // Auto-incrementing unsigned int primary key
      table.string('provider', 100).notNullable();
      table.string('account', 100).notNullable();
      table.text('balance').notNullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('balances_log', (table) => {
      table.index(['provider']);
      table.index(['account']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created balances_log table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table balances_log exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM balances_log');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('provider')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.string('provider', 100).notNullable();
    });
    console.log('✅ Added provider field');
  }
  
  if (!existingColumns.includes('account')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.string('account', 100).notNullable();
    });
    console.log('✅ Added account field');
  }
  
  if (!existingColumns.includes('balance')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.text('balance').notNullable();
    });
    console.log('✅ Added balance field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM balances_log');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('balances_log_provider_index')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.index(['provider']);
    });
    console.log('✅ Added provider index');
  }
  
  if (!existingIndexes.includes('balances_log_account_index')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.index(['account']);
    });
    console.log('✅ Added account index');
  }
  
  if (!existingIndexes.includes('balances_log_created_at_index')) {
    await knex.schema.alterTable('balances_log', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for balances_log table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
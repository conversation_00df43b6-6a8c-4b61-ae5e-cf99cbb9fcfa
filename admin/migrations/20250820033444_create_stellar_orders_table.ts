import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('stellar_orders');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('stellar_orders', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('order_id', 255).notNullable().unique();
      table.string('client_id', 255).notNullable();
      table.string('stellar_offer_id', 255).nullable();
      table.string('selling_asset', 20).notNullable();
      table.string('selling_issuer', 255).nullable();
      table.string('buying_asset', 20).notNullable();
      table.string('buying_issuer', 255).nullable();
      table.decimal('amount', 20, 7).notNullable();
      table.decimal('price', 20, 7).notNullable();
      table.decimal('filled_amount', 20, 7).defaultTo('0.0000000');
      table.enum('order_type', ['buy', 'sell']).notNullable();
      table.enum('status', ['active', 'completed', 'cancelled', 'partially_filled', 'failed']).defaultTo('active');
      table.string('stellar_hash', 255).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['client_id', 'status'], 'idx_client_orders');
      table.index(['selling_asset', 'buying_asset', 'status'], 'idx_pair_orders');
      table.index(['stellar_offer_id'], 'idx_stellar_offer');
      table.index(['created_at'], 'idx_created_at');
      table.index(['selling_asset']);
      table.index(['buying_asset']);
      table.index(['selling_issuer']);
      table.index(['buying_issuer']);
      table.index(['order_type']);
      table.index(['status']);
      table.index(['stellar_hash']);
      table.index(['updated_at']);
    });
    
    console.log('✅ Created stellar_orders table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table stellar_orders exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM stellar_orders');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('order_id')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('order_id', 255).notNullable().unique();
    });
    console.log('✅ Added order_id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('client_id', 255).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('stellar_offer_id')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('stellar_offer_id', 255).nullable();
    });
    console.log('✅ Added stellar_offer_id field');
  }
  
  if (!existingColumns.includes('selling_asset')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('selling_asset', 20).notNullable();
    });
    console.log('✅ Added selling_asset field');
  }
  
  if (!existingColumns.includes('selling_issuer')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('selling_issuer', 255).nullable();
    });
    console.log('✅ Added selling_issuer field');
  }
  
  if (!existingColumns.includes('buying_asset')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('buying_asset', 20).notNullable();
    });
    console.log('✅ Added buying_asset field');
  }
  
  if (!existingColumns.includes('buying_issuer')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('buying_issuer', 255).nullable();
    });
    console.log('✅ Added buying_issuer field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.decimal('amount', 20, 7).notNullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('price')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.decimal('price', 20, 7).notNullable();
    });
    console.log('✅ Added price field');
  }
  
  if (!existingColumns.includes('filled_amount')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.decimal('filled_amount', 20, 7).defaultTo('0.0000000');
    });
    console.log('✅ Added filled_amount field');
  }
  
  if (!existingColumns.includes('order_type')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.enum('order_type', ['buy', 'sell']).notNullable();
    });
    console.log('✅ Added order_type field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.enum('status', ['active', 'completed', 'cancelled', 'partially_filled', 'failed']).defaultTo('active');
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('stellar_hash')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.string('stellar_hash', 255).nullable();
    });
    console.log('✅ Added stellar_hash field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM stellar_orders');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('idx_client_orders')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['client_id', 'status'], 'idx_client_orders');
    });
    console.log('✅ Added idx_client_orders composite index');
  }
  
  if (!existingIndexes.includes('idx_pair_orders')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['selling_asset', 'buying_asset', 'status'], 'idx_pair_orders');
    });
    console.log('✅ Added idx_pair_orders composite index');
  }
  
  if (!existingIndexes.includes('idx_stellar_offer')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['stellar_offer_id'], 'idx_stellar_offer');
    });
    console.log('✅ Added idx_stellar_offer index');
  }
  
  if (!existingIndexes.includes('idx_created_at')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['created_at'], 'idx_created_at');
    });
    console.log('✅ Added idx_created_at index');
  }
  
  if (!existingIndexes.includes('stellar_orders_selling_asset_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['selling_asset']);
    });
    console.log('✅ Added selling_asset index');
  }
  
  if (!existingIndexes.includes('stellar_orders_buying_asset_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['buying_asset']);
    });
    console.log('✅ Added buying_asset index');
  }
  
  if (!existingIndexes.includes('stellar_orders_selling_issuer_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['selling_issuer']);
    });
    console.log('✅ Added selling_issuer index');
  }
  
  if (!existingIndexes.includes('stellar_orders_buying_issuer_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['buying_issuer']);
    });
    console.log('✅ Added buying_issuer index');
  }
  
  if (!existingIndexes.includes('stellar_orders_order_type_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['order_type']);
    });
    console.log('✅ Added order_type index');
  }
  
  if (!existingIndexes.includes('stellar_orders_status_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('stellar_orders_stellar_hash_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['stellar_hash']);
    });
    console.log('✅ Added stellar_hash index');
  }
  
  if (!existingIndexes.includes('stellar_orders_updated_at_index')) {
    await knex.schema.alterTable('stellar_orders', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  // Check for unique constraint on order_id
  if (!existingIndexes.includes('order_id')) {
    try {
      await knex.raw('ALTER TABLE stellar_orders ADD UNIQUE KEY order_id (order_id)');
      console.log('✅ Added unique constraint on order_id');
    } catch (error) {
      console.log('⚠️  Unique constraint on order_id already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for stellar_orders table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
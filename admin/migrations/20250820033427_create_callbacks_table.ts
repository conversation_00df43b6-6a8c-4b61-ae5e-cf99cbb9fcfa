import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('callbacks');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('callbacks', (table) => {
      table.increments('id').primary(); // Auto-incrementing int primary key
      table.string('client_id', 50).notNullable();
      table.string('callback_url', 255).notNullable();
      table.enum('status', ['active', 'inactive']).notNullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('callbacks', (table) => {
      table.index(['client_id']);
      table.index(['status']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created callbacks table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table callbacks exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM callbacks');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.string('client_id', 50).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('callback_url')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.string('callback_url', 255).notNullable();
    });
    console.log('✅ Added callback_url field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.enum('status', ['active', 'inactive']).notNullable();
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM callbacks');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('callbacks_client_id_index')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.index(['client_id']);
    });
    console.log('✅ Added client_id index');
  }
  
  if (!existingIndexes.includes('callbacks_status_index')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('callbacks_created_at_index')) {
    await knex.schema.alterTable('callbacks', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for callbacks table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
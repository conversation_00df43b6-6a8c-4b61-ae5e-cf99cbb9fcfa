import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('sc_transactions');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('sc_transactions', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('clientId', 64).notNullable();
      table.string('refId', 100).notNullable();
      table.string('hash', 100).notNullable().unique();
      table.enum('direction', ['INCOMING', 'OUTGOING']).notNullable();
      table.string('state', 50).notNullable();
      table.string('network', 100).notNullable();
      table.string('asset_id', 250).nullable();
      table.string('asset', 255).notNullable();
      table.decimal('amount', 36, 18).notNullable();
      table.string('source', 100).notNullable();
      table.string('destination', 100).notNullable();
      table.string('createTime', 40).notNullable();
      table.string('confirmTime', 30).nullable();
      table.timestamp('createdAt').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['clientId']);
      table.index(['refId']);
      table.index(['direction']);
      table.index(['state']);
      table.index(['network']);
      table.index(['asset_id']);
      table.index(['asset']);
      table.index(['source']);
      table.index(['destination']);
      table.index(['createTime']);
      table.index(['confirmTime']);
      table.index(['createdAt']);
    });
    
    console.log('✅ Created sc_transactions table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table sc_transactions exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM sc_transactions');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('clientId')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('clientId', 64).notNullable();
    });
    console.log('✅ Added clientId field');
  }
  
  if (!existingColumns.includes('refId')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('refId', 100).notNullable();
    });
    console.log('✅ Added refId field');
  }
  
  if (!existingColumns.includes('hash')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('hash', 100).notNullable().unique();
    });
    console.log('✅ Added hash field');
  }
  
  if (!existingColumns.includes('direction')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.enum('direction', ['INCOMING', 'OUTGOING']).notNullable();
    });
    console.log('✅ Added direction field');
  }
  
  if (!existingColumns.includes('state')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('state', 50).notNullable();
    });
    console.log('✅ Added state field');
  }
  
  if (!existingColumns.includes('network')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('network', 100).notNullable();
    });
    console.log('✅ Added network field');
  }
  
  if (!existingColumns.includes('asset_id')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('asset_id', 250).nullable();
    });
    console.log('✅ Added asset_id field');
  }
  
  if (!existingColumns.includes('asset')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('asset', 255).notNullable();
    });
    console.log('✅ Added asset field');
  }
  
  if (!existingColumns.includes('amount')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.decimal('amount', 36, 18).notNullable();
    });
    console.log('✅ Added amount field');
  }
  
  if (!existingColumns.includes('source')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('source', 100).notNullable();
    });
    console.log('✅ Added source field');
  }
  
  if (!existingColumns.includes('destination')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('destination', 100).notNullable();
    });
    console.log('✅ Added destination field');
  }
  
  if (!existingColumns.includes('createTime')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('createTime', 40).notNullable();
    });
    console.log('✅ Added createTime field');
  }
  
  if (!existingColumns.includes('confirmTime')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.string('confirmTime', 30).nullable();
    });
    console.log('✅ Added confirmTime field');
  }
  
  if (!existingColumns.includes('createdAt')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.timestamp('createdAt').defaultTo(knex.fn.now());
    });
    console.log('✅ Added createdAt field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM sc_transactions');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('sc_transactions_clientId_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['clientId']);
    });
    console.log('✅ Added clientId index');
  }
  
  if (!existingIndexes.includes('sc_transactions_refId_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['refId']);
    });
    console.log('✅ Added refId index');
  }
  
  if (!existingIndexes.includes('sc_transactions_direction_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['direction']);
    });
    console.log('✅ Added direction index');
  }
  
  if (!existingIndexes.includes('sc_transactions_state_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['state']);
    });
    console.log('✅ Added state index');
  }
  
  if (!existingIndexes.includes('sc_transactions_network_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['network']);
    });
    console.log('✅ Added network index');
  }
  
  if (!existingIndexes.includes('sc_transactions_asset_id_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['asset_id']);
    });
    console.log('✅ Added asset_id index');
  }
  
  if (!existingIndexes.includes('sc_transactions_asset_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['asset']);
    });
    console.log('✅ Added asset index');
  }
  
  if (!existingIndexes.includes('sc_transactions_source_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['source']);
    });
    console.log('✅ Added source index');
  }
  
  if (!existingIndexes.includes('sc_transactions_destination_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['destination']);
    });
    console.log('✅ Added destination index');
  }
  
  if (!existingIndexes.includes('sc_transactions_createTime_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['createTime']);
    });
    console.log('✅ Added createTime index');
  }
  
  if (!existingIndexes.includes('sc_transactions_confirmTime_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['confirmTime']);
    });
    console.log('✅ Added confirmTime index');
  }
  
  if (!existingIndexes.includes('sc_transactions_createdAt_index')) {
    await knex.schema.alterTable('sc_transactions', (table) => {
      table.index(['createdAt']);
    });
    console.log('✅ Added createdAt index');
  }
  
  // Check for unique constraint on hash
  if (!existingIndexes.includes('hash')) {
    try {
      await knex.raw('ALTER TABLE sc_transactions ADD UNIQUE KEY hash (hash)');
      console.log('✅ Added unique constraint on hash');
    } catch (error) {
      console.log('⚠️  Unique constraint on hash already exists or could not be added');
    }
  }
  
  console.log('✅ Field check complete for sc_transactions table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
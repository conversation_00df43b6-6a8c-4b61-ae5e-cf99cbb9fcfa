import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('trade_logs');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('trade_logs', (table) => {
      table.increments('id').primary(); // int NOT NULL AUTO_INCREMENT
      table.string('client_id', 255).notNullable();
      table.string('action', 50).notNullable();
      table.string('order_id', 255).nullable();
      table.json('data').nullable();
      table.string('ip_address', 45).nullable();
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance (matching the SQL schema)
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['client_id'], 'idx_client_logs');
      table.index(['action'], 'idx_action_logs');
      table.index(['created_at'], 'idx_created_at');
      table.index(['order_id']);
      table.index(['ip_address']);
    });
    
    console.log('✅ Created trade_logs table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table trade_logs exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM trade_logs');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.increments('id').primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('client_id')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.string('client_id', 255).notNullable();
    });
    console.log('✅ Added client_id field');
  }
  
  if (!existingColumns.includes('action')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.string('action', 50).notNullable();
    });
    console.log('✅ Added action field');
  }
  
  if (!existingColumns.includes('order_id')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.string('order_id', 255).nullable();
    });
    console.log('✅ Added order_id field');
  }
  
  if (!existingColumns.includes('data')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.json('data').nullable();
    });
    console.log('✅ Added data field');
  }
  
  if (!existingColumns.includes('ip_address')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.string('ip_address', 45).nullable();
    });
    console.log('✅ Added ip_address field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM trade_logs');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('idx_client_logs')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['client_id'], 'idx_client_logs');
    });
    console.log('✅ Added idx_client_logs index');
  }
  
  if (!existingIndexes.includes('idx_action_logs')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['action'], 'idx_action_logs');
    });
    console.log('✅ Added idx_action_logs index');
  }
  
  if (!existingIndexes.includes('idx_created_at')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['created_at'], 'idx_created_at');
    });
    console.log('✅ Added idx_created_at index');
  }
  
  if (!existingIndexes.includes('trade_logs_order_id_index')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['order_id']);
    });
    console.log('✅ Added order_id index');
  }
  
  if (!existingIndexes.includes('trade_logs_ip_address_index')) {
    await knex.schema.alterTable('trade_logs', (table) => {
      table.index(['ip_address']);
    });
    console.log('✅ Added ip_address index');
  }
  
  console.log('✅ Field check complete for trade_logs table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
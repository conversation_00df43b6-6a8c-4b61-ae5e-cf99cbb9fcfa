import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('user_2fa');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('user_2fa', (table) => {
      table.string('id', 200).primary(); // varchar(200) NOT NULL PRIMARY KEY
      table.string('user_id', 160).nullable();
      table.string('secret', 50).nullable();
      table.text('qr_code').nullable();
      table.string('code', 190).nullable();
      table.string('user_type', 30).nullable();
      table.enum('status', ['active', 'inactive', 'pending', 'removed']).nullable();
      table.timestamp('deleted_at').nullable();
      table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['user_id']);
      table.index(['user_type']);
      table.index(['status']);
      table.index(['code']);
      table.index(['deleted_at']);
      table.index(['updated_at']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created user_2fa table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table user_2fa exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM user_2fa');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.string('id', 200).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('user_id')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.string('user_id', 160).nullable();
    });
    console.log('✅ Added user_id field');
  }
  
  if (!existingColumns.includes('secret')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.string('secret', 50).nullable();
    });
    console.log('✅ Added secret field');
  }
  
  if (!existingColumns.includes('qr_code')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.text('qr_code').nullable();
    });
    console.log('✅ Added qr_code field');
  }
  
  if (!existingColumns.includes('code')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.string('code', 190).nullable();
    });
    console.log('✅ Added code field');
  }
  
  if (!existingColumns.includes('user_type')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.string('user_type', 30).nullable();
    });
    console.log('✅ Added user_type field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.enum('status', ['active', 'inactive', 'pending', 'removed']).nullable();
    });
    console.log('✅ Added status field');
  }
  
  if (!existingColumns.includes('deleted_at')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.timestamp('deleted_at').nullable();
    });
    console.log('✅ Added deleted_at field');
  }
  
  if (!existingColumns.includes('updated_at')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added updated_at field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM user_2fa');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('user_2fa_user_id_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['user_id']);
    });
    console.log('✅ Added user_id index');
  }
  
  if (!existingIndexes.includes('user_2fa_user_type_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['user_type']);
    });
    console.log('✅ Added user_type index');
  }
  
  if (!existingIndexes.includes('user_2fa_status_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('user_2fa_code_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['code']);
    });
    console.log('✅ Added code index');
  }
  
  if (!existingIndexes.includes('user_2fa_deleted_at_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['deleted_at']);
    });
    console.log('✅ Added deleted_at index');
  }
  
  if (!existingIndexes.includes('user_2fa_updated_at_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['updated_at']);
    });
    console.log('✅ Added updated_at index');
  }
  
  if (!existingIndexes.includes('user_2fa_created_at_index')) {
    await knex.schema.alterTable('user_2fa', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for user_2fa table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
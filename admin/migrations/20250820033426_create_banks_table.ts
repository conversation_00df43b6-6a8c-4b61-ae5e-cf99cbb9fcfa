import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('banks');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('banks', (table) => {
      table.string('id', 90).primary(); // varchar(90) primary key
      table.string('bank_name', 90).nullable();
      table.string('branch_name', 90).nullable();
      table.string('branch_address', 90).nullable();
      table.string('account_name', 50).nullable();
      table.string('account_number', 60).nullable();
      table.string('swift_code', 30).nullable();
      table.string('country', 30).nullable();
      table.string('currency', 40).nullable();
      table.string('beneficiary_address', 50).nullable();
      table.string('reference_code', 80).nullable();
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      table.enum('status', ['ACTIVE', 'INACTIVE']).notNullable().defaultTo('ACTIVE');
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('banks', (table) => {
      table.index(['bank_name']);
      table.index(['account_number']);
      table.index(['swift_code']);
      table.index(['country']);
      table.index(['currency']);
      table.index(['status']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created banks table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table banks exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM banks');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('id', 90).primary();
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('bank_name')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('bank_name', 90).nullable();
    });
    console.log('✅ Added bank_name field');
  }
  
  if (!existingColumns.includes('branch_name')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('branch_name', 90).nullable();
    });
    console.log('✅ Added branch_name field');
  }
  
  if (!existingColumns.includes('branch_address')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('branch_address', 90).nullable();
    });
    console.log('✅ Added branch_address field');
  }
  
  if (!existingColumns.includes('account_name')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('account_name', 50).nullable();
    });
    console.log('✅ Added account_name field');
  }
  
  if (!existingColumns.includes('account_number')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('account_number', 60).nullable();
    });
    console.log('✅ Added account_number field');
  }
  
  if (!existingColumns.includes('swift_code')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('swift_code', 30).nullable();
    });
    console.log('✅ Added swift_code field');
  }
  
  if (!existingColumns.includes('country')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('country', 30).nullable();
    });
    console.log('✅ Added country field');
  }
  
  if (!existingColumns.includes('currency')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('currency', 40).nullable();
    });
    console.log('✅ Added currency field');
  }
  
  if (!existingColumns.includes('beneficiary_address')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('beneficiary_address', 50).nullable();
    });
    console.log('✅ Added beneficiary_address field');
  }
  
  if (!existingColumns.includes('reference_code')) {
    await knex.schema.alterTable('banks', (table) => {
      table.string('reference_code', 80).nullable();
    });
    console.log('✅ Added reference_code field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('banks', (table) => {
      table.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  if (!existingColumns.includes('status')) {
    await knex.schema.alterTable('banks', (table) => {
      table.enum('status', ['ACTIVE', 'INACTIVE']).notNullable().defaultTo('ACTIVE');
    });
    console.log('✅ Added status field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM banks');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('banks_bank_name_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['bank_name']);
    });
    console.log('✅ Added bank_name index');
  }
  
  if (!existingIndexes.includes('banks_account_number_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['account_number']);
    });
    console.log('✅ Added account_number index');
  }
  
  if (!existingIndexes.includes('banks_swift_code_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['swift_code']);
    });
    console.log('✅ Added swift_code index');
  }
  
  if (!existingIndexes.includes('banks_country_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['country']);
    });
    console.log('✅ Added country index');
  }
  
  if (!existingIndexes.includes('banks_currency_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['currency']);
    });
    console.log('✅ Added currency index');
  }
  
  if (!existingIndexes.includes('banks_status_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['status']);
    });
    console.log('✅ Added status index');
  }
  
  if (!existingIndexes.includes('banks_created_at_index')) {
    await knex.schema.alterTable('banks', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  console.log('✅ Field check complete for banks table');
}

export async function down(knex: Knex): Promise<void> {
  // This migration is safe to run multiple times, so down migration just logs
  console.log('⚠️  This migration is safe to run multiple times. No rollback needed.');
  return Promise.resolve();
} 
import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Check if table exists, if not create it
  const tableExists = await knex.schema.hasTable('tier_fees');
  
  if (!tableExists) {
    // Create the table with all fields if it doesn't exist
    await knex.schema.createTable('tier_fees', (table) => {
      table.string('id', 36).primary(); // CHAR(36) PRIMARY KEY DEFAULT (UUID())
      table.string('product_id', 36).nullable(); // CHAR(36) NULL
      table.string('custome_fee_id', 36).nullable(); // CHAR(36) NULL (keeping original typo from SQL)
      table.decimal('min_amount', 15, 2).notNullable(); // DECIMAL(15,2) NOT NULL
      table.decimal('max_amount', 15, 2).notNullable(); // DECIMAL(15,2) NOT NULL
      table.enum('fee_type', ['FLAT', 'PERCENTAGE']).notNullable(); // ENUM('FLAT','PERCENTAGE') NOT NULL
      table.decimal('fee_value', 15, 2).notNullable(); // DECIMAL(15,2) NOT NULL
      table.timestamp('created_at').defaultTo(knex.fn.now()); // TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    });
    
    // Add foreign key constraints
    await knex.schema.alterTable('tier_fees', (table) => {
      table.foreign('product_id').references('product_id').inTable('products');
      table.foreign('custome_fee_id').references('id').inTable('custome_fees');
    });
    
    // Add indexes for better performance
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['product_id']);
      table.index(['custome_fee_id']);
      table.index(['fee_type']);
      table.index(['min_amount']);
      table.index(['max_amount']);
      table.index(['created_at']);
    });
    
    console.log('✅ Created tier_fees table with all fields');
    return;
  }

  // Table exists, check and add missing fields
  console.log('🔍 Table tier_fees exists, checking for missing fields...');
  
  const columns = await knex.raw('SHOW COLUMNS FROM tier_fees');
  const existingColumns = columns[0].map((col: any) => col.Field);
  
  console.log('📋 Existing columns:', existingColumns);
  
  // Check and add missing fields
  if (!existingColumns.includes('id')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.string('id', 36).primary().defaultTo(knex.raw('UUID()'));
    });
    console.log('✅ Added id field');
  }
  
  if (!existingColumns.includes('product_id')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.string('product_id', 36).nullable();
    });
    console.log('✅ Added product_id field');
  }
  
  if (!existingColumns.includes('custome_fee_id')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.string('custome_fee_id', 36).nullable();
    });
    console.log('✅ Added custome_fee_id field');
  }
  
  if (!existingColumns.includes('min_amount')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.decimal('min_amount', 15, 2).notNullable();
    });
    console.log('✅ Added min_amount field');
  }
  
  if (!existingColumns.includes('max_amount')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.decimal('max_amount', 15, 2).notNullable();
    });
    console.log('✅ Added max_amount field');
  }
  
  if (!existingColumns.includes('fee_type')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.enum('fee_type', ['FLAT', 'PERCENTAGE']).notNullable();
    });
    console.log('✅ Added fee_type field');
  }
  
  if (!existingColumns.includes('fee_value')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.decimal('fee_value', 15, 2).notNullable();
    });
    console.log('✅ Added fee_value field');
  }
  
  if (!existingColumns.includes('created_at')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.timestamp('created_at').defaultTo(knex.fn.now());
    });
    console.log('✅ Added created_at field');
  }
  
  // Check and add missing indexes
  const indexes = await knex.raw('SHOW INDEX FROM tier_fees');
  const existingIndexes = indexes[0].map((idx: any) => idx.Key_name);
  
  if (!existingIndexes.includes('tier_fees_product_id_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['product_id']);
    });
    console.log('✅ Added product_id index');
  }
  
  if (!existingIndexes.includes('tier_fees_custome_fee_id_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['custome_fee_id']);
    });
    console.log('✅ Added custome_fee_id index');
  }
  
  if (!existingIndexes.includes('tier_fees_fee_type_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['fee_type']);
    });
    console.log('✅ Added fee_type index');
  }
  
  if (!existingIndexes.includes('tier_fees_min_amount_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['min_amount']);
    });
    console.log('✅ Added min_amount index');
  }
  
  if (!existingIndexes.includes('tier_fees_max_amount_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['max_amount']);
    });
    console.log('✅ Added max_amount index');
  }
  
  if (!existingIndexes.includes('tier_fees_created_at_index')) {
    await knex.schema.alterTable('tier_fees', (table) => {
      table.index(['created_at']);
    });
    console.log('✅ Added created_at index');
  }
  
  // Check for foreign key constraints
  try {
    const foreignKeys = await knex.raw(`
      SELECT CONSTRAINT_NAME 
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'tier_fees' 
      AND REFERENCED_TABLE_NAME = 'products'
      AND REFERENCED_COLUMN_NAME = 'product_id'
    `);
    
    if (foreignKeys[0].length === 0) {
      await knex.raw(`
        ALTER TABLE tier_fees 
        ADD CONSTRAINT tier_fees_product_id_foreign 
        FOREIGN KEY (product_id) 
        REFERENCES products(product_id) 
        ON DELETE RESTRICT 
        ON UPDATE RESTRICT
      `);
      console.log('✅ Added foreign key constraint on product_id');
    } else {
      console.log('⚠️  Foreign key constraint on product_id already exists');
    }
  } catch (error) {
    console.log('⚠️  Foreign key constraint on product_id could not be added or already exists');
  }
  
  try {
    const foreignKeys = await knex.raw(`
      SELECT CONSTRAINT_NAME 
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'tier_fees' 
      AND REFERENCED_TABLE_NAME = 'custome_fees'
      AND REFERENCED_COLUMN_NAME = 'id'
    `);
    
    if (foreignKeys[0].length === 0) {
      await knex.raw(`
        ALTER TABLE tier_fees 
        ADD CONSTRAINT tier_fees_custome_fee_id_foreign 
        FOREIGN KEY (custome_fee_id) 
        REFERENCES custome_fees(id) 
        ON DELETE RESTRICT 
        ON UPDATE RESTRICT
      `);
      console.log('✅ Added foreign key constraint on custome_fee_id');
    } else {
      console.log('⚠️  Foreign key constraint on custome_fee_id already exists');
    }
  } catch (error) {
    console.log('⚠️  Foreign key constraint on custome_fee_id could not be added or already exists');
  }
  
  console.log('✅ Field check complete for tier_fees table');
}

export async function down(knex: Knex): Promise<void> {
  // Drop the table if it exists
  const tableExists = await knex.schema.hasTable('tier_fees');
  
  if (tableExists) {
    await knex.schema.dropTable('tier_fees');
    console.log('✅ Dropped tier_fees table');
  } else {
    console.log('⚠️  tier_fees table does not exist, nothing to drop');
  }
}
# Muda Payments API Documentation

This document provides information about the Muda Payments API endpoints that are available for third-party integration. The API allows you to process payments, check balances, and manage transactions.

## Authentication

All API requests (except for login) require authentication using a JWT token. The token should be included in the `Authorization` header as a Bearer token.

```
Authorization: Bearer <your_jwt_token>
```

## Base URL

```
https://api.muda.com/payment
```

## Available Endpoints

### Payment Endpoints

These endpoints handle the core payment functionality.

#### Validate Transaction Request

Validates a transaction request before processing.

- **URL**: `/validate-request`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "clientId": 12345,
    "product_id": "product123",
    "trans_type": "PULL",
    "public_key": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "account_number": "256XXXXXXXXX",
    "reference_id": "ref123456",
    "currency": "UGX",
    "amount": 10000
  }
  ```
- **Success Response**: `201 Created`
  ```json
  {
    "status": 201,
    "message": "Transaction validated successfully",
    "data": {
      "accountName": "John Doe",
      "phoneNumber": "256XXXXXXXXX",
      "fee": 200,
      "reference_id": "ref123456",
      "validation_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "client_id": 12345,
      "product_id": "product123",
      "trans_type": "PULL",
      "trans_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "amount": 10000,
      "asset_code": "cUGX",
      "fee": 200,
      "currency": "UGX",
      "sender_account": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      "receiver_account": "256XXXXXXXXX",
      "memo": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "status": "pending"
    }
  }
  ```

#### Validate Account

Validates an account (phone number, bank account, card) with the appropriate payment provider.

- **URL**: `/validate-account`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "phone": "256XXXXXXXXX",
    "service_name": "MOBILE_MONEY",
    "currency": "UGX"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "Account validated successfully",
    "data": {
      "isValid": true,
      "accountName": "John Doe",
      "message": "Account validated successfully"
    }
  }
  ```
- **Error Response**: `400 Bad Request`
  ```json
  {
    "status": 400,
    "message": "Invalid account number",
    "data": {
      "isValid": false,
      "accountName": "",
      "message": "Invalid account number"
    }
  }
  ```

#### Direct Payout

Initiates a direct payout to a mobile money account. This operation first debits the Stellar wallet and then sends the money to the recipient.

- **URL**: `/direct-payout`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "clientId": 12345,
    "amount": 10000,
    "phone": "256XXXXXXXXX",
    "reference_id": "ref123456",
    "currency": "UGX",
    "product_id": 1,
    "memo": "Payment for services"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "Transaction completed successfully",
    "data": {
      "trans_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "status": "SUCCESS",
      "is_offline": false
    }
  }
  ```
- **Pending Response**: `202 Accepted`
  ```json
  {
    "status": 202,
    "message": "Transaction is being processed",
    "data": {
      "trans_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "status": "PENDING",
      "is_offline": false
    }
  }
  ```

#### Direct Collection

Initiates a direct collection request from a mobile money account or other payment source.

- **URL**: `/direct-collection`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "clientId": 12345,
    "amount": 10000,
    "phone": "256XXXXXXXXX",
    "reference_id": "ref123456",
    "currency": "UGX",
    "product_id": 1,
    "memo": "Payment collection"
  }
  ```
- **Success Response**: `202 Accepted`
  ```json
  {
    "status": 202,
    "message": "Collection request sent",
    "data": {
      "trans_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "status": "INITIATED"
    }
  }
  ```
- **Note**: Collection requests are typically asynchronous. The actual success/failure will be communicated via webhook callbacks.

### Account Information Endpoints

These endpoints provide information about accounts and transactions.

#### Get Balance

Retrieves the current balance for a client.

- **URL**: `/balance`
- **Method**: `GET`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "clientId": "12345"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "Balance fetched successfully",
    "data": [
      {
        "code": "UGX",
        "balance": "50000.0000",
        "issuer": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      },
      {
        "code": "USD",
        "balance": "100.0000",
        "issuer": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
      }
    ]
  }
  ```

#### Get Statement

Retrieves transaction history for a client.

- **URL**: `/statement`
- **Method**: `GET`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "clientId": "12345"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "Transaction history fetched",
    "data": [
      {
        "trans_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
        "reference_id": "ref123456",
        "amount": "10000",
        "currency": "UGX",
        "status": "SUCCESS",
        "created_at": "2023-06-01T12:00:00Z",
        "trans_type": "PUSH"
      }
    ]
  }
  ```

#### Get Transaction

Retrieves details for a specific transaction.

- **URL**: `/transaction/:id`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**: `id=[transaction_id]`
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "success",
    "data": {
      "type": "transaction_status",
      "timestamp": "2023-06-01T12:00:00Z",
      "reference_id": "ref123456",
      "status": "SUCCESS",
      "amount": "10000",
      "currency": "UGX",
      "sender_account": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      "receiver_account": "256XXXXXXXXX",
      "transaction_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "meta": "Payment processed successfully",
      "client_id": "12345"
    }
  }
  ```

#### Get PegPay Balance

Retrieves the current PegPay account balance.

- **URL**: `/pegpay-balance`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**: `200 OK`
  ```json
  {
    "status": 200,
    "message": "Balance fetched successfully",
    "data": {
      "balance": "99488.0000",
      "statusDescription": "SUCCESS"
    }
  }
  ```

## Webhooks

The API sends webhook notifications for transaction status updates. To receive these notifications, you need to register a webhook URL with your account.

### Webhook Payload

```json
{
  "type": "transaction_status",
  "timestamp": "2023-06-01T12:00:00Z",
  "reference_id": "ref123456",
  "status": "SUCCESS",
  "amount": "10000",
  "currency": "UGX",
  "sender_account": "GXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "receiver_account": "256XXXXXXXXX",
  "transaction_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
  "meta": "Payment processed successfully",
  "client_id": "12345"
}
```

### Webhook Status Values

- `PENDING`: The transaction is being processed
- `SUCCESS`: The transaction completed successfully
- `FAILED`: The transaction failed

## Supported Payment Methods

The API supports multiple payment methods based on the `service_name` specified in the product configuration:

1. **MOBILE_MONEY**: Mobile money transactions (e.g., MTN Mobile Money, Airtel Money)
2. **BANK_TRANSFER**: Bank transfer transactions
3. **CARD_PAYMENT**: Card payment transactions (for collections)
4. **CRYPTO**: Cryptocurrency transactions (for payouts)

## Offline Mode

For payout transactions (PUSH), the system supports offline mode. If the connection to the payment provider is unavailable, the transaction will be stored and processed later when the connection is restored. The webhook will be sent once the transaction is processed.

## Error Handling

All API endpoints return standard HTTP status codes. In case of an error, the response will include an error message and, if applicable, additional error details.

Example error response:

```json
{
  "status": 400,
  "message": "Invalid data",
  "data": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["amount"],
      "message": "Expected number, received string"
    }
  ]
}
```

## Rate Limiting

API requests are subject to rate limiting. The current limits are:
- 100 requests per minute per client
- 5,000 requests per day per client

If you exceed these limits, you will receive a `429 Too Many Requests` response.

## Support

For API support, please contact:
- Email: <EMAIL>
- Phone: +256 XXX XXX XXX 
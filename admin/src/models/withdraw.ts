import Model from "../helpers/model";
import <PERSON>F<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "../helpers/2fa.helper";
import EmailSender from "../helpers/email.helper";
import { v4 as uuidv4 } from "uuid";
import jwt from "jsonwebtoken";
import CryptoJS from "crypto-js";
import { z } from "zod";
import StellarService from "../helpers/StellarService";
import { LiquidityRailService } from "../services/liquidityrail.service";
import RatesService from "../helpers/exchangerates.helper";

const SECRET_KEY = ""; // process.env.SECRET_KEY || "your-secret-key";
const sendmail =  new EmailSender()
const ratesService = new RatesService();
const liquidityRailService = new LiquidityRailService();

type User_ = {
  email: string;
  password: string;
  role: string;
  first_name: string;
  last_name: string;
};

type RoleUpdateData = {
  name: string;
  details?: string;
  status: 'active' | 'inactive';
  updated_at: string;
};

type UserData = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  role_details: {
    id: string;
    name: string;
    details: string;
    status: string;
    access_rights: any[];
  };
  status: string;
  updated_at: string;
  created_at: string;
};

export default class Withdraw extends Model {
  private readonly Withdraw_TABLE = 'custome_fees';

  constructor() {
    super();
  }

  async issueTokens(data: any) {
    return this.makeResponse(409, "SUCCESS", data);
  }


}

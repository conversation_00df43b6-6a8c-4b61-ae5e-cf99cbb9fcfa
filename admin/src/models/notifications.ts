import Model from "../helpers/model";

export default class Notifications extends Model {

  async systemNotifications(data: any) {
    return this.makeResponse(200, "Notifications fetched successfully", []);
  }

  async systemRequestCounter(data: any) {
    try {
      // Get all pending requests count
      const totalPendingResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE status = 'pending'`
      );
      
      // Get pending deposit approvals count
      const pendingDepositResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE entry_type = 'deposit' AND status = 'pending'`
      );
      
      // Get pending user approvals count
      const pendingUsersResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_admin' OR entry_type = 'edit_admin') AND status = 'pending'`
      );
      
      // Get pending business approvals count
      const pendingBusinessResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_business' OR entry_type = 'edit_business') AND status = 'pending'`
      );
      
      // Get pending fees approvals count
      const pendingFeesResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_business_fees' OR entry_type = 'edit_business_fees') AND status = 'pending'`
      );
      
      // Get pending role approvals count
      const pendingRoleResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_role' OR entry_type = 'edit_role') AND status = 'pending'`
      );
      
      // Get pending exchange rates approvals count
      const pendingExchangeRatesResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_exchange_rate' OR entry_type = 'edit_exchange_rate') AND status = 'pending'`
      );
      
      // Get pending payment methods approvals count
      const pendingPaymentMethodsResult: any = await this.callRawQuery(
        `SELECT COUNT(*) as total FROM maker_checker WHERE (entry_type = 'add_payment_methods' OR entry_type = 'edit_payment_methods') AND status = 'pending'`
      );

      // Compile comprehensive response data
      const requestCounts = {
        total_pending: totalPendingResult[0]?.total || 0,
        by_category: {
          deposits: pendingDepositResult[0]?.total || 0,
          users: pendingUsersResult[0]?.total || 0,
          businesses: pendingBusinessResult[0]?.total || 0,
          fees: pendingFeesResult[0]?.total || 0,
          roles: pendingRoleResult[0]?.total || 0,
          exchange_rates: pendingExchangeRatesResult[0]?.total || 0,
          payment_methods: pendingPaymentMethodsResult[0]?.total || 0
        }
      };

      return this.makeResponse(200, "Pending Request counter fetched successfully", requestCounts);
      
    } catch (error: any) {
      console.error("Error fetching system request counter:", error);
      return this.makeResponse(500, "Error fetching request counter", {
        total_pending: 0,
        by_category: {
          deposits: 0,
          users: 0,
          businesses: 0,
          fees: 0,
          roles: 0,
          exchange_rates: 0,
          payment_methods: 0
        }
      });
    }
  }

 
}

import Model from "../helpers/model";
import { uploadToS3 } from "../helpers/S3UploadHelper";




class Media extends Model {
  async uploadFile(data: any, files: any[]) {
    console.log("data", data);
    console.log("files", files);
    const fileType = ["DOCUMENT", "STATUS_POST", "THUMBNAIL", "VIDEO", "IMAGE"];

    try {
      if (files.length > 3) {
        return this.makeResponse(400, "Maximum of 3 files can be uploaded at once");
      }
      const { userId, file_type } = data;
      if (!fileType.includes(file_type)) {
        return this.makeResponse(400, "File type should be one of " + JSON.stringify(fileType));
      }




      // Upload each file to S3 with thumbnails automatically handled
      const uploadPromises = files.map(file => uploadToS3(file, "muda"));
      const uploadResults = await Promise.all(uploadPromises);

      const uploads = [];
        const file_id = "d" + this.getRandomString();
        const file_url = uploadResults[0].url;
        const uploadInfo = {
          file_id,
          user_id: userId,
          file_type,
          file_url
        };
        await this.insertData("uploads", uploadInfo);
      

      return this.makeResponse(200, "upload successful", uploadInfo);
    } catch (error) {
      console.log("UPLOAD_ERROR", error);
      return this.makeResponse(500, "error uploading file, please try again");
    }
  }
}

export default Media;

import Model from "../helpers/model";
import { QuoteFilterBuilder } from "../helpers/quote-filter.builder";
import axios from 'axios';
import dotenv from 'dotenv';
dotenv.config();

export interface QuoteFilter {
  search?: string;
  start_date?: Date;
  end_date?: Date;
  provider_id?: string;
  company_id?: string;
  status?: string;
  currency?: string;
  receive_currency?: string;
  pay_in_status?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface TradeMetricsResult {
  pair: any;
  fromAmount: number;
  transRate: number;
  fee: number;
  grossToAmount: number;
  netToAmount: number;
  effectiveRate: number;
  indexRate?: number;
  spreadPercent?: number;
  profitValue?: number;
  profitFlag?: string;
}

interface TradeMetricsInput {
  pair: string;
  fromAmount: number;
  transRate: number;
  fee?: number;
  indexRate?: number | null;
}

interface TradeProfitParams {
  send_asset: string;
  receive_currency: string;
  fromAmount: number;
  exchangeRate: number;
  fee: number;
}

interface LiquidityRailMetrics {
  transactions: {
    total: number;
    successful: number;
    failed: number;
    totalVolume: number;
    averageSpread: number;
    totalProfit: number;
  };
  fees: {
    totalFees: number;
    averageFee: number;
    feeBreakdown: any[];
  };
  clients: {
    totalClients: number;
    activeClients: number;
    topClients: any[];
  };
  providers: {
    totalProviders: number;
    activeProviders: number;
    providerPerformance: any[];
  };
}

export class LiquidityRailService  {
  private model: Model;
  private readonly LIQUIDITY_RAIL_API_URL: string;

  constructor() {
    this.model = new Model();
    this.LIQUIDITY_RAIL_API_URL = process.env.LIQUIDITY_RAIL_API_URL || 'https://rail.stage-mudax.xyz';
  }

  private buildWhereClause(filter: QuoteFilter): string {
    return new QuoteFilterBuilder(filter).build();
  }

  private calculateSpreadAndProfit(transaction: any, midMarketRate: any) {
      const {
          send_amount: sendAmount,
          receive_amount: receiveAmount,
          ex_rate: exchangeRate,
          fee
      } = transaction;

      const sendAmountNum = parseFloat(sendAmount);
      const receiveAmountNum = parseFloat(receiveAmount);
      const exchangeRateNum = parseFloat(exchangeRate);
      const feeNum = parseFloat(fee);

      const assumedMidMarketRate = midMarketRate || exchangeRateNum * 1.002;

      const spread = assumedMidMarketRate - exchangeRateNum;
      const spreadPercentage = (spread / assumedMidMarketRate) * 100;

      // Calculate profit (from spread + fee)
      const spreadProfit = sendAmountNum * spread;
      const totalProfit = spreadProfit + feeNum;

      return {
          spreadPercentage: Number(spreadPercentage).toFixed(4),
          profit: Number(totalProfit).toFixed(2),
          assumedMidMarketRate: Number(assumedMidMarketRate).toFixed(4)
      };
  }


  
  async getingLRCharges(data?: any): Promise<any> {
    try {

      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/charges?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });
      return {
        status: 200,
        message: `charges fetch`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {
      console.error('Error charges:', error);
      return {
        status: 500,
        message: 'Error charges',
        error: error.message
      };
    }
  }

  async updatingLRCharges(id: any, data?: any): Promise<any> {
    try {


      const responseData: any = await this.model.confirmUser2Fa(data);
      // if (!responseData?.status) {
      //   return {
      //             status: 401,
      //             message: responseData?.message,
      //             data:    responseData?.message
      //         };
      // }
      const RESPONSE = await axios.put(`${this.LIQUIDITY_RAIL_API_URL}/admin/charges/${id}`, data, {
                                         headers: {
                                          'Authorization': `${data?.headers?.authorization}`,
                                          'Content-Type': 'application/json',
                                        }});
       
      return {
        status: 200,
        message: `charges updated`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {


      console.error('Error updating charges:', error);
      return {
        status: 500,
        message: 'Error updating charges',
        error: error.message
      };
    }
  }






  async getLLRProviders(data?: any): Promise<any> {
    try {

      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/providers?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });
       
      const TRADES = await RESPONSE.data.data.items.map(async (item: any) =>{
          item.tradeMetrics = await this.calculateSpreadAndProfit(item, "");
          return item;  
      });

      return {
        status: 200,
        message: `Successfully imported quotes`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }


  async getLLRProvidersFees(data?: any): Promise<any> {
    try {
      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/providers/${data?.params?.id}/fees?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });
       
      const TRADES = await RESPONSE.data.data.items.map(async (item: any) =>{
          item.tradeMetrics = await this.calculateSpreadAndProfit(item, "");
          return item;  
      });

      return {
        status: 200,
        message: `Successfully imported quotes`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }


  async getLLRProvidersFee(data?: any): Promise<any> {
    try {

      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/providers/${data?.params?.id}/fees/${data?.params?.provider_service_id}?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });
       
      const TRADES = await RESPONSE.data.data.items.map(async (item: any) =>{
          item.tradeMetrics = await this.calculateSpreadAndProfit(item, "");
          return item;  
      });

      return {
        status: 200,
        message: `Successfully imported quotes`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }
  

  async profitonTradeStats(data?: any): Promise<any> {
    try {

      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/transactions?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });
       
      const TRADES = await RESPONSE.data.data.items.map(async (item: any) =>{
          item.tradeMetrics = await this.calculateSpreadAndProfit(item, "");
          return item;  
      });

      return {
        status: 200,
        message: `Successfully imported quotes`,
        data: RESPONSE.data.data
      };

    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }

  async importQuotes(data?: any): Promise<any> {
    try {
      const queryString: any = new URLSearchParams(data?.query).toString();
      const url = `${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/transactions?${queryString}`;
      console.log('url:::::', url);
       const RESPONSE = await axios.get(url, { 
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
       });

      return {
        status: 200,
        message: `Successfully imported quotes`,
        data: RESPONSE.data.data
      };
    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }


  
  async getProfitLiquidityRailNetworkStats(data?: any): Promise<any> {
    try {
      const queryString: any = new URLSearchParams(data?.query).toString();
       const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/profits?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
       });

      return {
        status: 200,
        message: `Successfully imported profits`,
        data: RESPONSE.data.data
      };
    } catch (error: any) {
      console.error('Error importing quotes:', error);
      return {
        status: 500,
        message: 'Error importing quotes',
        error: error.message
      };
    }
  }


  
  async importQuotesFees(data?: any): Promise<any> {
    try {
      
       const queryString: any = new URLSearchParams(data?.query).toString();
       const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/transaction/fees?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });

      const QUOTES_FEES = RESPONSE.data.data;
      return {
        status: 200,
        message: `Successfully imported quote fees`,
        data: QUOTES_FEES
      };
    } catch (error: any) {
      console.error('Error importing quote fees:', error);
      return {
        status: 500,
        message: 'Error importing quote fees',
        error: error.message
      };
    }
  }




  
  async importClients(data?: any): Promise<any> {
    try {

      
      const queryString: any = new URLSearchParams(data?.query).toString();
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/clients?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });

      const CLIENTS = RESPONSE.data.data;
      
      return {
        status: 200,
        message: `Successfully imported clients`,
        data: CLIENTS
      };
    } catch (error: any) {
      console.error('Error importing clients:', error);
      return {
        status: 500,
        message: 'Error importing clients',
        error: error.message
      };
    }
  }


  
  async importProviders(data?: any): Promise<any> {
    try {
      
      const queryString: any = new URLSearchParams(data?.query).toString();
      // reports/rails/providers
      const RESPONSE = await axios.get(`${this.LIQUIDITY_RAIL_API_URL}/admin/reports/rails/services?${queryString}`, {
        headers: {
          'Authorization': `${data?.headers?.authorization}`
        }
      });

      const PROVIDERS = RESPONSE.data.data;

      return {
        status: 200,
        message: `Successfully imported providers`,
        data: PROVIDERS
      };
    } catch (error: any) {
      console.error('Error importing providers:', error);
      return {
        status: 500,
        message: 'Error importing providers',
        error: error.message
      };
    }
  }

  async getLiquidityRailMetrics(data?: any): Promise<any> {
    try {
      // Get transactions data
      const transactionsResponse = await this.importQuotes(data);
      const transactions = transactionsResponse.data.items || [];
      
      // Get fees data
      const feesResponse = await this.importQuotesFees(data);
      const fees = feesResponse.data || [];
      
      // Get clients data
      const clientsResponse = await this.importClients(data);
      const clients = clientsResponse.data || [];
      
      // Get providers data
      const providersResponse = await this.importProviders(data);
      const providers = providersResponse.data || [];

      // Calculate metrics
      const metrics: LiquidityRailMetrics = {
        transactions: {
          total: transactions.length,
          successful: transactions.filter((t: any) => t.status === 'SUCCESSFUL').length,
          failed: transactions.filter((t: any) => t.status === 'FAILED').length,
          totalVolume: transactions.reduce((sum: number, t: any) => sum + parseFloat(t.send_amount || 0), 0),
          averageSpread: transactions.reduce((sum: number, t: any) => {
            const metrics = this.calculateSpreadAndProfit(t, "");
            return sum + parseFloat(metrics.spreadPercentage);
          }, 0) / transactions.length || 0,
          totalProfit: transactions.reduce((sum: number, t: any) => {
            const metrics = this.calculateSpreadAndProfit(t, "");
            return sum + parseFloat(metrics.profit);
          }, 0)
        },
        fees: {
          totalFees: fees.reduce((sum: number, f: any) => sum + parseFloat(f.fee || 0), 0),
          averageFee: fees.length ? fees.reduce((sum: number, f: any) => sum + parseFloat(f.fee || 0), 0) / fees.length : 0,
          feeBreakdown: fees
        },
        clients: {
          totalClients: clients.length,
          activeClients: clients.filter((c: any) => c.status === 'active').length,
          topClients: clients.slice(0, 5) // Top 5 clients
        },
        providers: {
          totalProviders: providers.length,
          activeProviders: providers.filter((p: any) => p.status === 'active').length,
          providerPerformance: providers.map((p: any) => ({
            name: p.name,
            status: p.status,
            successRate: p.success_rate || 0,
            totalTransactions: p.total_transactions || 0
          }))
        }
      };

      return {
        status: 200,
        message: 'Successfully fetched liquidity rail metrics',
        data: metrics
      };
    } catch (error: any) {
      console.error('Error fetching liquidity rail metrics:', error);
      return {
        status: 500,
        message: 'Error fetching liquidity rail metrics',
        error: error.message
      };
    }
  }
}

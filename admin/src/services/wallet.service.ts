import axios, { AxiosResponse } from 'axios';
import dotenv from 'dotenv';

dotenv.config();

class WalletService {
  private walletServiceUrl: string;
  constructor() {
    this.walletServiceUrl = process.env.WALLET_SERVICE_URL || 'http://localhost:3006';
  }

  async completeAdminTransaction(operation: 'APPROVE_DEPOSIT' | 'APPROVE_SWAP', adminId: string, transId: string): Promise<any> {
    try {
      const response: AxiosResponse = await axios.post(
        `${this.walletServiceUrl}/payment/complete-admin-transaction`,
        { 
          operation,
          adminId,
          transId
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000 // 30 second timeout
        }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error calling wallet service completeAdminTransaction:', error.message);
      throw new Error(`Wallet service call failed: ${error.message}`);
    }
  }

  /**
   * Issue tokens via wallet service (for backward compatibility)
   */
  async issueTokens(transactionId: string, adminId: string = 'admin'): Promise<any> {
    return this.completeAdminTransaction('APPROVE_DEPOSIT', adminId, transactionId);
  }

  /**
   * Swap tokens via wallet service (for backward compatibility)
   */
  async swapTokens(transactionId: string, adminId: string = 'admin'): Promise<any> {
    return this.completeAdminTransaction('APPROVE_SWAP', adminId, transactionId);
  }
}

export default WalletService; 
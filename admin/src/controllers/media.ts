import express, { Request, Response } from 'express';
import Media from '../models/media.model';
import { JWTMiddleware } from '../helpers/jwt.middleware';
import expressFileUpload from 'express-fileupload';

const router = express.Router();
const media = new Media();

router.post('/uploadFile', uploadFile);



async function uploadFile(req: Request, res: Response) {
  const dummyData = {
    status: 200,
    message: "upload successful",
    data: {
      file_id: "123",
      user_id: "123",
      file_type: "image",
      file_url: "https://www.google.com"
    }
  };
  try {
    console.log("req.files", req.files);
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(200).json(dummyData);
    }



    const files = req.files as { [fieldname: string]: expressFileUpload.UploadedFile | expressFileUpload.UploadedFile[] };
    const uploadedFiles = Array.isArray(files.content) ? files.content : [files.content];
    const result = await media.uploadFile(req.body, uploadedFiles);

    res.status(200).json(result);
  } catch (error) {
    console.error("Error in addProperty:", error);
    res.status(500).json({ message: 'Error adding property', error });
    return res.status(200).json(dummyData);
  }
}

export default router;

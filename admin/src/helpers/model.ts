import BaseModel from "./base.model";
import { get, post } from "./httpRequest";
import { v4 as uuidv4 } from 'uuid';
import EmailSender from './email';
import TwoFactorAuthHelper from "./2fa.helper";
import CryptoJS from "crypto-js";
import StellarSdk from "stellar-sdk";
import { getAccessRightDetails } from "./accessrights.helper";
export type WebhookData = {
    type: string;
    statusCode: number;
    message: string;
    client_id: string;
    trans_type: string;
    timestamp: string;
    reference_id: string;
    status: string;
    amount: string;
    fee: string;
    currency: string;
    sender_account: string;
    receiver_account: string;
    transaction_id: string;
    meta: string;
    chainInfo?: ChainInfo;
}
type ChainInfo = {
    from_address: string;
    to_address: string;
    amount: string;
    asset_code: string;
    contract_address?: string;
    hash?: string;
    state?: string;
    direction?: string;
}

import jwt from "jsonwebtoken";

const SECRET_KEY = process.env.SECRET_KEY || "DQSJTOOZWCZY2F32762NZRSOD64Y6Q7W"
const COMMON_ROLE_CLIENT_ADMIN = { "id": "0000-0000-0000-0001", "name": "Admin (DF)", "details": "Default Admin", "status": "active" }
const COMMON_ROLE_CLIENT = { "id": "0000-0000-0000-0002", "name": "User (DF)", "details": "Default User", "status": "active" }
const COMMON_ROLE_ADMIN = { "id": "0000-0000-0000-0003", "name": "Admin (DF)", "details": "Default Admin", "status": "active" }



import { sendNotification } from "./FCM";

const mailer = new EmailSender();
export default class Model extends BaseModel {
    async GetIssuerAccount(asset_code: string, arg1: string) {
        return process.env.STELLAR_PAYOUT_ISSUER_SECRET
    }

    // main admin with all client access
    async defaultRole() {
        return COMMON_ROLE_CLIENT_ADMIN;
    }

    // main client with view client access
    async defaultClientRole() {
        return COMMON_ROLE_CLIENT;
    }

    // main admin  account role
    async defaultAdminRole() {
        return COMMON_ROLE_ADMIN;
    }

    async randomPassString(lenth: any) {
        let result = '';
        for (let i = 0; i < lenth; i++) {
            result += Math.floor(Math.random() * 10);
        }
        return result;
    }

    async saveOperationLog(operationType: string, user_id: string, userType: 'admin' | 'client', tableName: string, recordId: string, newData: any, ipAddress: string = "1") {
        try {
            const logData = {
                user_id: user_id,
                operation_type: operationType,
                user_type: userType,
                table_name: tableName,
                record_id: recordId,
                new_data: typeof newData === 'object' ? JSON.stringify(newData) : newData,
                ip_address: ipAddress,
            }
            await this.insertData("operation_logs", logData);
        } catch (error: any) {
         //   console.error("Error saving log:", error);
        }
        return true
    }
    encryptPassword(password: string) {
        const secretKey = process.env.SECRET_KEY || "your-secret-key";
        const encrypteNewdPassword = CryptoJS.AES.encrypt(password, secretKey).toString();
        return encrypteNewdPassword;
    }
    async userFaAuthAccountStatus(data: any) {
        try {

            const userID = (data.user_type === 'admin' || data.user_type === "") ? data.clientId : data.userId;
            const client: any = await this.callQuerySafe(
                `SELECT * FROM user_2fa WHERE user_id = ? AND user_type = ? AND deleted_at IS NULL ORDER BY created_at DESC LIMIT 1`,
                [userID, data.user_type]
            );
            return client;

        } catch (error: any) {
            return;
        }
    }

    async createWallet(clientId: string) {
        try {
            const keys: any = await this.selectDataQuerySafe("client_wallets", { client_wallets: clientId })
            if (keys.length > 0) {
                return keys[0]
            }
            const keypair = StellarSdk.Keypair.random();
            const publicKey = keypair.publicKey();
            const secretKey = keypair.secret();
            const encryptedSecretKey = CryptoJS.AES.encrypt(secretKey, SECRET_KEY).toString();
            const decryptedSecretKey = CryptoJS.AES.decrypt(encryptedSecretKey, SECRET_KEY).toString(CryptoJS.enc.Utf8);
            console.log(`decSSecretKey`, decryptedSecretKey)
            const apiKey = uuidv4();
            const apiKeyData = {
                client_id: clientId,
                public_key: publicKey,
                secret_key: encryptedSecretKey,
            };

            await this.insertData("client_wallets", apiKeyData);
            return this.makeResponse(201, "API keys generated successfully", apiKeyData);
        } catch (error: any) {
            console.error("Error generating API keys:", error);
            return this.makeResponse(500, "Server error");
        }
    }

    makeResponse(status: number, message: string, data: any = null) {
        const response: any = { status, message };
        if (data !== null) {
            response.data = data;
        }
        return response;
    }

    getRandomString() {
        return uuidv4().replace(/-/g, '');
    }

    getTransId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 15; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    async randomPassword(lenth: any) {
        let result = '';
        for (let i = 0; i < lenth; i++) {
            result += Math.floor(Math.random() * 10);
        }
        return result;
    }

    /**
     * Generate a secure password using bcrypt
     * @param password - Plain text password
     * @param saltRounds - Number of salt rounds (default: 12)
     * @returns Promise<string> - Hashed password
     */
    async generatePassword(password: string, saltRounds: number = 12): Promise<string> {
        try {
            const bcrypt = require('bcrypt');
            const hashedPassword = await bcrypt.hash(password, saltRounds);
            return hashedPassword;
        } catch (error: any) {
            console.error('Error generating password hash:', error);
            throw new Error('Failed to generate password hash');
        }
    }

    /**
     * Validate password against policy requirements
     * @param password - Password to validate
     * @returns Object with validation result and details
     */
    validatePasswordPolicy(password: string): { isValid: boolean; errors: string[]; strength: 'weak' | 'medium' | 'strong'; score: number; } {
        const errors: string[] = [];
        let score = 0;

        // Check minimum length (8-12 characters)
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        } else if (password.length >= 8) {
            score += 1;
        }
        if (password.length >= 12) {
            score += 1;
        }

        // Check for uppercase letters
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        } else {
            score += 1;
        }

        // Check for lowercase letters
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        } else {
            score += 1;
        }

        // Check for numbers
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        } else {
            score += 1;
        }

        // Check for special characters
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        } else {
            score += 1;
        }

        // Check for common weak passwords
        const commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        if (commonPasswords.includes(password.toLowerCase())) {
            errors.push('Password is too common, please choose a more unique password');
            score = 0;
        }

        // Determine strength based on score
        let strength: 'weak' | 'medium' | 'strong' = 'weak';
        if (score >= 5) {
            strength = 'strong';
        } else if (score >= 3) {
            strength = 'medium';
        }

        const isValid = errors.length === 0 && score >= 3;

        return {
            isValid,
            errors,
            strength,
            score
        };
    }

    /**
     * Verify a password against its hash
     * @param password - Plain text password
     * @param hash - Hashed password to compare against
     * @returns Promise<boolean> - True if password matches
     */
    async verifyPassword(password: string, hash: string): Promise<boolean> {
        try {
            const bcrypt = require('bcrypt');
            const isValid = await bcrypt.compare(password, hash);
            return isValid;
        } catch (error: any) {
            console.error('Error verifying password:', error);
            return false;
        }
    }

    /**
     * Generate a secure random password
     * @param length - Length of password (default: 12)
     * @param includeSpecialChars - Whether to include special characters (default: true)
     * @returns string - Generated password
     */
    generateSecurePassword(length: number = 12, includeSpecialChars: boolean = true): string {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';
        const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        let chars = uppercase + lowercase + numbers;
        if (includeSpecialChars) {
            chars += special;
        }

        let password = '';

        // Ensure at least one character from each required category
        password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
        password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
        password += numbers.charAt(Math.floor(Math.random() * numbers.length));
        if (includeSpecialChars) {
            password += special.charAt(Math.floor(Math.random() * special.length));
        }

        // Fill the rest randomly
        for (let i = password.length; i < length; i++) {
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    /**
     * Track failed login attempts and implement rate limiting
     * @param identifier - User identifier (email, username, etc.)
     * @param maxAttempts - Maximum allowed attempts (default: 5)
     * @param lockoutDuration - Lockout duration in minutes (default: 15)
     * @returns Object with lockout status and remaining attempts
     */
    async trackFailedLoginAttempt(identifier: string, maxAttempts: number = 5, lockoutDuration: number = 15): Promise<{
        isLocked: boolean;
        remainingAttempts: number;
        lockoutExpiresAt?: Date;
        message?: string;
    }> {
        try {
            const now = new Date();
            const lockoutExpiresAt = new Date(now.getTime() + lockoutDuration * 60 * 1000);

            // Check if user is currently locked out
            const existingLockout = await this.selectDataQuerySafe("login_attempts", {
                identifier,
                is_locked: true
            });

            if (existingLockout.length > 0) {
                const lockout = existingLockout[0];
                const lockoutTime = new Date(lockout.lockout_expires_at);

                if (lockoutTime > now) {
                    return {
                        isLocked: true,
                        remainingAttempts: 0,
                        lockoutExpiresAt: lockoutTime,
                        message: `Account is locked. Try again after ${lockoutTime.toLocaleString()}`
                    };
                } else {
                    // Lockout expired, reset attempts
                    await this.updateData("login_attempts",
                        `identifier = '${identifier}'`,
                        {
                            failed_attempts: 0,
                            is_locked: false,
                            lockout_expires_at: null
                        }
                    );
                }
            }

            // Get current failed attempts
            const attempts = await this.selectDataQuerySafe("login_attempts", { identifier });

            if (attempts.length === 0) {
                // First failed attempt
                await this.insertData("login_attempts", {
                    identifier,
                    failed_attempts: 1,
                    last_attempt_at: now.toISOString(),
                    is_locked: false
                });

                return {
                    isLocked: false,
                    remainingAttempts: maxAttempts - 1
                };
            }

            const currentAttempts = attempts[0];
            const newAttemptCount = currentAttempts.failed_attempts + 1;

            if (newAttemptCount >= maxAttempts) {
                // Lock the account
                await this.updateData("login_attempts",
                    `identifier = '${identifier}'`,
                    {
                        failed_attempts: newAttemptCount,
                        last_attempt_at: now.toISOString(),
                        is_locked: true,
                        lockout_expires_at: lockoutExpiresAt.toISOString()
                    }
                );

                return {
                    isLocked: true,
                    remainingAttempts: 0,
                    lockoutExpiresAt,
                    message: `Too many failed attempts. Account locked until ${lockoutExpiresAt.toLocaleString()}`
                };
            } else {
                // Update failed attempts
                await this.updateData("login_attempts",
                    `identifier = '${identifier}'`,
                    {
                        failed_attempts: newAttemptCount,
                        last_attempt_at: now.toISOString()
                    }
                );

                return {
                    isLocked: false,
                    remainingAttempts: maxAttempts - newAttemptCount
                };
            }
        } catch (error: any) {
            console.error('Error tracking failed login attempt:', error);
            // Return not locked in case of error to avoid blocking legitimate users
            return {
                isLocked: false,
                remainingAttempts: maxAttempts
            };
        }
    }

    /**
     * Reset failed login attempts for a user (call on successful login)
     * @param identifier - User identifier
     */
    async resetFailedLoginAttempts(identifier: string): Promise<void> {
        try {
            await this.updateData("login_attempts",
                `identifier = '${identifier}'`,
                {
                    failed_attempts: 0,
                    is_locked: false,
                    lockout_expires_at: null
                }
            );
        } catch (error: any) {
            console.error('Error resetting failed login attempts:', error);
        }
    }


    getMySQLDateTime_() {
        return new Date().toISOString().slice(0, 19).replace('T', ' ');
    }

    getMySQLDateTime_future(hours: number, unit: string) {
        const date = new Date(Date.now() + hours * 60 * 60 * 1000);
        const pad = (n: any) => n.toString().padStart(2, '0');
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
            `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    }

    async updateRoleAccessRights(roleId: string, accessRights: string[]) {

        const EXISTING_RIGHTS: any = await this.callRawQuery(`SELECT id, access_right_id FROM role_access_rights  WHERE role_id = '${roleId}' AND deleted_at IS NULL`);
        console.log("EXISTING_RIGHTS", EXISTING_RIGHTS);
        const EXISTING_IDS = EXISTING_RIGHTS.map((right: any) => right.access_right_id);
        const NEW_IDS = accessRights;

        if (EXISTING_IDS.length > 0) {
            const IDS_TO_DELETE = EXISTING_IDS.filter((id: string) => !NEW_IDS.includes(id));
            if (IDS_TO_DELETE.length > 0) {
                await this.updateData(
                    "role_access_rights",
                    `role_id = '${roleId}' AND access_right_id IN ('${IDS_TO_DELETE.join("','")}')`,
                    { deleted_at: this.getMySQLDateTime_() }
                );
            }
            console.log("EXISTING_RIGHTS TO DELETE", IDS_TO_DELETE);
        }

        let ids_to_add: any = NEW_IDS;
        if (EXISTING_IDS.length > 0) {
            ids_to_add = NEW_IDS.filter((id: string) => !EXISTING_IDS.includes(id));
        }
        console.log("EXISTING_RIGHTS TO ADD", ids_to_add);
        // finally add the rights
        for (const ACCESS_RIGHT_ID of ids_to_add) {
            const ROLE_ACCESS_RIGHT_DATA = {
                id: uuidv4(),
                role_id: roleId,
                access_right_id: ACCESS_RIGHT_ID,
                created_at: this.getMySQLDateTime_()
            };
            await this.insertData("role_access_rights", ROLE_ACCESS_RIGHT_DATA);
        }

    }


    async sendAppNotification(userId: string, operation: string, name = '', otp = '') {
        console.log(`SEND_1`, { userId, operation });



        console.log(`SEND_2`, { userId, operation });

        const messageBody: any = await this.callQuerySafe("SELECT * FROM notification_templates WHERE operation = ? AND channel != 'EMAIL'", [operation]);
        if (messageBody.length === 0) {
            console.log(`SEND_3`, messageBody);
            return this.makeResponse(404, "Operation not found");
        }

        const token = ""
        const message = messageBody[0]['body'];
        const subject = messageBody[0]['title'];

        const newMessage = this.constructSmsMessage(message, name, otp, "", "");
        const data = { title: subject, body: newMessage };

        console.log(`SEND_4`, data);

        const response = await sendNotification(token, data);
        console.log(`SEND_5`, response);

        return false;
    }

    async sendEmail(operation: string, email: string, name = "", otp = "", tableData: any = [], code: string = '') {
        try {
            const messageBody = await this.selectDataQuerySafe("notification_templates", { operation });
            if (messageBody.length === 0) {
                return this.makeResponse(404, "Operation not found");
            }

            let listHtml = "<ul>";
            tableData.forEach((item: any) => {
                listHtml += `<li>${item}</li>`;
            });
            listHtml += "</ul>";

            const message = messageBody[0]['body'];
            const subject = messageBody[0]['title'];

            const newMessage = this.constructSmsMessage(message, name, otp, listHtml, code);
            mailer.sendMail(email, subject, subject, newMessage);

            return true;
        } catch (error) {

            console.log('error  sending email   ----- ', error)
            return this.makeResponse(203, "Error fetching company");
        }
    }

    constructSmsMessage(template: string, name: string, otp: string, listHtml: any, code: string): string {
        const data: any = { name, otp, code, listHtml };
        for (const key in data) {
            if (Object.prototype.hasOwnProperty.call(data, key)) {
                template = template.replace(new RegExp(`{${key}}`, 'g'), data[key]);
            }
        }
        return template;
    }

    generateRandom4DigitNumber() {
        return "10" + Math.floor(100000 + Math.random() * 900000);
    }

    generateRandomDigitNumber(length: number) {
        return Math.floor(10066000 + Math.random() * 90066000).toString().slice(0, length);
    }
    async getapikeys(clientId: string) {
        return await this.selectDataQuerySafe("api_keys", { client_id: clientId });
    }
    async getClientWallet(clientId: string) {
        const wallet = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
        return wallet

    }

    async saveWebhookLog(cl_id: any, clientId: any, transId: any, callbackUrl: any, event: any, webhookData: any, status: any, direction: any = "OUTGOING", provider = "MUDA") {
        try {
            await this.insertData("webhook_logs", {
                cl_id: cl_id,
                client_id: clientId,
                trans_id: transId,
                webhook_url: callbackUrl,
                event: event,
                webhook_data: JSON.stringify(webhookData),
                status_code: status,
                timestamp: this.formatedDate(new Date()),
                direction: direction,
                provider: provider
            });
        } catch (error: any) {
            console.error(`❌ Error in saveWebhookLog:`, error);
            return false;
        }
    }

    formatedDate(updated_at: any) {
        return new Date(updated_at).toISOString().slice(0, 19).replace('T', ' ')
    }

    async updateWebhookLog(cl_id: any, response: any, response_code: any, updated_at: any) {
        try {
            const data = {
                response: JSON.stringify(response),
                response_code: response_code,
                updated_at: this.formatedDate(new Date())
            }
            await this.updateData("webhook_logs", `cl_id = '${cl_id}'`, data)
        } catch (error: any) {
            console.error(`❌ Error in updateWebhookLog:`, error);
            return false;
        }
    }

    async sendWebhook(clientId: any, callbackUrl: any, transId: any, event: any, webhookData: WebhookData) {

        const cl_id = this.getTransId()

        try {
            console.log(`sendWebhook3`, { clientId, callbackUrl, transId, event, webhookData })
            await this.saveWebhookLog(cl_id, clientId, transId, callbackUrl, event, webhookData, 202, "RAIL")

            const response = await post(callbackUrl, webhookData);
            console.log(`sendWebhook5`, { response })
            await this.updateWebhookLog(cl_id, response, response.status, this.formatedDate(new Date()))
            return response;
        } catch (error: any) {
            console.log(`sendWebhook4`, { error })
            await this.updateWebhookLog(cl_id, error, error.status || 500, this.formatedDate(new Date()))
            return false;
        }
    }

    async saveWebhook(
        clientId: string,
        transId: any,
        event: any,
        webhookData: any
    ) {
        try {
            const cl_id = this.getTransId()
            console.log(`saveWebhook1`, { cl_id, clientId, transId, event, webhookData })
            await this.saveWebhookLog(cl_id, clientId, transId, "", "INITIAL", webhookData, 202, "RAIL")
            const clientInfo: any = await this.selectDataQuerySafe("webhooks", { client_id: clientId });
            if (clientInfo.length === 0) {
                console.error(`No webhook URL found for client ID: ${clientId}`);
                return false;
            }
            console.log(`saveWebhook2`, { clientInfo })


            for (let i = 0; i < clientInfo.length; i++) {
                const callbackUrl = clientInfo[i]['callback_url'];
                console.log(`saveWebhook3`, { callbackUrl })
                await this.sendWebhook(clientId, callbackUrl, transId, event, webhookData);
            }
            return true

        } catch (error: any) {
            console.log(`saveWebhook4`, { error })
            return false;
        }
    }

    async getDecryptedApiKey(clientId: string) {
        try {
            const apiKeyRecord = await this.selectDataQuerySafe("client_wallets", { client_id: clientId });
            if (apiKeyRecord.length === 0) {
                return this.createWallet(clientId);
            }

            return {
                client_id: clientId,
                public_key: apiKeyRecord[0].public_key,
                secret_key: "" // This is now the hashed value
            }

        } catch (error: any) {
            console.error("Error retrieving API keys:", error);
            return null
        }
    }

    async getBusinessByEmail(email: string) {
        return await this.selectDataQuerySafe("clients", { contact_email: email });
    }

    validateDomain(domain: string) {
        const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const cleanDomain = domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
        return domainRegex.test(cleanDomain);
    }

    validateAndCleanDomain(domain: string) {
        return domain.replace(/^(?:https?:\/\/)?(?:www\.)?/, '').split('/')[0];
    }

    doesEmailDomainMatch(email: string, domain: string) {
        return email.split('@')[1] === domain;
    }

    async getClientInfo(clientId: string) {
        return await this.selectDataQuerySafe("clients", { client_id: clientId });
    }

    async getUserClientByUserId(userId: any) {
        return await this.selectDataQuerySafe("client_logins", { id: userId })
    }

    async getUserClientLogin(email: any) {
        return await this.selectDataQuerySafe("client_logins", { email })
    }

    async getCampanyOwner(clientId: any, userId: any) {
        return await this.callQuerySafe("SELECT * FROM client_logins WHERE client_id = ? AND id = ? AND role = 'owner'", [clientId, userId])
    }

    async sendUserAuthToken(userId: string) {
        const user = await this.getUserClientByUserId(userId)
        console.log(`client3`, user)

        if (user.length === 0) {
            return false
        }
        const token = await this.getOtp(user[0].email, userId, 'code')
        this.sendEmail("AUTH_TOKEN", user[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${user[0].email}`);

    }

    async sendClientAuthToken(clientId: string) {
        const client = await this.getClientInfo(clientId)
        if (client.length === 0) {
            return false
        }
        const token = await this.getOtp(client[0].email, clientId, 'code')
        this.sendEmail("AUTH_TOKEN", client[0].email, token, token);
        return this.makeResponse(200, `Token sent successfully to ${client[0].email}`);

    }

    async getOtp(email: string, user_id: string, otpType: string = 'otp') {
        console.log(`client4`, email, user_id, otpType)

        const user: any = await this.selectDataQuerySafe("user_otp", { email, user_id });
        let otp = this.generateRandom4DigitNumber().toString();

        if (otpType === 'code') {
            otp = this.generateRandomDigitNumber(8);
        }

        console.log(`client5`, { email, otp });

        if (user.length === 0) {
            await this.insertData('user_otp', { user_id, email, otp });
        } else {
            await this.updateData('user_otp', `email = '${email}' and user_id = '${user_id}'`, { otp });
        }

        return otp;
    }

    async saveNotification(title: string, companyId: string, message: any) {
        const newNotification = { title, companyId, message };
        return await this.insertData('notifications', newNotification);
    }

    async getDocVerifiers(docId: string) {
        return await this.callRawQuery(`SELECT * FROM verifiers WHERE doc_id='${docId}'`);
    }

    async confirmUser2Fa(data: any) {
        try {

            const user_type: any = data?.user_type || 'admin';
            const client: any = await this.userFaAuthAccountStatus({ ...data, user_type: user_type })
            const responseData: any = await TwoFactorAuthHelper.verifySecret(client[0]?.secret, data.token)
            if (!responseData?.status) {
                return { status: false, message: 'Invalid 2fa token' }
            }
            return { status: true, message: '' };
        } catch (error: any) {
            return { status: true, message: 'Invalid 2fa token' };
        }
    }


    async checkDefaultPasswordExpiry(default_password_expiry: string, expiry_hours: number) {
        if (default_password_expiry === null) {
            return true;
        }
        const currentTime: any = new Date();
        const defaultPasswordExpiry: any = new Date(default_password_expiry);
        return currentTime.getTime() < defaultPasswordExpiry.getTime()
    }


    // get logged user access right to action app events 
    async getLoggedUserAccessRights(userId: string, accessRight: string, type: string = "admin") {
        try {

            const accessRightId = await getAccessRightDetails(accessRight)?.id;
            if (!accessRightId || accessRightId === undefined || accessRightId === "") {
                return false;
            }

            let user: any;
            if (type === "admin") {
                user = await this.callRawQuery(`SELECT * FROM system_users WHERE id = '${userId}'`);
            } else {
                user = await this.getUserClientByUserId(userId)
            }
            if (user.length === 0) {
                return false
            }

            let acessRights: any = [];
            if (user[0].role === "0000-0000-0000-0003") {

                const acessRightsAll: any = await this.callRawQuery(`
                                                  SELECT r.* 
                                                  FROM access_rights r WHERE (r.type = 'admin' OR r.type IS NULL) AND r.deleted_at IS NULL
                                                    ORDER BY r.created_at DESC`);
                const allowedRightsIds: any = ["d1d46ab5-41c1-11f0-bd21-16243d6fb08b", "d1d46d88-41c1-11f0-bd21-16243d6fb08b", "d1b17217-41c1-11f0-bd21-16243d6fb08b", "d17176b6-41c1-11f0-bd21-16243d6fb08b", "d149ac93-41c1-11f0-bd21-16243d6fb08b"]
                acessRights = acessRightsAll.filter((role: any) => allowedRightsIds.includes(role.id));
                acessRights.forEach((role: any) => {
                    role.role_id = role.id;
                });
                acessRights = acessRights.filter((role: any) => role.role_id === accessRightId);


            } else {
                acessRights = (await this.callRawQuery(` SELECT ar.id as role_id
                                                        FROM access_rights ar 
                                                          INNER JOIN role_access_rights rar ON ar.id = rar.access_right_id
                                                            WHERE rar.role_id = '${user[0].role}'
                                                              AND rar.status = 'active'  
                                                                AND ar.deleted_at IS NULL 
                                                                  AND rar.deleted_at IS NULL`)) as any[];
                acessRights = acessRights.filter((role: any) => role.role_id === accessRightId);
            }
            console.log("accessRightId >>>>>> 3");
            return acessRights.length > 0 ? true : false;
        } catch (error: any) {

            console.log("accessRightId >>>>>> 4", error);
            return false;
        }

    }




    // async saveOperationLog(trans_id: string, status: string, step: any, response_code: number, description: string, data: any) {
    //     try {
    //         const logData = {
    //             trans_id: trans_id,
    //             status: status,
    //             step: step,
    //             response_code: response_code,
    //             description: description,
    //             data: typeof data === 'string' ? data : JSON.stringify(data)
    //         }
    //         return await this.insertData("transactions_log", logData);
    //     } catch (error: any) {
    //         console.error("Error saving transaction log:", error);
    //         return false;
    //     }

    // }


    async saveTierFees(data: any, productId: string, typeFee: string) {
        if (typeFee === "product") {

            // delete old tier_fees
            await this.deleteData("tier_fees", `product_id = '${productId}'`);

            // for each tier_fee_range
            for (const tier_fee_range of data.tier_fee_range) {
                const saveData_ = {
                    id: uuidv4(),
                    product_id: productId,
                    min_amount: tier_fee_range.min_amount,
                    max_amount: tier_fee_range.max_amount,
                    fee_type: tier_fee_range?.fee_type ?? "FLAT",
                    fee_value: tier_fee_range?.fee_amount ?? 0,
                    created_at: this.getMySQLDateTime_()
                }
                await this.insertData("tier_fees", saveData_);
            }
        }

        if (typeFee === "custome_fee") {

            // delete old tier_fees
            await this.deleteData("tier_fees", `custome_fee_id = '${productId}'`);

            // for each tier_fee_range
            for (const tier_fee_range of data.tier_fee_range) {
                const saveData_ = {
                    id: uuidv4(),
                    custome_fee_id: productId,
                    min_amount: tier_fee_range.min_amount,
                    max_amount: tier_fee_range.max_amount,
                    fee_type: tier_fee_range?.fee_type ?? "FLAT",
                    fee_value: tier_fee_range?.fee_amount ?? 0,
                    created_at: this.getMySQLDateTime_()
                }
                await this.insertData("tier_fees", saveData_);
            }
        }


    }

    /**
     * Secure error response - prevents verbose error information from being exposed
     * @param statusCode - HTTP status code
     * @param message - User-friendly error message
     * @param data - Optional data to include (never include error objects)
     * @returns Formatted response object
     */
    makeSecureResponse(statusCode: number, message: string, data?: any) {
        // Log the full error details internally for debugging
        if (data && typeof data === 'object' && data.error) {
            console.error('Internal error details:', data.error);
            // Remove sensitive error information from response
            delete data.error;
        }

        return this.makeResponse(statusCode, message, data);
    }

    /**
     * Handle errors securely without exposing internal details
     * @param error - The error object
     * @param context - Context where the error occurred
     * @param userMessage - User-friendly error message
     * @returns Secure error response
     */
    handleErrorSecurely(error: any, context: string, userMessage: string = "An error occurred") {
        // Log the full error internally for debugging
        console.error(`Error in ${context}:`, error);

        // Return a secure response without exposing error details
        return this.makeResponse(500, userMessage);
    }
}

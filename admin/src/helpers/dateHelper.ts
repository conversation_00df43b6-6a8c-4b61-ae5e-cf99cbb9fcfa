/**
 * Date Helper Utility Functions
 * Provides consistent date handling across the application
 * 
 * Usage Examples:
 * 
 * // Get current date
 * const today = CURRENT_DATE(); // Returns "2024-01-15"
 * 
 * // Get current date and time
 * const now = CURRENT_DATETIME(); // Returns "2024-01-15T10:30:45.123Z"
 * 
 * // Get MySQL formatted date
 * const mysqlDate = getCurrentDateMySQL(); // Returns "2024-01-15"
 * 
 * // Get MySQL formatted datetime
 * const mysqlDateTime = getCurrentDateTimeMySQL(); // Returns "2024-01-15 10:30:45"
 * 
 * // Check if date is today
 * const isToday = isToday('2024-01-15'); // Returns true/false
 * 
 * // Get first day of last 6 months (updated function)
 * const sixMonthsAgo = getFirstDayOfCurrentMonth(); // Returns "2023-07-01" (if today is 2024-01-15)
 * 
 * // Get first day of current month
 * const thisMonth = getFirstDayOfThisMonth(); // Returns "2024-01-01"
 * 
 * // Get first day of specific months ago
 * const threeMonthsAgo = getFirstDayOfMonthsAgo(3); // Returns "2023-10-01"
 * 
 * // Get current month range
 * const monthRange = getCurrentMonthRange(); // Returns {start: "2024-01-01", end: "2024-02-01"}
 */

/**
 * Get current date in YYYY-MM-DD format
 */
export const CURRENT_DATE = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get current date and time in ISO format
 */
export const CURRENT_DATETIME = (): string => {
  return new Date().toISOString();
};

/**
 * Get current date in MySQL format (YYYY-MM-DD)
 */
export const getCurrentDateMySQL = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get current date and time in MySQL format (YYYY-MM-DD HH:MM:SS)
 */
export const getCurrentDateTimeMySQL = (): string => {
  const now = new Date();
  return now.toISOString().slice(0, 19).replace('T', ' ');
};

/**
 * Format date for MySQL queries
 * @param date - Date object or date string
 * @returns Formatted date string
 */
export const formatDateForMySQL = (date: Date | string): string => {
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

/**
 * Get first day of the last 6 months in YYYY-MM-DD format
 */
export const getFirstDayOfCurrentMonth = (): string => {
  const now = new Date();
  // Subtract 6 months from current date
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6, 1);
  return sixMonthsAgo.toISOString().split('T')[0];
};

/**
 * Get first day of current month in YYYY-MM-DD format
 */
export const getFirstDayOfThisMonth = (): string => {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
};

/**
 * Get first day of N months ago in YYYY-MM-DD format
 * @param monthsAgo - Number of months to go back (default: 6)
 */
export const getFirstDayOfMonthsAgo = (monthsAgo: number = 6): string => {
  const now = new Date();
  const targetDate = new Date(now.getFullYear(), now.getMonth() - monthsAgo, 1);
  return targetDate.toISOString().split('T')[0];
};

/**
 * Get first day of next month in YYYY-MM-DD format
 */
export const getFirstDayOfNextMonth = (): string => {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString().split('T')[0];
};

/**
 * Check if a date is today
 * @param date - Date to check
 * @returns boolean
 */
export const isToday = (date: Date | string): boolean => {
  const today = new Date().toISOString().split('T')[0];
  const checkDate = new Date(date).toISOString().split('T')[0];
  return today === checkDate;
};

/**
 * Get date range for current month
 * @returns Object with start and end dates
 */
export const getCurrentMonthRange = () => {
  return {
    start: getFirstDayOfCurrentMonth(),
    end: getFirstDayOfNextMonth()
  };
}; 
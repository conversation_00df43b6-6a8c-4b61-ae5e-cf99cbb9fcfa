import * as cron from 'node-cron';

import Model from './model';
import WalletService from '../services/wallet.service';

class CronService {
    constructor() {
        console.log("Admin Cron Service initiated.");
        this.scheduleEverySixHours();
    }

    private scheduleEverySixHours() {
        cron.schedule('0 */6 * * *', async () => {
            console.log('Running every six hours admin task...');
            try {
                console.log('Every six hours admin task completed.');
            } catch (error) {
                console.error('Error running every six hours admin task:', error);
            }
        });
    }
}
type AccessRight = {
    id: string;
    name: string;
    tag: string;
    status?: string;
    type?: string | null;
    roleGroup?: string | null;
    createdAt?: string;
    updatedAt?: string | null;
    deletedAt?: string | null;
  };
  
  
  export const ACCESS_RIGHTS: Record<string, AccessRight> = {
    createNewAccount: {
      id: '6923c175-9b23-46b3-9198-15656e3b4311',
      name: 'Create New account',
      tag: 'createNewAccount',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 12:54:58',
      updatedAt: '2025-05-27 12:56:01',
      deletedAt: null
    },
    changingRates: {
      id: '6923c175-9b23-46b3-9198-15656e3b4323',
      name: 'Changing rates',
      tag: 'changingRates',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 12:53:42',
      updatedAt: '2025-05-27 12:55:34',
      deletedAt: null
    },
    addBusinessAccount: {
      id: '6923c175-9b23-46b3-9198-15656e3b43b1',
      name: 'Add Business Account',
      tag: 'addBusinessAccount',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 09:16:38',
      updatedAt: '2025-05-27 12:20:51',
      deletedAt: null
    },
    addAdminAccount: {
      id: '6923c175-9b23-46b3-9198-15656e3b43b2',
      name: 'Add Admin Account',
      tag: 'addAdminAccount',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 08:55:05',
      updatedAt: '2025-05-27 09:16:06',
      deletedAt: null
    },
    addFloat: {
      id: '6923c175-9b23-46b3-9198-15656e3b43b3',
      name: 'Add Float',
      tag: 'addFloat',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 12:54:58',
      updatedAt: '2025-05-27 12:56:30',
      deletedAt: null
    },
    maker: {
      id: '6923c175-9b23-46b3-9198-15656e3b43b4',
      name: 'Maker',
      tag: 'maker',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 12:54:58',
      updatedAt: '2025-05-27 12:56:30',
      deletedAt: null
    },
    checker: {
      id: '6923c175-9b23-46b3-9198-15656e3b43b6',
      name: 'Checker',
      tag: 'checker',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-05-27 12:54:58',
      updatedAt: '2025-05-27 12:56:30',
      deletedAt: null
    },
    usersView: {
      id: 'd12fafcd-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.view (View user list and details)',
      tag: 'usersView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    usersCreate: {
      id: 'd12fb4f2-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.create (Create new users)',
      tag: 'usersCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    usersEdit: {
      id: 'd12fb60b-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.edit (Edit user details)',
      tag: 'usersEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    usersDelete: {
      id: 'd12fb654-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.delete (Delete users)',
      tag: 'usersDelete',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    usersApprove: {
      id: 'd12fb68b-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.approve (Approve user changes)',
      tag: 'usersApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    users2faManage: {
      id: 'd12fb6ca-41c1-11f0-bd21-16243d6fb08b',
      name: 'users.2fa_manage (Manage 2FA settings)',
      tag: 'users2faManage',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    rolesView: {
      id: 'd13adfc2-41c1-11f0-bd21-16243d6fb08b',
      name: 'roles.view (View roles and permissions)',
      tag: 'rolesView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    rolesCreate: {
      id: 'd13ae2ce-41c1-11f0-bd21-16243d6fb08b',
      name: 'roles.create (Create new roles)',
      tag: 'rolesCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    rolesEdit: {
      id: 'd13ae333-41c1-11f0-bd21-16243d6fb08b',
      name: 'roles.edit (Edit role permissions)',
      tag: 'rolesEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    rolesDelete: {
      id: 'd13ae37b-41c1-11f0-bd21-16243d6fb08b',
      name: 'roles.delete (Delete roles)',
      tag: 'rolesDelete',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    rolesApprove: {
      id: 'd13ae3b4-41c1-11f0-bd21-16243d6fb08b',
      name: 'roles.approve (Approve role changes)',
      tag: 'rolesApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsView: {
      id: 'd149ac93-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.view (View transactions)',
      tag: 'transactionsView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsCreate: {
      id: 'd149af62-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.create (Create transactions)',
      tag: 'transactionsCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsEdit: {
      id: 'd149afd6-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.edit (Edit transactions)',
      tag: 'transactionsEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsApprove: {
      id: 'd149b00e-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.approve (Approve transactions)',
      tag: 'transactionsApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsReverse: {
      id: 'd149b045-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.reverse (Reverse transactions)',
      tag: 'transactionsReverse',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    transactionsExport: {
      id: 'd149b07b-41c1-11f0-bd21-16243d6fb08b',
      name: 'transactions.export (Export transaction data)',
      tag: 'transactionsExport',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    depositsView: {
      id: 'd15aa0ca-41c1-11f0-bd21-16243d6fb08b',
      name: 'deposits.view (View deposits)',
      tag: 'depositsView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    depositsCreate: {
      id: 'd15aa3db-41c1-11f0-bd21-16243d6fb08b',
      name: 'deposits.create (Create deposits)',
      tag: 'depositsCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    depositsEdit: {
      id: 'd15aa43a-41c1-11f0-bd21-16243d6fb08b',
      name: 'deposits.edit (Edit deposits)',
      tag: 'depositsEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    depositsApprove: {
      id: 'd15aa47c-41c1-11f0-bd21-16243d6fb08b',
      name: 'deposits.approve (Approve deposits)',
      tag: 'depositsApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    depositsReverse: {
      id: 'd15aa4bd-41c1-11f0-bd21-16243d6fb08b',
      name: 'deposits.reverse (Reverse deposits)',
      tag: 'depositsReverse',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    feesView: {
      id: 'd1683c57-41c1-11f0-bd21-16243d6fb08b',
      name: 'fees.view (View fee configurations)',
      tag: 'feesView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    feesCreate: {
      id: 'd1683f29-41c1-11f0-bd21-16243d6fb08b',
      name: 'fees.create (Create fee rules)',
      tag: 'feesCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    feesEdit: {
      id: 'd1683f83-41c1-11f0-bd21-16243d6fb08b',
      name: 'fees.edit (Edit fee rules)',
      tag: 'feesEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    feesApprove: {
      id: 'd1683fc7-41c1-11f0-bd21-16243d6fb08b',
      name: 'fees.approve (Approve fee changes)',
      tag: 'feesApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    feesDelete: {
      id: 'd1684000-41c1-11f0-bd21-16243d6fb08b',
      name: 'fees.delete (Delete fee rules)',
      tag: 'feesDelete',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    ratesView: {
      id: 'd17176b6-41c1-11f0-bd21-16243d6fb08b',
      name: 'rates.view (View exchange rates)',
      tag: 'ratesView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    ratesCreate: {
      id: 'd17179a5-41c1-11f0-bd21-16243d6fb08b',
      name: 'rates.create (Create new rates)',
      tag: 'ratesCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    ratesEdit: {
      id: 'd1717a13-41c1-11f0-bd21-16243d6fb08b',
      name: 'rates.edit (Edit rates)',
      tag: 'ratesEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    ratesApprove: {
      id: 'd1717a54-41c1-11f0-bd21-16243d6fb08b',
      name: 'rates.approve (Approve rate changes)',
      tag: 'ratesApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    ratesDelete: {
      id: 'd1717a8c-41c1-11f0-bd21-16243d6fb08b',
      name: 'rates.delete (Delete rates)',
      tag: 'ratesDelete',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    businessView: {
      id: 'd17a92ca-41c1-11f0-bd21-16243d6fb08b',
      name: 'business.view (View business settings)',
      tag: 'businessView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    businessEdit: {
      id: 'd17a95ee-41c1-11f0-bd21-16243d6fb08b',
      name: 'business.edit (Edit business settings)',
      tag: 'businessEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    businessApprove: {
      id: 'd17a964e-41c1-11f0-bd21-16243d6fb08b',
      name: 'business.approve (Approve business changes)',
      tag: 'businessApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    walletView: {
      id: 'd1854a74-41c1-11f0-bd21-16243d6fb08b',
      name: 'wallet.view (View wallet details)',
      tag: 'walletView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    walletCreate: {
      id: 'd1855117-41c1-11f0-bd21-16243d6fb08b',
      name: 'wallet.create (Create wallets)',
      tag: 'walletCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    walletEdit: {
      id: 'd18551be-41c1-11f0-bd21-16243d6fb08b',
      name: 'wallet.edit (Edit wallet settings)',
      tag: 'walletEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    walletApprove: {
      id: 'd18551fa-41c1-11f0-bd21-16243d6fb08b',
      name: 'wallet.approve (Approve wallet changes)',
      tag: 'walletApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    reportsView: {
      id: 'd194828f-41c1-11f0-bd21-16243d6fb08b',
      name: 'reports.view (View reports)',
      tag: 'reportsView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    reportsGenerate: {
      id: 'd19485ab-41c1-11f0-bd21-16243d6fb08b',
      name: 'reports.generate (Generate reports)',
      tag: 'reportsGenerate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    reportsExport: {
      id: 'd1948617-41c1-11f0-bd21-16243d6fb08b',
      name: 'reports.export (Export reports)',
      tag: 'reportsExport',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    reportsApprove: {
      id: 'd1948657-41c1-11f0-bd21-16243d6fb08b',
      name: 'reports.approve (Approve report generation)',
      tag: 'reportsApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:54',
      updatedAt: null,
      deletedAt: null
    },
    clientsView: {
      id: 'd1b17217-41c1-11f0-bd21-16243d6fb08b',
      name: 'clients.view (View client list and details)',
      tag: 'clientsView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    clientsCreate: {
      id: 'd1b1753e-41c1-11f0-bd21-16243d6fb08b',
      name: 'clients.create (Create new clients)',
      tag: 'clientsCreate',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    clientsEdit: {
      id: 'd1b175a9-41c1-11f0-bd21-16243d6fb08b',
      name: 'clients.edit (Edit client details)',
      tag: 'clientsEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    clientsApprove: {
      id: 'd1b175e4-41c1-11f0-bd21-16243d6fb08b',
      name: 'clients.approve (Approve client changes)',
      tag: 'clientsApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    clientsDelete: {
      id: 'd1b17618-41c1-11f0-bd21-16243d6fb08b',
      name: 'clients.delete (Delete clients)',
      tag: 'clientsDelete',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    settingsView: {
      id: 'd1c6ea65-41c1-11f0-bd21-16243d6fb08b',
      name: 'settings.view (View system settings)',
      tag: 'settingsView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    settingsEdit: {
      id: 'd1c6ed91-41c1-11f0-bd21-16243d6fb08b',
      name: 'settings.edit (Edit system settings)',
      tag: 'settingsEdit',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    settingsApprove: {
      id: 'd1c6edef-41c1-11f0-bd21-16243d6fb08b',
      name: 'settings.approve (Approve setting changes)',
      tag: 'settingsApprove',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    dashboardView: {
      id: 'd1d46ab5-41c1-11f0-bd21-16243d6fb08b',
      name: 'dashboard.view (View dashboard)',
      tag: 'dashboardView',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    dashboardExport: {
      id: 'd1d46d88-41c1-11f0-bd21-16243d6fb08b',
      name: 'dashboard.export (Export dashboard data)',
      tag: 'dashboardExport',
      status: 'active',
      type: null,
      roleGroup: null,
      createdAt: '2025-06-05 04:01:55',
      updatedAt: null,
      deletedAt: null
    },
    initiateWithdrawals: {
      id: 'f575d236-6089-11f0-bd21-16243d6fb08b',
      name: 'Initiate withdrawals & sending of funds',
      tag: 'initiateWithdrawals',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    approveWithdrawals: {
      id: 'f575d618-6089-11f0-bd21-16243d6fb08b',
      name: 'Approve withdrawals and payouts',
      tag: 'approveWithdrawals',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    viewWallets: {
      id: 'f575f037-6089-11f0-bd21-16243d6fb08b',
      name: 'View wallets',
      tag: 'viewWallets',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    viewTransactions: {
      id: 'f575f178-6089-11f0-bd21-16243d6fb08b',
      name: 'View transactions',
      tag: 'viewTransactions',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    exportReports: {
      id: 'f575f246-6089-11f0-bd21-16243d6fb08b',
      name: 'Exporting reports',
      tag: 'exportReports',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    exportWalletBalances: {
      id: 'f575f2a0-6089-11f0-bd21-16243d6fb08b',
      name: 'Exporting wallet balances',
      tag: 'exportWalletBalances',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    addTeamMembers: {
      id: 'f575f32c-6089-11f0-bd21-16243d6fb08b',
      name: 'Adding team members',
      tag: 'addTeamMembers',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    removeTeamMembers: {
      id: 'f575f388-6089-11f0-bd21-16243d6fb08b',
      name: 'Removing team members',
      tag: 'removeTeamMembers',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    viewApiKeys: {
      id: 'f575f408-6089-11f0-bd21-16243d6fb08b',
      name: 'View API Keys',
      tag: 'viewApiKeys',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    viewIpsWebhooks: {
      id: 'f575f475-6089-11f0-bd21-16243d6fb08b',
      name: 'View IPs & Webhooks',
      tag: 'viewIpsWebhooks',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    addBeneficiaries: {
      id: 'f575f4b9-6089-11f0-bd21-16243d6fb08b',
      name: 'Add beneficiaries',
      tag: 'addBeneficiaries',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    },
    approveBeneficiaries: {
      id: 'f575f4fd-6089-11f0-bd21-16243d6fb08b',
      name: 'Approve new beneficiaries',
      tag: 'approveBeneficiaries',
      status: 'active',
      type: 'client',
      roleGroup: null,
      createdAt: '2025-07-14 08:10:09',
      updatedAt: null,
      deletedAt: null
    }
  };
  
  /**
   * Type for the ACCESS_RIGHTS keys
   */
  export type AccessRightTag = keyof typeof ACCESS_RIGHTS;
  
  export const getAccessRightDetails = (tag: AccessRightTag): AccessRight => {
    return ACCESS_RIGHTS[tag];
  };
  
  export const getAllAccessRights = (): AccessRight[] => {
    return Object.values(ACCESS_RIGHTS);
  };
  
  export const getAllAccessRightTags = (): AccessRightTag[] => {
    return Object.keys(ACCESS_RIGHTS) as AccessRightTag[];
  };

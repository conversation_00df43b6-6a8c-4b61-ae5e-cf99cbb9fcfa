import nodemailer from 'nodemailer';
import { Transporter, SendMailOptions } from 'nodemailer';
import AWS from 'aws-sdk';

export default class EmailSender {
    private transporter: Transporter;
    constructor() {
        this.transporter = nodemailer.createTransport({
            host: process.env.SES_SMTP_URL,
            port: Number(process.env.SES_SMTP_PORT),
            secure: false,
            requireTLS: true,
            auth: {
                user: process.env.SES_SMT_USER,
                pass: process.env.SES_SMT_PASSWORD,
            },
            logger: true
        });
    }
 


    
    async sendMail(to: string, subject: string, heading: string, body: string) {

        const html = this.createHtmlEmail(heading, body);
        const ses: any = new AWS.SES({
            apiVersion: '2010-12-01',
            region: process.env.SES_AWS_REGION,
            credentials: {
                accessKeyId: process.env.SES_ACCESS_KEY || "",
                secretAccessKey: process.env.SES_ACCESS_SECRETE || ""
            }
        });

        const params: any = {
            Destination: {
                ToAddresses: [to]
            },
            Message: {
                Body: {
                    Html: {
                        Charset: "UTF-8",
                        Data: html
                    }
                },
                Subject: {
                    Charset: 'UTF-8',
                    Data: subject
                }
            },
            Source: process.env.SES_FROM,
        };

        try {
            const info: any = await ses.sendEmail(params).promise();
            console.log('SES Email sent:', info.MessageId);
            return true;
        } catch (error) {
            console.error('Error sending SES email:', error);
            return false;
        }
    }

    private createHtmlEmail(heading: string, body: string, footer = "The MUDA team"): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; background-color: #f4f4f4; color: #333333; margin: 0; padding: 0; }
                    p{ font-size:14px; }
                    .container { background-color: #ffffff; width: 80%; max-width: 600px; padding: 20px; border: 1px solid #dddddd; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); }
                    .header { background-color: #e6f2ff; color: #333333; padding: 10px; text-align: center; border-bottom: 1px solid #dddddd; }
                    .body { padding: 20px; text-align: left; }
                    .footer { background-color: #e6f2ff; color: #333333; text-align: center; padding: 10px; border-top: 1px solid #dddddd; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">${heading}</div>
                    <div class="body">${body}</div>
                    <div class="footer">${footer}</div>
                </div>
            </body>
            </html>
        `;
    }
}
